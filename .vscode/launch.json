{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in library 'test_rust'",
      "cargo": {
        "args": [
          "test",
          "--no-run",
          "--lib",
          "--package=test-rust"
        ],
        "filter": {
          "name": "test_rust",
          "kind": "lib"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug executable 'test-rust'",
      "cargo": {
        "args": [
          "build",
          "--bin=test-rust",
          "--package=test-rust"
        ],
        "filter": {
          "name": "test-rust",
          "kind": "bin"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in executable 'test-rust'",
      "cargo": {
        "args": [
          "test",
          "--no-run",
          "--bin=test-rust",
          "--package=test-rust"
        ],
        "filter": {
          "name": "test-rust",
          "kind": "bin"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    }
  ]
}
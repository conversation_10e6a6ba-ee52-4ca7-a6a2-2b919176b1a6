[package]
name = "test-rust"
version = "0.1.0"
edition = "2024"

[dependencies]
rand = "0.8.5"
trpl = "0.2.0"
macros_test ={ path = "./macros_test" }
test-lib = { path = "./test-lib" }
singleton = "0.2.2"
ffmpeg-next = "8.0.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4.42", features = ["serde"]}
imageproc = "0.25.0"
image = "0.25.8"
ab_glyph = "0.2.32"
rodio = "0.17"
rayon = "1.11.0"
anyhow = "1.0.100"
fs_extra = "1.3.0"
num_cpus = "1.17.0"
tokio = { version = "1.47.1", features = ["full"] }
futures = "0.3.31"
jwalk = "0.8.1"
walkdir = "2.5.0"
threadpool = "1.8.1"


[build-dependencies]
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
base64 = "0.22.1"
glob = "0.3.3"
walkdir = "2.5.0"
mime_guess = "2.0.5"


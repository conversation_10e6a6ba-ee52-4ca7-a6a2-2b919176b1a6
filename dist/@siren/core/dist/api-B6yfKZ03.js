import{invoke as e}from"./core-DqoKAhCt.js";function t(e,t){return n=>({plugin_id:t===void 0?void 0:e,method:t===void 0?e:t,payload:n})}function n(t){return e(t.method,t.payload)}function r(t){return e(`call_plugin`,{meta:{id:t.plugin_id,method:t.method,payload:t.payload}})}const i=t(`greet`),a=t(`get_lang`),o=t(`get_lang_map`),s=t(`get_settings`),c=t(`get_plugins`),l=t(`update_setting`),u=t(`register_global_shortcut`),d=t(`unregister_global_shortcut`),f=t(`example_plugin`,`greet`);export{t as $invoker,a as getLang,o as getLangMap,c as getPlugins,s as getSettings,i as greetMain,f as greetPlugin,n as invokeMain,r as invokePlugin,u as registerGlobalShortcut,d as unregisterGlobalShortcut,l as updateSetting};
//# sourceMappingURL=api-B6yfKZ03.js.map
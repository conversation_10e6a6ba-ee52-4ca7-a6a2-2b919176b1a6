import"../core-DqoKAhCt.js";import{$invoker as e,getLang as t,getLangMap as n,getPlugins as r,getSettings as i,greetMain as a,greetPlugin as o,invokeMain as s,invokePlugin as c,registerGlobalShortcut as l,unregisterGlobalShortcut as u,updateSetting as d}from"../api-B6yfKZ03.js";export{e as $invoker,t as getLang,n as getLangMap,r as getPlugins,i as getSettings,a as greetMain,o as greetPlugin,s as invokeMain,c as invokePlugin,l as registerGlobalShortcut,u as unregisterGlobalShortcut,d as updateSetting};
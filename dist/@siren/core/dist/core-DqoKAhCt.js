function e(e,t,n,r){if(n===`a`&&!r)throw TypeError(`Private accessor was defined without a getter`);if(typeof t==`function`?e!==t||!r:!t.has(e))throw TypeError(`Cannot read private member from an object whose class did not declare it`);return n===`m`?r:n===`a`?r.call(e):r?r.value:t.get(e)}function t(e,t,n,r,i){if(r===`m`)throw TypeError(`Private method is not writable`);if(r===`a`&&!i)throw TypeError(`Private accessor was defined without a setter`);if(typeof t==`function`?e!==t||!i:!t.has(e))throw TypeError(`Cannot write private member to an object whose class did not declare it`);return r===`a`?i.call(e,n):i?i.value=n:t.set(e,n),n}var n,r,i,a,o;const s=`__TAURI_TO_IPC_KEY__`;function c(e,t=!1){return window.__TAURI_INTERNALS__.transformCallback(e,t)}var l=class{constructor(o){n.set(this,void 0),r.set(this,0),i.set(this,[]),a.set(this,void 0),t(this,n,o||(()=>{}),`f`),this.id=c(o=>{let s=o.index;if(`end`in o){s==e(this,r,`f`)?this.cleanupCallback():t(this,a,s,`f`);return}let c=o.message;if(s==e(this,r,`f`)){for(e(this,n,`f`).call(this,c),t(this,r,e(this,r,`f`)+1,`f`);e(this,r,`f`)in e(this,i,`f`);){let a=e(this,i,`f`)[e(this,r,`f`)];e(this,n,`f`).call(this,a),delete e(this,i,`f`)[e(this,r,`f`)],t(this,r,e(this,r,`f`)+1,`f`)}e(this,r,`f`)===e(this,a,`f`)&&this.cleanupCallback()}else e(this,i,`f`)[s]=c})}cleanupCallback(){window.__TAURI_INTERNALS__.unregisterCallback(this.id)}set onmessage(e){t(this,n,e,`f`)}get onmessage(){return e(this,n,`f`)}[(n=new WeakMap,r=new WeakMap,i=new WeakMap,a=new WeakMap,s)](){return`__CHANNEL__:${this.id}`}toJSON(){return this[s]()}},u=class{constructor(e,t,n){this.plugin=e,this.event=t,this.channelId=n}async unregister(){return m(`plugin:${this.plugin}|remove_listener`,{event:this.event,channelId:this.channelId})}};async function d(e,t,n){let r=new l(n);return m(`plugin:${e}|registerListener`,{event:t,handler:r}).then(()=>new u(e,t,r.id))}async function f(e){return m(`plugin:${e}|check_permissions`)}async function p(e){return m(`plugin:${e}|request_permissions`)}async function m(e,t={},n){return window.__TAURI_INTERNALS__.invoke(e,t,n)}function h(e,t=`asset`){return window.__TAURI_INTERNALS__.convertFileSrc(e,t)}var g=class{get rid(){return e(this,o,`f`)}constructor(e){o.set(this,void 0),t(this,o,e,`f`)}async close(){return m(`plugin:resources|close`,{rid:this.rid})}};o=new WeakMap;function _(){return!!(globalThis||window).isTauri}export{l as Channel,u as PluginListener,g as Resource,s as SERIALIZE_TO_IPC_FN,d as addPluginListener,f as checkPermissions,h as convertFileSrc,m as invoke,_ as isTauri,p as requestPermissions,c as transformCallback};
//# sourceMappingURL=core-DqoKAhCt.js.map
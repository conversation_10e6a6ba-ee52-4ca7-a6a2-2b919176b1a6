import"./core-DqoKAhCt.js";import{platform as e}from"./tauri-Cxf-KRk8.js";globalThis.garbage||(globalThis.garbage=new WeakMap);var t=class{_toDispose=new Set;constructor(){}dispose(){globalThis.garbage.set(this,this.identifier);for(let e of this._toDispose.values())e.dispose();this._toDispose.clear()}_register(e){if(e==this)throw Error(`Cannot register a disposable on itself`);this._toDispose.add(e)}get identifier(){return``}};function n(e,t){let n=e.findIndex(e=>e===t);n>=0&&e.splice(n,1)}var r=class extends t{_eventListeners=new Map;constructor(){super()}fire(e,t){let r=this._eventListeners.get(e);r&&r.slice().forEach(e=>{e.func(t),e.once&&n(r,e)})}on(e,t){let r={func:t,dispose:()=>{let t=this._eventListeners.get(e);t&&n(t,r)}};return this._eventListeners.has(e)?this._eventListeners.get(e).push(r):this._eventListeners.set(e,[r]),r}once(e,t){let r={func:t,once:!0,dispose:()=>{let t=this._eventListeners.get(e);t&&n(t,r)}};return this._eventListeners.has(e)?this._eventListeners.get(e).push(r):this._eventListeners.set(e,[r]),r}dispose(){super.dispose(),this._eventListeners.clear()}};const i=new WeakMap;var a=class extends r{ctx;isReady=!1;initContext(e){this.ctx=e}async start(){}ready(e=!0){this.isReady=e,this.fire(`ready`,e)}async waitReady(){if(!this.isReady)return new Promise(e=>{this.once(`ready`,t=>{t&&e()})})}};function o(e,t){let n=i.get(e);if(n)return n;let r=new e(t);return i.set(e,r),r}function s(e){return i.has(e)}var c=class t{showModifiers=[];modifiers=[];actualKey=``;constructor(e,t,n,r,i,a){this.altKey=e,this.ctrlKey=t,this.shiftKey=n,this.metaKey=r,this.key=i,this.code=a,i!==`Control`&&i!==`Shift`&&i!==`Alt`&&i!==`Meta`&&i!==`Dead`&&i!==`Unidentified`?this.actualKey=i:this.actualKey=``,r&&(this.showModifiers.push(`Meta`),this.modifiers.push(`Super`)),t&&(this.showModifiers.push(`Ctrl`),this.modifiers.push(`Ctrl`)),e&&(this.showModifiers.push(`Alt`),this.modifiers.push(`Alt`)),n&&(this.showModifiers.push(`Shift`),this.modifiers.push(`Shift`))}static from(e){return new t(e.altKey,e.ctrlKey,e.shiftKey,e.metaKey,e.key,e.code)}static fromStr(e){let n=e.split(`+`).map(e=>e.toLowerCase()),r=n.includes(`alt`)||n.includes(`option`),i=n.includes(`ctrl`)||n.includes(`control`),a=n.includes(`shift`),o=n.includes(`meta`)||n.includes(`command`)||n.includes(`win`)||n.includes(`super`)||n.includes(`cmd`),s=n.pop();return new t(r,i,a,o,s,s)}get formattedKey(){return this.actualKey.length===1?this.actualKey.toUpperCase():this.actualKey}display(){if(e()===`macos`){let e=``;return this.metaKey&&(e+=l.command),this.ctrlKey&&(e+=l.control),this.altKey&&(e+=l.option),this.shiftKey&&(e+=l.shift),e+=this.key.toUpperCase(),e}return this.show()}toString(){return this.modifiers.length?this.modifiers.join(`+`)+`+`+this.code:this.code}show(){return this.showModifiers.length?this.showModifiers.join(`+`)+`+`+this.formattedKey:this.formattedKey}get isValid(){return!!this.actualKey}toType(){return{altKey:this.altKey,ctrlKey:this.ctrlKey,metaKey:this.metaKey,shiftKey:this.shiftKey,key:this.key,code:this.code}}};const l={command:`⌘`,option:`⌥`,shift:`⇧`,control:`⌃`,enter:`⏎`,backspace:`⌫`,tab:`⇥`,space:`␣`,up:`↑`,down:`↓`,left:`←`,right:`→`,esc:`esc`,delete:`⌦`,pageup:`⇞`,pagedown:`⇟`,home:`↖`,end:`↘`,capslock:`⇪`,numlock:`⇭`,printscreen:`Print`,scrolllock:`Scroll`,pause:`Pause`,insert:`Ins`};var u=[[{id:`retry`,name:`Retry`,icon:`xx`},{id:`cancel`,name:`Cancel`,shortcut:c.fromStr(`Command+W`)}]];const d={"processing-task-item":``,"completed-task-item":{}};var f=class extends a{contextMenus={"completed-task-item":[],"processing-task-item":[]};async init(){return this.contextMenus[`processing-task-item`]=u,this.contextMenus[`completed-task-item`]=[],[`processing-task-item`,`completed-task-item`]}get(e){return this.contextMenus[e]}},p=class extends a{constructor(){super()}do(){}},m=class{constructor(e,t){this._manifest=e,this._context=t}get id(){return this.manifest.id}get manifest(){return this._manifest}get context(){return this._context}onActivate(){console.log(`Base Plugin activated`)}onDeactivate(){console.log(`Base Plugin deactivated`)}onInstall(){console.log(`Base Plugin installed`)}onUninstall(){console.log(`Base Plugin uninstalled`)}onUpdate(){console.log(`Base Plugin updated`)}},h=class extends a{_pluginResolves=new Map;_plugins=new Map;_dev_overridePlugin=null;constructor(){super()}setOverridePlugin(e){this._dev_overridePlugin=e}get dev_overridePlugin(){return this._dev_overridePlugin}async register(e){let t=new g(e);this._pluginResolves.set(e.id,t);try{let e=await t.resolve(!!this.dev_overridePlugin);return e.onActivate(),e}catch(e){console.error(`error in register: `,e)}return null}async registerAll(e){return(await Promise.all(e.map(e=>this.register(e)))).filter(e=>e)}async resolve(e){let t=this._plugins.get(e);if(!t){let t=this._pluginResolves.get(e);if(!t)throw Error(`Plugin ${e} not found`);let n=await t.resolve();return this._plugins.set(e,n),n}return t}},g=class{_modulePath;constructor(e){this._manifest=e;let t=new Blob([e.js],{type:`application/javascript`});this._modulePath=URL.createObjectURL(t)}async resolve(e){let t;return t=e?await o(h).dev_overridePlugin?.(this._manifest):await import(this._modulePath).then(e=>e.default),new t(this._manifest,o(p))}get manifest(){return this._manifest}get modulePath(){return this._modulePath}};let _=function(e){return e[e.Main=0]=`Main`,e[e.AddTask=1]=`AddTask`,e}({});var v=class extends a{_callbacks=new Map;hook(e,t,n){this._callbacks.has(e)||this._callbacks.set(e,new Map);let r=this._callbacks.get(e);r?.has(t)?console.warn(`Render callback ${t} already registered`):r?.set(t,n)}async trigger(e,t,n){return this._callbacks.get(e)?.get(t)?.(n)}};export{m as BasePlugin,f as ContextMenuManager,t as Disposable,r as Emitter,c as KeyBinding,l as MacOSEmojiMap,a as Manager,p as PluginContext,h as PluginManager,g as PluginResolveImpl,d as RegisteredContextMenu,_ as RenderEvent,v as RenderManager,o as getManager,s as hasManager};
//# sourceMappingURL=index.js.map
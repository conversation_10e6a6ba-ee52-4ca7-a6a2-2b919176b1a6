import{getSettings as e,invokeMain as t,updateSetting as n}from"./api-B6yfKZ03.js";import{defineStore as r}from"pinia";import{computed as i,ref as a}from"vue";const o=r(`app`,()=>{let e=a(``),t=a(``);return{name:e,version:t,setAppName:t=>{e.value=t},setAppVersion:e=>{t.value=e}}}),s=r(`settings`,()=>{let r=a({}),o=a(!1),s=a(``),c=i(()=>Object.values(r.value).sort((e,t)=>e.order-t.order)),l=i(()=>Object.keys(r.value)),u=i(()=>Object.keys(r.value).length),d=i(()=>r.value),f=i(()=>c.value.filter(e=>e.type===`category`&&!e.parent_id).map(e=>e.id)),p=i(()=>{let e=`general.theme`;return r.value[e]?r.value[e].value||`system`:localStorage.getItem(e)||`system`}),m=i(()=>{let e=`general.language`,t=``;if(!r.value[e])t=localStorage.getItem(e)||`system`;else{let n=r.value[e];t=n.value||n.default_value}return t&&t!==`system`?t:navigator.language||navigator.languages[0]||`zh`}),h=(e,t)=>{let n=e;t&&(n=`plugin.${t}.${e}`);let i=r.value[n];return!i||i.type===`group`||i.type===`category`?null:i.value===null?i.default_value:i.value};return{settings:r,settingsLoaded:o,currentSettingRootId:s,settingsArr:c,settingIds:l,selectSettingsTotal:u,selectSettingEntities:d,rootSettingIds:f,theme:p,langCode:m,getSettingValue:h,setCurrentSettingRootId:e=>{s.value=e},fetchSettings:async()=>{if(o.value)return;let n=await t(e());n.forEach(e=>{e.type!==`category`&&e.type!==`group`&&e.client_side&&(e.value=localStorage.getItem(e.id)||e.value)}),console.log(`settingsData`,n);let i={};n.forEach(e=>{i[e.id]=e}),r.value=i,o.value=!0,l.value.length&&(s.value=l.value.includes(`general`)?`general`:l.value[0]);let a=r.value[`general.omnibar.enable`],c=a.value==null||a.value===void 0?a.default_value:a.value;r.value[`general.omnibar.shortcut`]&&(r.value[`general.omnibar.shortcut`].disabled=!c)},doUpdateSetting:async({id:e,value:i,recursive:a})=>{let o=await t(n({id:e,value:i,recursive:a||!1}));!o||o.length===0||o.forEach(e=>{let t=r.value[e.id];if(t.client_side&&(e.value==null||e.value===void 0?localStorage.removeItem(e.id):localStorage.setItem(e.id,e.value)),t&&(t.value=e.value),e.id===`general.omnibar.enable`){let e=h(`general.omnibar.enable`);r.value[`general.omnibar.shortcut`]&&(r.value[`general.omnibar.shortcut`].disabled=!e)}})}}});export{o as useAppStore,s as useSettingsStore};
//# sourceMappingURL=settings-Ce9F8p1y.js.map
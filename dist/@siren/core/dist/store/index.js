import"../core-DqoKAhCt.js";import"../api-B6yfKZ03.js";import{useAppStore as e,useSettingsStore as t}from"../settings-Ce9F8p1y.js";import{defineStore as n}from"pinia";import{computed as r,ref as i}from"vue";let a=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce((e,t)=>(t&=63,t<36?e+=t.toString(36):t<62?e+=(t-26).toString(36).toUpperCase():t>62?e+=`-`:e+=`_`,e),``);const o=n(`contextmenu`,()=>{let e=i({});return{state:e,makeChange:t=>{let n=a();t?Array.isArray(t)?t.forEach(t=>{e.value[t]=n}):e.value[t]=n:Object.keys(e.value).forEach(t=>{e.value[t]=n})}}}),s=n(`plugin`,()=>{let e=i(new Map),t=i([]),n=n=>{let r=new Map,i=[];n.forEach(e=>{r.set(e.id,e),i.push(e.id)}),e.value=r,t.value=i},a=r(()=>Array.from(e.value.values())),o=r(()=>t.value),s=r(()=>e.value.size),c=r(()=>Object.fromEntries(e.value));return{pluginsMap:e,pluginsIds:t,registerAll:n,allPlugins:a,selectPluginIds:o,selectPluginTotal:s,selectPluginEntities:c}}),c=n(`task`,()=>{let e=i(!1),t=i(!1),n=i(!1);return{inEdit:e,inDetail:t,inFilter:n,setInEdit:t=>{e.value=t},setInDetail:e=>{t.value=e},setInFilter:e=>{n.value=e}}}),l=n(`counter`,()=>{let e=i(0),t=i(`Eduardo`),n=r(()=>e.value*2);function a(){e.value++}return{count:e,name:t,doubleCount:n,increment:a}});export{e as useAppStore,o as useContextMenuStore,l as useCounterStore,s as usePluginStore,t as useSettingsStore,c as useTaskStore};
//# sourceMappingURL=index.js.map
import{Resource as e,SERIALIZE_TO_IPC_FN as t,invoke as n,transformCallback as r}from"./core-DqoKAhCt.js";var i=class{constructor(...e){this.type=`Logical`,e.length===1?`Logical`in e[0]?(this.width=e[0].Logical.width,this.height=e[0].Logical.height):(this.width=e[0].width,this.height=e[0].height):(this.width=e[0],this.height=e[1])}toPhysical(e){return new a(this.width*e,this.height*e)}[t](){return{width:this.width,height:this.height}}toJSON(){return this[t]()}},a=class{constructor(...e){this.type=`Physical`,e.length===1?`Physical`in e[0]?(this.width=e[0].Physical.width,this.height=e[0].Physical.height):(this.width=e[0].width,this.height=e[0].height):(this.width=e[0],this.height=e[1])}toLogical(e){return new i(this.width/e,this.height/e)}[t](){return{width:this.width,height:this.height}}toJSON(){return this[t]()}},o=class{constructor(e){this.size=e}toLogical(e){return this.size instanceof i?this.size:this.size.toLogical(e)}toPhysical(e){return this.size instanceof a?this.size:this.size.toPhysical(e)}[t](){return{[`${this.size.type}`]:{width:this.size.width,height:this.size.height}}}toJSON(){return this[t]()}},s=class{constructor(...e){this.type=`Logical`,e.length===1?`Logical`in e[0]?(this.x=e[0].Logical.x,this.y=e[0].Logical.y):(this.x=e[0].x,this.y=e[0].y):(this.x=e[0],this.y=e[1])}toPhysical(e){return new c(this.x*e,this.y*e)}[t](){return{x:this.x,y:this.y}}toJSON(){return this[t]()}},c=class{constructor(...e){this.type=`Physical`,e.length===1?`Physical`in e[0]?(this.x=e[0].Physical.x,this.y=e[0].Physical.y):(this.x=e[0].x,this.y=e[0].y):(this.x=e[0],this.y=e[1])}toLogical(e){return new s(this.x/e,this.y/e)}[t](){return{x:this.x,y:this.y}}toJSON(){return this[t]()}},l=class{constructor(e){this.position=e}toLogical(e){return this.position instanceof s?this.position:this.position.toLogical(e)}toPhysical(e){return this.position instanceof c?this.position:this.position.toPhysical(e)}[t](){return{[`${this.position.type}`]:{x:this.position.x,y:this.position.y}}}toJSON(){return this[t]()}},u;(function(e){e.WINDOW_RESIZED=`tauri://resize`,e.WINDOW_MOVED=`tauri://move`,e.WINDOW_CLOSE_REQUESTED=`tauri://close-requested`,e.WINDOW_DESTROYED=`tauri://destroyed`,e.WINDOW_FOCUS=`tauri://focus`,e.WINDOW_BLUR=`tauri://blur`,e.WINDOW_SCALE_FACTOR_CHANGED=`tauri://scale-change`,e.WINDOW_THEME_CHANGED=`tauri://theme-changed`,e.WINDOW_CREATED=`tauri://window-created`,e.WEBVIEW_CREATED=`tauri://webview-created`,e.DRAG_ENTER=`tauri://drag-enter`,e.DRAG_OVER=`tauri://drag-over`,e.DRAG_DROP=`tauri://drag-drop`,e.DRAG_LEAVE=`tauri://drag-leave`})(u||={});async function d(e,t){window.__TAURI_EVENT_PLUGIN_INTERNALS__.unregisterListener(e,t),await n(`plugin:event|unlisten`,{event:e,eventId:t})}async function f(e,t,i){let a=typeof i?.target==`string`?{kind:`AnyLabel`,label:i.target}:i?.target??{kind:`Any`};return n(`plugin:event|listen`,{event:e,target:a,handler:r(t)}).then(t=>async()=>d(e,t))}async function p(e,t,n){return f(e,n=>{d(e,n.id),t(n)},n)}async function m(e,t){await n(`plugin:event|emit`,{event:e,payload:t})}async function h(e,t,r){await n(`plugin:event|emit_to`,{target:typeof e==`string`?{kind:`AnyLabel`,label:e}:e,event:t,payload:r})}var g=class t extends e{constructor(e){super(e)}static async new(e,r,i){return n(`plugin:image|new`,{rgba:_(e),width:r,height:i}).then(e=>new t(e))}static async fromBytes(e){return n(`plugin:image|from_bytes`,{bytes:_(e)}).then(e=>new t(e))}static async fromPath(e){return n(`plugin:image|from_path`,{path:e}).then(e=>new t(e))}async rgba(){return n(`plugin:image|rgba`,{rid:this.rid}).then(e=>new Uint8Array(e))}async size(){return n(`plugin:image|size`,{rid:this.rid})}};function _(e){return e==null?null:typeof e==`string`?e:e instanceof g?e.rid:e}var v;(function(e){e[e.Critical=1]=`Critical`,e[e.Informational=2]=`Informational`})(v||={});var y=class{constructor(e){this._preventDefault=!1,this.event=e.event,this.id=e.id}preventDefault(){this._preventDefault=!0}isPreventDefault(){return this._preventDefault}},b;(function(e){e.None=`none`,e.Normal=`normal`,e.Indeterminate=`indeterminate`,e.Paused=`paused`,e.Error=`error`})(b||={});function x(){return new w(window.__TAURI_INTERNALS__.metadata.currentWindow.label,{skip:!0})}async function S(){return n(`plugin:window|get_all_windows`).then(e=>e.map(e=>new w(e,{skip:!0})))}const C=[`tauri://created`,`tauri://error`];var w=class{constructor(e,t={}){this.label=e,this.listeners=Object.create(null),t?.skip||n(`plugin:window|create`,{options:{...t,parent:typeof t.parent==`string`?t.parent:t.parent?.label,label:e}}).then(async()=>this.emit(`tauri://created`)).catch(async e=>this.emit(`tauri://error`,e))}static async getByLabel(e){return(await S()).find(t=>t.label===e)??null}static getCurrent(){return x()}static async getAll(){return S()}static async getFocusedWindow(){for(let e of await S())if(await e.isFocused())return e;return null}async listen(e,t){return this._handleTauriEvent(e,t)?()=>{let n=this.listeners[e];n.splice(n.indexOf(t),1)}:f(e,t,{target:{kind:`Window`,label:this.label}})}async once(e,t){return this._handleTauriEvent(e,t)?()=>{let n=this.listeners[e];n.splice(n.indexOf(t),1)}:p(e,t,{target:{kind:`Window`,label:this.label}})}async emit(e,t){if(C.includes(e)){for(let n of this.listeners[e]||[])n({event:e,id:-1,payload:t});return}return m(e,t)}async emitTo(e,t,n){if(C.includes(t)){for(let e of this.listeners[t]||[])e({event:t,id:-1,payload:n});return}return h(e,t,n)}_handleTauriEvent(e,t){return C.includes(e)?(e in this.listeners?this.listeners[e].push(t):this.listeners[e]=[t],!0):!1}async scaleFactor(){return n(`plugin:window|scale_factor`,{label:this.label})}async innerPosition(){return n(`plugin:window|inner_position`,{label:this.label}).then(e=>new c(e))}async outerPosition(){return n(`plugin:window|outer_position`,{label:this.label}).then(e=>new c(e))}async innerSize(){return n(`plugin:window|inner_size`,{label:this.label}).then(e=>new a(e))}async outerSize(){return n(`plugin:window|outer_size`,{label:this.label}).then(e=>new a(e))}async isFullscreen(){return n(`plugin:window|is_fullscreen`,{label:this.label})}async isMinimized(){return n(`plugin:window|is_minimized`,{label:this.label})}async isMaximized(){return n(`plugin:window|is_maximized`,{label:this.label})}async isFocused(){return n(`plugin:window|is_focused`,{label:this.label})}async isDecorated(){return n(`plugin:window|is_decorated`,{label:this.label})}async isResizable(){return n(`plugin:window|is_resizable`,{label:this.label})}async isMaximizable(){return n(`plugin:window|is_maximizable`,{label:this.label})}async isMinimizable(){return n(`plugin:window|is_minimizable`,{label:this.label})}async isClosable(){return n(`plugin:window|is_closable`,{label:this.label})}async isVisible(){return n(`plugin:window|is_visible`,{label:this.label})}async title(){return n(`plugin:window|title`,{label:this.label})}async theme(){return n(`plugin:window|theme`,{label:this.label})}async isAlwaysOnTop(){return n(`plugin:window|is_always_on_top`,{label:this.label})}async center(){return n(`plugin:window|center`,{label:this.label})}async requestUserAttention(e){let t=null;return e&&(t=e===v.Critical?{type:`Critical`}:{type:`Informational`}),n(`plugin:window|request_user_attention`,{label:this.label,value:t})}async setResizable(e){return n(`plugin:window|set_resizable`,{label:this.label,value:e})}async setEnabled(e){return n(`plugin:window|set_enabled`,{label:this.label,value:e})}async isEnabled(){return n(`plugin:window|is_enabled`,{label:this.label})}async setMaximizable(e){return n(`plugin:window|set_maximizable`,{label:this.label,value:e})}async setMinimizable(e){return n(`plugin:window|set_minimizable`,{label:this.label,value:e})}async setClosable(e){return n(`plugin:window|set_closable`,{label:this.label,value:e})}async setTitle(e){return n(`plugin:window|set_title`,{label:this.label,value:e})}async maximize(){return n(`plugin:window|maximize`,{label:this.label})}async unmaximize(){return n(`plugin:window|unmaximize`,{label:this.label})}async toggleMaximize(){return n(`plugin:window|toggle_maximize`,{label:this.label})}async minimize(){return n(`plugin:window|minimize`,{label:this.label})}async unminimize(){return n(`plugin:window|unminimize`,{label:this.label})}async show(){return n(`plugin:window|show`,{label:this.label})}async hide(){return n(`plugin:window|hide`,{label:this.label})}async close(){return n(`plugin:window|close`,{label:this.label})}async destroy(){return n(`plugin:window|destroy`,{label:this.label})}async setDecorations(e){return n(`plugin:window|set_decorations`,{label:this.label,value:e})}async setShadow(e){return n(`plugin:window|set_shadow`,{label:this.label,value:e})}async setEffects(e){return n(`plugin:window|set_effects`,{label:this.label,value:e})}async clearEffects(){return n(`plugin:window|set_effects`,{label:this.label,value:null})}async setAlwaysOnTop(e){return n(`plugin:window|set_always_on_top`,{label:this.label,value:e})}async setAlwaysOnBottom(e){return n(`plugin:window|set_always_on_bottom`,{label:this.label,value:e})}async setContentProtected(e){return n(`plugin:window|set_content_protected`,{label:this.label,value:e})}async setSize(e){return n(`plugin:window|set_size`,{label:this.label,value:e instanceof o?e:new o(e)})}async setMinSize(e){return n(`plugin:window|set_min_size`,{label:this.label,value:e instanceof o?e:e?new o(e):null})}async setMaxSize(e){return n(`plugin:window|set_max_size`,{label:this.label,value:e instanceof o?e:e?new o(e):null})}async setSizeConstraints(e){function t(e){return e?{Logical:e}:null}return n(`plugin:window|set_size_constraints`,{label:this.label,value:{minWidth:t(e?.minWidth),minHeight:t(e?.minHeight),maxWidth:t(e?.maxWidth),maxHeight:t(e?.maxHeight)}})}async setPosition(e){return n(`plugin:window|set_position`,{label:this.label,value:e instanceof l?e:new l(e)})}async setFullscreen(e){return n(`plugin:window|set_fullscreen`,{label:this.label,value:e})}async setSimpleFullscreen(e){return n(`plugin:window|set_simple_fullscreen`,{label:this.label,value:e})}async setFocus(){return n(`plugin:window|set_focus`,{label:this.label})}async setFocusable(e){return n(`plugin:window|set_focusable`,{label:this.label,value:e})}async setIcon(e){return n(`plugin:window|set_icon`,{label:this.label,value:_(e)})}async setSkipTaskbar(e){return n(`plugin:window|set_skip_taskbar`,{label:this.label,value:e})}async setCursorGrab(e){return n(`plugin:window|set_cursor_grab`,{label:this.label,value:e})}async setCursorVisible(e){return n(`plugin:window|set_cursor_visible`,{label:this.label,value:e})}async setCursorIcon(e){return n(`plugin:window|set_cursor_icon`,{label:this.label,value:e})}async setBackgroundColor(e){return n(`plugin:window|set_background_color`,{color:e})}async setCursorPosition(e){return n(`plugin:window|set_cursor_position`,{label:this.label,value:e instanceof l?e:new l(e)})}async setIgnoreCursorEvents(e){return n(`plugin:window|set_ignore_cursor_events`,{label:this.label,value:e})}async startDragging(){return n(`plugin:window|start_dragging`,{label:this.label})}async startResizeDragging(e){return n(`plugin:window|start_resize_dragging`,{label:this.label,value:e})}async setBadgeCount(e){return n(`plugin:window|set_badge_count`,{label:this.label,value:e})}async setBadgeLabel(e){return n(`plugin:window|set_badge_label`,{label:this.label,value:e})}async setOverlayIcon(e){return n(`plugin:window|set_overlay_icon`,{label:this.label,value:e?_(e):void 0})}async setProgressBar(e){return n(`plugin:window|set_progress_bar`,{label:this.label,value:e})}async setVisibleOnAllWorkspaces(e){return n(`plugin:window|set_visible_on_all_workspaces`,{label:this.label,value:e})}async setTitleBarStyle(e){return n(`plugin:window|set_title_bar_style`,{label:this.label,value:e})}async setTheme(e){return n(`plugin:window|set_theme`,{label:this.label,value:e})}async onResized(e){return this.listen(u.WINDOW_RESIZED,t=>{t.payload=new a(t.payload),e(t)})}async onMoved(e){return this.listen(u.WINDOW_MOVED,t=>{t.payload=new c(t.payload),e(t)})}async onCloseRequested(e){return this.listen(u.WINDOW_CLOSE_REQUESTED,async t=>{let n=new y(t);await e(n),n.isPreventDefault()||await this.destroy()})}async onDragDropEvent(e){let t=await this.listen(u.DRAG_ENTER,t=>{e({...t,payload:{type:`enter`,paths:t.payload.paths,position:new c(t.payload.position)}})}),n=await this.listen(u.DRAG_OVER,t=>{e({...t,payload:{type:`over`,position:new c(t.payload.position)}})}),r=await this.listen(u.DRAG_DROP,t=>{e({...t,payload:{type:`drop`,paths:t.payload.paths,position:new c(t.payload.position)}})}),i=await this.listen(u.DRAG_LEAVE,t=>{e({...t,payload:{type:`leave`}})});return()=>{t(),r(),n(),i()}}async onFocusChanged(e){let t=await this.listen(u.WINDOW_FOCUS,t=>{e({...t,payload:!0})}),n=await this.listen(u.WINDOW_BLUR,t=>{e({...t,payload:!1})});return()=>{t(),n()}}async onScaleChanged(e){return this.listen(u.WINDOW_SCALE_FACTOR_CHANGED,e)}async onThemeChanged(e){return this.listen(u.WINDOW_THEME_CHANGED,e)}},T;(function(e){e.Disabled=`disabled`,e.Throttle=`throttle`,e.Suspend=`suspend`})(T||={});var E;(function(e){e.AppearanceBased=`appearanceBased`,e.Light=`light`,e.Dark=`dark`,e.MediumLight=`mediumLight`,e.UltraDark=`ultraDark`,e.Titlebar=`titlebar`,e.Selection=`selection`,e.Menu=`menu`,e.Popover=`popover`,e.Sidebar=`sidebar`,e.HeaderView=`headerView`,e.Sheet=`sheet`,e.WindowBackground=`windowBackground`,e.HudWindow=`hudWindow`,e.FullScreenUI=`fullScreenUI`,e.Tooltip=`tooltip`,e.ContentBackground=`contentBackground`,e.UnderWindowBackground=`underWindowBackground`,e.UnderPageBackground=`underPageBackground`,e.Mica=`mica`,e.Blur=`blur`,e.Acrylic=`acrylic`,e.Tabbed=`tabbed`,e.TabbedDark=`tabbedDark`,e.TabbedLight=`tabbedLight`})(E||={});var D;(function(e){e.FollowsWindowActiveState=`followsWindowActiveState`,e.Active=`active`,e.Inactive=`inactive`})(D||={});function O(e){return e===null?null:{name:e.name,scaleFactor:e.scaleFactor,position:new c(e.position),size:new a(e.size),workArea:{position:new c(e.workArea.position),size:new a(e.workArea.size)}}}async function k(){return n(`plugin:window|current_monitor`).then(O)}async function A(){return n(`plugin:window|primary_monitor`).then(O)}async function j(e,t){return n(`plugin:window|monitor_from_point`,{x:e,y:t}).then(O)}async function M(){return n(`plugin:window|available_monitors`).then(e=>e.map(O))}async function N(){return n(`plugin:window|cursor_position`).then(e=>new c(e))}function P(){return new L(x(),window.__TAURI_INTERNALS__.metadata.currentWebview.label,{skip:!0})}async function F(){return n(`plugin:webview|get_all_webviews`).then(e=>e.map(e=>new L(new w(e.windowLabel,{skip:!0}),e.label,{skip:!0})))}const I=[`tauri://created`,`tauri://error`];var L=class{constructor(e,t,r){this.window=e,this.label=t,this.listeners=Object.create(null),r?.skip||n(`plugin:webview|create_webview`,{windowLabel:e.label,options:{...r,label:t}}).then(async()=>this.emit(`tauri://created`)).catch(async e=>this.emit(`tauri://error`,e))}static async getByLabel(e){return(await F()).find(t=>t.label===e)??null}static getCurrent(){return P()}static async getAll(){return F()}async listen(e,t){return this._handleTauriEvent(e,t)?()=>{let n=this.listeners[e];n.splice(n.indexOf(t),1)}:f(e,t,{target:{kind:`Webview`,label:this.label}})}async once(e,t){return this._handleTauriEvent(e,t)?()=>{let n=this.listeners[e];n.splice(n.indexOf(t),1)}:p(e,t,{target:{kind:`Webview`,label:this.label}})}async emit(e,t){if(I.includes(e)){for(let n of this.listeners[e]||[])n({event:e,id:-1,payload:t});return}return m(e,t)}async emitTo(e,t,n){if(I.includes(t)){for(let e of this.listeners[t]||[])e({event:t,id:-1,payload:n});return}return h(e,t,n)}_handleTauriEvent(e,t){return I.includes(e)?(e in this.listeners?this.listeners[e].push(t):this.listeners[e]=[t],!0):!1}async position(){return n(`plugin:webview|webview_position`,{label:this.label}).then(e=>new c(e))}async size(){return n(`plugin:webview|webview_size`,{label:this.label}).then(e=>new a(e))}async close(){return n(`plugin:webview|webview_close`,{label:this.label})}async setSize(e){return n(`plugin:webview|set_webview_size`,{label:this.label,value:e instanceof o?e:new o(e)})}async setPosition(e){return n(`plugin:webview|set_webview_position`,{label:this.label,value:e instanceof l?e:new l(e)})}async setFocus(){return n(`plugin:webview|set_webview_focus`,{label:this.label})}async setAutoResize(e){return n(`plugin:webview|set_webview_auto_resize`,{label:this.label,value:e})}async hide(){return n(`plugin:webview|webview_hide`,{label:this.label})}async show(){return n(`plugin:webview|webview_show`,{label:this.label})}async setZoom(e){return n(`plugin:webview|set_webview_zoom`,{label:this.label,value:e})}async reparent(e){return n(`plugin:webview|reparent`,{label:this.label,window:typeof e==`string`?e:e.label})}async clearAllBrowsingData(){return n(`plugin:webview|clear_all_browsing_data`)}async setBackgroundColor(e){return n(`plugin:webview|set_webview_background_color`,{color:e})}async onDragDropEvent(e){let t=await this.listen(u.DRAG_ENTER,t=>{e({...t,payload:{type:`enter`,paths:t.payload.paths,position:new c(t.payload.position)}})}),n=await this.listen(u.DRAG_OVER,t=>{e({...t,payload:{type:`over`,position:new c(t.payload.position)}})}),r=await this.listen(u.DRAG_DROP,t=>{e({...t,payload:{type:`drop`,paths:t.payload.paths,position:new c(t.payload.position)}})}),i=await this.listen(u.DRAG_LEAVE,t=>{e({...t,payload:{type:`leave`}})});return()=>{t(),r(),n(),i()}}};function R(){let e=P();return new B(e.label,{skip:!0})}async function z(){return n(`plugin:window|get_all_windows`).then(e=>e.map(e=>new B(e,{skip:!0})))}var B=class e{constructor(e,t={}){this.label=e,this.listeners=Object.create(null),t?.skip||n(`plugin:webview|create_webview_window`,{options:{...t,parent:typeof t.parent==`string`?t.parent:t.parent?.label,label:e}}).then(async()=>this.emit(`tauri://created`)).catch(async e=>this.emit(`tauri://error`,e))}static async getByLabel(t){let n=(await z()).find(e=>e.label===t)??null;return n?new e(n.label,{skip:!0}):null}static getCurrent(){return R()}static async getAll(){return z()}async listen(e,t){return this._handleTauriEvent(e,t)?()=>{let n=this.listeners[e];n.splice(n.indexOf(t),1)}:f(e,t,{target:{kind:`WebviewWindow`,label:this.label}})}async once(e,t){return this._handleTauriEvent(e,t)?()=>{let n=this.listeners[e];n.splice(n.indexOf(t),1)}:p(e,t,{target:{kind:`WebviewWindow`,label:this.label}})}async setBackgroundColor(e){return n(`plugin:window|set_background_color`,{color:e}).then(()=>n(`plugin:webview|set_webview_background_color`,{color:e}))}};V(B,[w,L]);function V(e,t){(Array.isArray(t)?t:[t]).forEach(t=>{Object.getOwnPropertyNames(t.prototype).forEach(n=>{typeof e.prototype==`object`&&e.prototype&&n in e.prototype||Object.defineProperty(e.prototype,n,Object.getOwnPropertyDescriptor(t.prototype,n)??Object.create(null))})})}async function H(){x().show()}var U;(function(e){e[e.Trace=1]=`Trace`,e[e.Debug=2]=`Debug`,e[e.Info=3]=`Info`,e[e.Warn=4]=`Warn`,e[e.Error=5]=`Error`})(U||={});function W(e){if(e)if(e.startsWith(`Error`)){let t=e.split(`
`)[3]?.trim();if(!t)return;let n=t.match(/at\s+(?<functionName>.*?)\s+\((?<fileName>.*?):(?<lineNumber>\d+):(?<columnNumber>\d+)\)/);if(n){let{functionName:e,fileName:t,lineNumber:r,columnNumber:i}=n.groups;return`${e}@${t}:${r}:${i}`}else{let e=t.match(/at\s+(?<fileName>.*?):(?<lineNumber>\d+):(?<columnNumber>\d+)/);if(e){let{fileName:t,lineNumber:n,columnNumber:r}=e.groups;return`<anonymous>@${t}:${n}:${r}`}}}else return e.split(`
`).map(e=>e.split(`@`)).filter(([e,t])=>e.length>0&&t!==`[native code]`)[2]?.filter(e=>e.length>0).join(`@`)}async function G(e,t,r){let i=W(Error().stack),{file:a,line:o,keyValues:s}=r??{};await n(`plugin:log|log`,{level:e,message:t,location:i,file:a,line:o,keyValues:s})}async function K(e,t){await G(U.Error,e,t)}async function q(e,t){await G(U.Warn,e,t)}async function J(e,t){await G(U.Info,e,t)}async function Y(e,t){await G(U.Debug,e,t)}async function X(e,t){await G(U.Trace,e,t)}function Z(e,t){console[e]=(e,...n)=>{let r=e;for(let e of n)typeof e==`object`?r+=JSON.stringify(e):r+=`${e}`;t(r).catch(e=>{console.error(`log error`,e)})}}function Q(){Z(`log`,X),Z(`debug`,Y),Z(`info`,J),Z(`warn`,q),Z(`error`,K)}var $;(function(e){e.Nsis=`nsis`,e.Msi=`msi`,e.Deb=`deb`,e.Rpm=`rpm`,e.AppImage=`appimage`,e.App=`app`})($||={});async function ee(){return n(`plugin:app|version`)}async function te(){return n(`plugin:app|name`)}function ne(){return window.__TAURI_OS_PLUGIN_INTERNALS__.platform}export{y as CloseRequestedEvent,E as Effect,D as EffectState,s as LogicalPosition,i as LogicalSize,c as PhysicalPosition,a as PhysicalSize,b as ProgressBarStatus,u as TauriEvent,v as UserAttentionType,B as WebviewWindow,w as Window,M as availableMonitors,k as currentMonitor,N as cursorPosition,m as emit,h as emitTo,Z as forwardConsole,z as getAllWebviewWindows,S as getAllWindows,R as getCurrentWebviewWindow,x as getCurrentWindow,te as getName,ee as getVersion,f as listen,j as monitorFromPoint,p as once,ne as platform,A as primaryMonitor,Q as redirectLogs,H as setupAppWindow};
//# sourceMappingURL=tauri-Cxf-KRk8.js.map
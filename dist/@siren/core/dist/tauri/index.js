import{Channel as e,PluginListener as t,Resource as n,SERIALIZE_TO_IPC_FN as r,addPluginListener as i,checkPermissions as a,convertFileSrc as o,invoke as s,isTauri as c,requestPermissions as l,transformCallback as u}from"../core-DqoKAhCt.js";import{CloseRequestedEvent as d,Effect as f,EffectState as p,LogicalPosition as m,LogicalSize as h,PhysicalPosition as g,PhysicalSize as _,ProgressBarStatus as v,TauriEvent as y,UserAttentionType as b,WebviewWindow as x,Window as S,availableMonitors as C,currentMonitor as w,cursorPosition as T,emit as E,emitTo as D,forwardConsole as O,getAllWebviewWindows as k,getAllWindows as A,getCurrentWebviewWindow as j,getCurrentWindow as M,getName as N,getVersion as P,listen as F,monitorFromPoint as I,once as L,platform as R,primaryMonitor as z,redirectLogs as B,setupAppWindow as V}from"../tauri-Cxf-KRk8.js";export{e as Channel,d as CloseRequestedEvent,f as Effect,p as EffectState,m as LogicalPosition,h as LogicalSize,g as PhysicalPosition,_ as PhysicalSize,t as PluginListener,v as ProgressBarStatus,n as Resource,r as SERIALIZE_TO_IPC_FN,y as TauriEvent,b as UserAttentionType,x as WebviewWindow,S as Window,i as addPluginListener,C as availableMonitors,a as checkPermissions,o as convertFileSrc,w as currentMonitor,T as cursorPosition,E as emit,D as emitTo,O as forwardConsole,k as getAllWebviewWindows,A as getAllWindows,N as getAppName,P as getAppVersion,j as getCurrentWebviewWindow,M as getCurrentWindow,s as invoke,c as isTauri,F as listen,I as monitorFromPoint,L as once,R as platform,z as primaryMonitor,B as redirectLogs,l as requestPermissions,V as setupAppWindow,u as transformCallback};
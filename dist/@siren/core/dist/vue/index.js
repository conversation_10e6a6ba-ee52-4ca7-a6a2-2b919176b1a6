import"../core-DqoKAhCt.js";import"../api-B6yfKZ03.js";import{useAppStore as e,useSettingsStore as t}from"../settings-Ce9F8p1y.js";import{computed as n,inject as r}from"vue";const i=Symbol(`pluginContext`);function a(r){let i=t(),a=e(),o=n(()=>i.langCode),s=n(()=>a.name);return(e,t)=>{let n=typeof r==`function`?r():typeof r==`string`?r:r?.value||s.value,i=e;n&&(i=`${n}.${e}`);let a=window.__LANG_MAP__[o.value]||window.__LANG_MAP__.zh,c=a[i]||a[`${s.value}.${e}`]||e;return t&&Object.entries(t).forEach(([e,t])=>{c=c.replace(`{{${e}}}`,t)}),c}}function o(){let e=r(i);return a(e?.id)}export{i as pluginContextSb,a as useI18n,o as useScopedI18n};
//# sourceMappingURL=index.js.map
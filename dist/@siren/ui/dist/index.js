import*as e from"vue";import{Comment as t,Fragment as n,Teleport as r,camelize as i,cloneVNode as a,computed as o,createBlock as s,createCommentVNode as c,createElementBlock as l,createElementVNode as u,createTextVNode as d,createVNode as f,defineComponent as p,getCurrentInstance as m,guardReactiveProps as h,h as g,inject as _,isRef as v,markRaw as y,mergeDefaults as b,mergeProps as x,nextTick as S,normalizeClass as C,normalizeProps as w,normalizeStyle as T,onBeforeUnmount as E,onMounted as D,onUnmounted as O,openBlock as k,provide as A,reactive as ee,ref as j,renderList as M,renderSlot as N,resolveDynamicComponent as te,shallowRef as ne,toDisplayString as P,toHandlerKey as re,toRef as ie,toRefs as F,toValue as ae,unref as I,useAttrs as L,vModelText as oe,watch as R,watchEffect as z,watchPostEffect as se,withCtx as B,withDirectives as ce,withKeys as le,withModifiers as V}from"vue";import{computedEager as ue,createGlobalState as de,createSharedComposable as fe,defaultWindow as pe,onKeyStroke as me,reactiveOmit as H,toValue as he,unrefElement as ge,useElementVisibility as _e,useEventListener as ve,useMounted as ye,useResizeObserver as be,useVModel as xe}from"@vueuse/core";import{createEventHook as Se,isClient as Ce,isIOS as we,reactiveComputed as Te,refAutoReset as Ee,tryOnBeforeUnmount as De}from"@vueuse/shared";import{ContextMenuManager as Oe,KeyBinding as ke,getManager as Ae}from"@siren/core";import{useI18n as je}from"@siren/core/vue";import{useContextMenuStore as Me}from"@siren/core/store";import{getCurrentWebviewWindow as Ne}from"@siren/core/tauri";import{useRouter as Pe}from"vue-router";var Fe=Object.defineProperty,Ie=Object.getOwnPropertyDescriptor,Le=Object.getOwnPropertyNames,Re=Object.prototype.hasOwnProperty,ze=e=>{let t={};for(var n in e)Fe(t,n,{get:e[n],enumerable:!0});return t},Be=(e,t,n,r)=>{if(t&&typeof t==`object`||typeof t==`function`)for(var i=Le(t),a=0,o=i.length,s;a<o;a++)s=i[a],!Re.call(e,s)&&s!==n&&Fe(e,s,{get:(e=>t[e]).bind(null,s),enumerable:!(r=Ie(t,s))||r.enumerable});return e},Ve=(e,t,n)=>(Be(e,t,`default`),n&&Be(n,t,`default`));function He(e){var t,n,r=``;if(typeof e==`string`||typeof e==`number`)r+=e;else if(typeof e==`object`)if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(n=He(e[t]))&&(r&&(r+=` `),r+=n)}else for(n in e)e[n]&&(r&&(r+=` `),r+=n);return r}function Ue(){for(var e,t,n=0,r=``,i=arguments.length;n<i;n++)(e=arguments[n])&&(t=He(e))&&(r&&(r+=` `),r+=t);return r}const We=e=>{let t=Je(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{let n=e.split(`-`);return n[0]===``&&n.length!==1&&n.shift(),Ge(n,t)||qe(e)},getConflictingClassGroupIds:(e,t)=>{let i=n[e]||[];return t&&r[e]?[...i,...r[e]]:i}}},Ge=(e,t)=>{if(e.length===0)return t.classGroupId;let n=e[0],r=t.nextPart.get(n),i=r?Ge(e.slice(1),r):void 0;if(i)return i;if(t.validators.length===0)return;let a=e.join(`-`);return t.validators.find(({validator:e})=>e(a))?.classGroupId},Ke=/^\[(.+)\]$/,qe=e=>{if(Ke.test(e)){let t=Ke.exec(e)[1],n=t?.substring(0,t.indexOf(`:`));if(n)return`arbitrary..`+n}},Je=e=>{let{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(let e in n)Ye(n[e],r,e,t);return r},Ye=(e,t,n,r)=>{e.forEach(e=>{if(typeof e==`string`){let r=e===``?t:Xe(t,e);r.classGroupId=n;return}if(typeof e==`function`){if(Ze(e)){Ye(e(r),t,n,r);return}t.validators.push({validator:e,classGroupId:n});return}Object.entries(e).forEach(([e,i])=>{Ye(i,Xe(t,e),n,r)})})},Xe=(e,t)=>{let n=e;return t.split(`-`).forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},Ze=e=>e.isThemeGetter,Qe=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,n=new Map,r=new Map,i=(i,a)=>{n.set(i,a),t++,t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);if(t!==void 0)return t;if((t=r.get(e))!==void 0)return i(e,t),t},set(e,t){n.has(e)?n.set(e,t):i(e,t)}}},$e=e=>{let{prefix:t,experimentalParseClassName:n}=e,r=e=>{let t=[],n=0,r=0,i=0,a;for(let o=0;o<e.length;o++){let s=e[o];if(n===0&&r===0){if(s===`:`){t.push(e.slice(i,o)),i=o+1;continue}if(s===`/`){a=o;continue}}s===`[`?n++:s===`]`?n--:s===`(`?r++:s===`)`&&r--}let o=t.length===0?e:e.substring(i),s=et(o),c=s!==o,l=a&&a>i?a-i:void 0;return{modifiers:t,hasImportantModifier:c,baseClassName:s,maybePostfixModifierPosition:l}};if(t){let e=t+`:`,n=r;r=t=>t.startsWith(e)?n(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(n){let e=r;r=t=>n({className:t,parseClassName:e})}return r},et=e=>e.endsWith(`!`)?e.substring(0,e.length-1):e.startsWith(`!`)?e.substring(1):e,tt=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let n=[],r=[];return e.forEach(e=>{e[0]===`[`||t[e]?(n.push(...r.sort(),e),r=[]):r.push(e)}),n.push(...r.sort()),n}},nt=e=>({cache:Qe(e.cacheSize),parseClassName:$e(e),sortModifiers:tt(e),...We(e)}),rt=/\s+/,it=(e,t)=>{let{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:i,sortModifiers:a}=t,o=[],s=e.trim().split(rt),c=``;for(let e=s.length-1;e>=0;--e){let t=s[e],{isExternal:l,modifiers:u,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:p}=n(t);if(l){c=t+(c.length>0?` `+c:c);continue}let m=!!p,h=r(m?f.substring(0,p):f);if(!h){if(!m){c=t+(c.length>0?` `+c:c);continue}if(h=r(f),!h){c=t+(c.length>0?` `+c:c);continue}m=!1}let g=a(u).join(`:`),_=d?g+`!`:g,v=_+h;if(o.includes(v))continue;o.push(v);let y=i(h,m);for(let e=0;e<y.length;++e){let t=y[e];o.push(_+t)}c=t+(c.length>0?` `+c:c)}return c};function at(){let e=0,t,n,r=``;for(;e<arguments.length;)(t=arguments[e++])&&(n=ot(t))&&(r&&(r+=` `),r+=n);return r}const ot=e=>{if(typeof e==`string`)return e;let t,n=``;for(let r=0;r<e.length;r++)e[r]&&(t=ot(e[r]))&&(n&&(n+=` `),n+=t);return n};function st(e,...t){let n,r,i,a=o;function o(o){let c=t.reduce((e,t)=>t(e),e());return n=nt(c),r=n.cache.get,i=n.cache.set,a=s,s(o)}function s(e){let t=r(e);if(t)return t;let a=it(e,n);return i(e,a),a}return function(){return a(at.apply(null,arguments))}}const U=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},ct=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,lt=/^\((?:(\w[\w-]*):)?(.+)\)$/i,ut=/^\d+\/\d+$/,dt=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,ft=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,pt=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,mt=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ht=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,gt=e=>ut.test(e),W=e=>!!e&&!Number.isNaN(Number(e)),_t=e=>!!e&&Number.isInteger(Number(e)),vt=e=>e.endsWith(`%`)&&W(e.slice(0,-1)),yt=e=>dt.test(e),bt=()=>!0,xt=e=>ft.test(e)&&!pt.test(e),St=()=>!1,Ct=e=>mt.test(e),wt=e=>ht.test(e),Tt=e=>!G(e)&&!K(e),Et=e=>Rt(e,Ht,St),G=e=>ct.test(e),Dt=e=>Rt(e,Ut,xt),Ot=e=>Rt(e,Wt,W),kt=e=>Rt(e,Bt,St),At=e=>Rt(e,Vt,wt),jt=e=>Rt(e,Kt,Ct),K=e=>lt.test(e),Mt=e=>zt(e,Ut),Nt=e=>zt(e,Gt),Pt=e=>zt(e,Bt),Ft=e=>zt(e,Ht),It=e=>zt(e,Vt),Lt=e=>zt(e,Kt,!0),Rt=(e,t,n)=>{let r=ct.exec(e);return r?r[1]?t(r[1]):n(r[2]):!1},zt=(e,t,n=!1)=>{let r=lt.exec(e);return r?r[1]?t(r[1]):n:!1},Bt=e=>e===`position`||e===`percentage`,Vt=e=>e===`image`||e===`url`,Ht=e=>e===`length`||e===`size`||e===`bg-size`,Ut=e=>e===`length`,Wt=e=>e===`number`,Gt=e=>e===`family-name`,Kt=e=>e===`shadow`,qt=st(()=>{let e=U(`color`),t=U(`font`),n=U(`text`),r=U(`font-weight`),i=U(`tracking`),a=U(`leading`),o=U(`breakpoint`),s=U(`container`),c=U(`spacing`),l=U(`radius`),u=U(`shadow`),d=U(`inset-shadow`),f=U(`text-shadow`),p=U(`drop-shadow`),m=U(`blur`),h=U(`perspective`),g=U(`aspect`),_=U(`ease`),v=U(`animate`),y=()=>[`auto`,`avoid`,`all`,`avoid-page`,`page`,`left`,`right`,`column`],b=()=>[`center`,`top`,`bottom`,`left`,`right`,`top-left`,`left-top`,`top-right`,`right-top`,`bottom-right`,`right-bottom`,`bottom-left`,`left-bottom`],x=()=>[...b(),K,G],S=()=>[`auto`,`hidden`,`clip`,`visible`,`scroll`],C=()=>[`auto`,`contain`,`none`],w=()=>[K,G,c],T=()=>[gt,`full`,`auto`,...w()],E=()=>[_t,`none`,`subgrid`,K,G],D=()=>[`auto`,{span:[`full`,_t,K,G]},_t,K,G],O=()=>[_t,`auto`,K,G],k=()=>[`auto`,`min`,`max`,`fr`,K,G],A=()=>[`start`,`end`,`center`,`between`,`around`,`evenly`,`stretch`,`baseline`,`center-safe`,`end-safe`],ee=()=>[`start`,`end`,`center`,`stretch`,`center-safe`,`end-safe`],j=()=>[`auto`,...w()],M=()=>[gt,`auto`,`full`,`dvw`,`dvh`,`lvw`,`lvh`,`svw`,`svh`,`min`,`max`,`fit`,...w()],N=()=>[e,K,G],te=()=>[...b(),Pt,kt,{position:[K,G]}],ne=()=>[`no-repeat`,{repeat:[``,`x`,`y`,`space`,`round`]}],P=()=>[`auto`,`cover`,`contain`,Ft,Et,{size:[K,G]}],re=()=>[vt,Mt,Dt],ie=()=>[``,`none`,`full`,l,K,G],F=()=>[``,W,Mt,Dt],ae=()=>[`solid`,`dashed`,`dotted`,`double`],I=()=>[`normal`,`multiply`,`screen`,`overlay`,`darken`,`lighten`,`color-dodge`,`color-burn`,`hard-light`,`soft-light`,`difference`,`exclusion`,`hue`,`saturation`,`color`,`luminosity`],L=()=>[W,vt,Pt,kt],oe=()=>[``,`none`,m,K,G],R=()=>[`none`,W,K,G],z=()=>[`none`,W,K,G],se=()=>[W,K,G],B=()=>[gt,`full`,...w()];return{cacheSize:500,theme:{animate:[`spin`,`ping`,`pulse`,`bounce`],aspect:[`video`],blur:[yt],breakpoint:[yt],color:[bt],container:[yt],"drop-shadow":[yt],ease:[`in`,`out`,`in-out`],font:[Tt],"font-weight":[`thin`,`extralight`,`light`,`normal`,`medium`,`semibold`,`bold`,`extrabold`,`black`],"inset-shadow":[yt],leading:[`none`,`tight`,`snug`,`normal`,`relaxed`,`loose`],perspective:[`dramatic`,`near`,`normal`,`midrange`,`distant`,`none`],radius:[yt],shadow:[yt],spacing:[`px`,W],text:[yt],"text-shadow":[yt],tracking:[`tighter`,`tight`,`normal`,`wide`,`wider`,`widest`]},classGroups:{aspect:[{aspect:[`auto`,`square`,gt,G,K,g]}],container:[`container`],columns:[{columns:[W,G,K,s]}],"break-after":[{"break-after":y()}],"break-before":[{"break-before":y()}],"break-inside":[{"break-inside":[`auto`,`avoid`,`avoid-page`,`avoid-column`]}],"box-decoration":[{"box-decoration":[`slice`,`clone`]}],box:[{box:[`border`,`content`]}],display:[`block`,`inline-block`,`inline`,`flex`,`inline-flex`,`table`,`inline-table`,`table-caption`,`table-cell`,`table-column`,`table-column-group`,`table-footer-group`,`table-header-group`,`table-row-group`,`table-row`,`flow-root`,`grid`,`inline-grid`,`contents`,`list-item`,`hidden`],sr:[`sr-only`,`not-sr-only`],float:[{float:[`right`,`left`,`none`,`start`,`end`]}],clear:[{clear:[`left`,`right`,`both`,`none`,`start`,`end`]}],isolation:[`isolate`,`isolation-auto`],"object-fit":[{object:[`contain`,`cover`,`fill`,`none`,`scale-down`]}],"object-position":[{object:x()}],overflow:[{overflow:S()}],"overflow-x":[{"overflow-x":S()}],"overflow-y":[{"overflow-y":S()}],overscroll:[{overscroll:C()}],"overscroll-x":[{"overscroll-x":C()}],"overscroll-y":[{"overscroll-y":C()}],position:[`static`,`fixed`,`absolute`,`relative`,`sticky`],inset:[{inset:T()}],"inset-x":[{"inset-x":T()}],"inset-y":[{"inset-y":T()}],start:[{start:T()}],end:[{end:T()}],top:[{top:T()}],right:[{right:T()}],bottom:[{bottom:T()}],left:[{left:T()}],visibility:[`visible`,`invisible`,`collapse`],z:[{z:[_t,`auto`,K,G]}],basis:[{basis:[gt,`full`,`auto`,s,...w()]}],"flex-direction":[{flex:[`row`,`row-reverse`,`col`,`col-reverse`]}],"flex-wrap":[{flex:[`nowrap`,`wrap`,`wrap-reverse`]}],flex:[{flex:[W,gt,`auto`,`initial`,`none`,G]}],grow:[{grow:[``,W,K,G]}],shrink:[{shrink:[``,W,K,G]}],order:[{order:[_t,`first`,`last`,`none`,K,G]}],"grid-cols":[{"grid-cols":E()}],"col-start-end":[{col:D()}],"col-start":[{"col-start":O()}],"col-end":[{"col-end":O()}],"grid-rows":[{"grid-rows":E()}],"row-start-end":[{row:D()}],"row-start":[{"row-start":O()}],"row-end":[{"row-end":O()}],"grid-flow":[{"grid-flow":[`row`,`col`,`dense`,`row-dense`,`col-dense`]}],"auto-cols":[{"auto-cols":k()}],"auto-rows":[{"auto-rows":k()}],gap:[{gap:w()}],"gap-x":[{"gap-x":w()}],"gap-y":[{"gap-y":w()}],"justify-content":[{justify:[...A(),`normal`]}],"justify-items":[{"justify-items":[...ee(),`normal`]}],"justify-self":[{"justify-self":[`auto`,...ee()]}],"align-content":[{content:[`normal`,...A()]}],"align-items":[{items:[...ee(),{baseline:[``,`last`]}]}],"align-self":[{self:[`auto`,...ee(),{baseline:[``,`last`]}]}],"place-content":[{"place-content":A()}],"place-items":[{"place-items":[...ee(),`baseline`]}],"place-self":[{"place-self":[`auto`,...ee()]}],p:[{p:w()}],px:[{px:w()}],py:[{py:w()}],ps:[{ps:w()}],pe:[{pe:w()}],pt:[{pt:w()}],pr:[{pr:w()}],pb:[{pb:w()}],pl:[{pl:w()}],m:[{m:j()}],mx:[{mx:j()}],my:[{my:j()}],ms:[{ms:j()}],me:[{me:j()}],mt:[{mt:j()}],mr:[{mr:j()}],mb:[{mb:j()}],ml:[{ml:j()}],"space-x":[{"space-x":w()}],"space-x-reverse":[`space-x-reverse`],"space-y":[{"space-y":w()}],"space-y-reverse":[`space-y-reverse`],size:[{size:M()}],w:[{w:[s,`screen`,...M()]}],"min-w":[{"min-w":[s,`screen`,`none`,...M()]}],"max-w":[{"max-w":[s,`screen`,`none`,`prose`,{screen:[o]},...M()]}],h:[{h:[`screen`,`lh`,...M()]}],"min-h":[{"min-h":[`screen`,`lh`,`none`,...M()]}],"max-h":[{"max-h":[`screen`,`lh`,...M()]}],"font-size":[{text:[`base`,n,Mt,Dt]}],"font-smoothing":[`antialiased`,`subpixel-antialiased`],"font-style":[`italic`,`not-italic`],"font-weight":[{font:[r,K,Ot]}],"font-stretch":[{"font-stretch":[`ultra-condensed`,`extra-condensed`,`condensed`,`semi-condensed`,`normal`,`semi-expanded`,`expanded`,`extra-expanded`,`ultra-expanded`,vt,G]}],"font-family":[{font:[Nt,G,t]}],"fvn-normal":[`normal-nums`],"fvn-ordinal":[`ordinal`],"fvn-slashed-zero":[`slashed-zero`],"fvn-figure":[`lining-nums`,`oldstyle-nums`],"fvn-spacing":[`proportional-nums`,`tabular-nums`],"fvn-fraction":[`diagonal-fractions`,`stacked-fractions`],tracking:[{tracking:[i,K,G]}],"line-clamp":[{"line-clamp":[W,`none`,K,Ot]}],leading:[{leading:[a,...w()]}],"list-image":[{"list-image":[`none`,K,G]}],"list-style-position":[{list:[`inside`,`outside`]}],"list-style-type":[{list:[`disc`,`decimal`,`none`,K,G]}],"text-alignment":[{text:[`left`,`center`,`right`,`justify`,`start`,`end`]}],"placeholder-color":[{placeholder:N()}],"text-color":[{text:N()}],"text-decoration":[`underline`,`overline`,`line-through`,`no-underline`],"text-decoration-style":[{decoration:[...ae(),`wavy`]}],"text-decoration-thickness":[{decoration:[W,`from-font`,`auto`,K,Dt]}],"text-decoration-color":[{decoration:N()}],"underline-offset":[{"underline-offset":[W,`auto`,K,G]}],"text-transform":[`uppercase`,`lowercase`,`capitalize`,`normal-case`],"text-overflow":[`truncate`,`text-ellipsis`,`text-clip`],"text-wrap":[{text:[`wrap`,`nowrap`,`balance`,`pretty`]}],indent:[{indent:w()}],"vertical-align":[{align:[`baseline`,`top`,`middle`,`bottom`,`text-top`,`text-bottom`,`sub`,`super`,K,G]}],whitespace:[{whitespace:[`normal`,`nowrap`,`pre`,`pre-line`,`pre-wrap`,`break-spaces`]}],break:[{break:[`normal`,`words`,`all`,`keep`]}],wrap:[{wrap:[`break-word`,`anywhere`,`normal`]}],hyphens:[{hyphens:[`none`,`manual`,`auto`]}],content:[{content:[`none`,K,G]}],"bg-attachment":[{bg:[`fixed`,`local`,`scroll`]}],"bg-clip":[{"bg-clip":[`border`,`padding`,`content`,`text`]}],"bg-origin":[{"bg-origin":[`border`,`padding`,`content`]}],"bg-position":[{bg:te()}],"bg-repeat":[{bg:ne()}],"bg-size":[{bg:P()}],"bg-image":[{bg:[`none`,{linear:[{to:[`t`,`tr`,`r`,`br`,`b`,`bl`,`l`,`tl`]},_t,K,G],radial:[``,K,G],conic:[_t,K,G]},It,At]}],"bg-color":[{bg:N()}],"gradient-from-pos":[{from:re()}],"gradient-via-pos":[{via:re()}],"gradient-to-pos":[{to:re()}],"gradient-from":[{from:N()}],"gradient-via":[{via:N()}],"gradient-to":[{to:N()}],rounded:[{rounded:ie()}],"rounded-s":[{"rounded-s":ie()}],"rounded-e":[{"rounded-e":ie()}],"rounded-t":[{"rounded-t":ie()}],"rounded-r":[{"rounded-r":ie()}],"rounded-b":[{"rounded-b":ie()}],"rounded-l":[{"rounded-l":ie()}],"rounded-ss":[{"rounded-ss":ie()}],"rounded-se":[{"rounded-se":ie()}],"rounded-ee":[{"rounded-ee":ie()}],"rounded-es":[{"rounded-es":ie()}],"rounded-tl":[{"rounded-tl":ie()}],"rounded-tr":[{"rounded-tr":ie()}],"rounded-br":[{"rounded-br":ie()}],"rounded-bl":[{"rounded-bl":ie()}],"border-w":[{border:F()}],"border-w-x":[{"border-x":F()}],"border-w-y":[{"border-y":F()}],"border-w-s":[{"border-s":F()}],"border-w-e":[{"border-e":F()}],"border-w-t":[{"border-t":F()}],"border-w-r":[{"border-r":F()}],"border-w-b":[{"border-b":F()}],"border-w-l":[{"border-l":F()}],"divide-x":[{"divide-x":F()}],"divide-x-reverse":[`divide-x-reverse`],"divide-y":[{"divide-y":F()}],"divide-y-reverse":[`divide-y-reverse`],"border-style":[{border:[...ae(),`hidden`,`none`]}],"divide-style":[{divide:[...ae(),`hidden`,`none`]}],"border-color":[{border:N()}],"border-color-x":[{"border-x":N()}],"border-color-y":[{"border-y":N()}],"border-color-s":[{"border-s":N()}],"border-color-e":[{"border-e":N()}],"border-color-t":[{"border-t":N()}],"border-color-r":[{"border-r":N()}],"border-color-b":[{"border-b":N()}],"border-color-l":[{"border-l":N()}],"divide-color":[{divide:N()}],"outline-style":[{outline:[...ae(),`none`,`hidden`]}],"outline-offset":[{"outline-offset":[W,K,G]}],"outline-w":[{outline:[``,W,Mt,Dt]}],"outline-color":[{outline:N()}],shadow:[{shadow:[``,`none`,u,Lt,jt]}],"shadow-color":[{shadow:N()}],"inset-shadow":[{"inset-shadow":[`none`,d,Lt,jt]}],"inset-shadow-color":[{"inset-shadow":N()}],"ring-w":[{ring:F()}],"ring-w-inset":[`ring-inset`],"ring-color":[{ring:N()}],"ring-offset-w":[{"ring-offset":[W,Dt]}],"ring-offset-color":[{"ring-offset":N()}],"inset-ring-w":[{"inset-ring":F()}],"inset-ring-color":[{"inset-ring":N()}],"text-shadow":[{"text-shadow":[`none`,f,Lt,jt]}],"text-shadow-color":[{"text-shadow":N()}],opacity:[{opacity:[W,K,G]}],"mix-blend":[{"mix-blend":[...I(),`plus-darker`,`plus-lighter`]}],"bg-blend":[{"bg-blend":I()}],"mask-clip":[{"mask-clip":[`border`,`padding`,`content`,`fill`,`stroke`,`view`]},`mask-no-clip`],"mask-composite":[{mask:[`add`,`subtract`,`intersect`,`exclude`]}],"mask-image-linear-pos":[{"mask-linear":[W]}],"mask-image-linear-from-pos":[{"mask-linear-from":L()}],"mask-image-linear-to-pos":[{"mask-linear-to":L()}],"mask-image-linear-from-color":[{"mask-linear-from":N()}],"mask-image-linear-to-color":[{"mask-linear-to":N()}],"mask-image-t-from-pos":[{"mask-t-from":L()}],"mask-image-t-to-pos":[{"mask-t-to":L()}],"mask-image-t-from-color":[{"mask-t-from":N()}],"mask-image-t-to-color":[{"mask-t-to":N()}],"mask-image-r-from-pos":[{"mask-r-from":L()}],"mask-image-r-to-pos":[{"mask-r-to":L()}],"mask-image-r-from-color":[{"mask-r-from":N()}],"mask-image-r-to-color":[{"mask-r-to":N()}],"mask-image-b-from-pos":[{"mask-b-from":L()}],"mask-image-b-to-pos":[{"mask-b-to":L()}],"mask-image-b-from-color":[{"mask-b-from":N()}],"mask-image-b-to-color":[{"mask-b-to":N()}],"mask-image-l-from-pos":[{"mask-l-from":L()}],"mask-image-l-to-pos":[{"mask-l-to":L()}],"mask-image-l-from-color":[{"mask-l-from":N()}],"mask-image-l-to-color":[{"mask-l-to":N()}],"mask-image-x-from-pos":[{"mask-x-from":L()}],"mask-image-x-to-pos":[{"mask-x-to":L()}],"mask-image-x-from-color":[{"mask-x-from":N()}],"mask-image-x-to-color":[{"mask-x-to":N()}],"mask-image-y-from-pos":[{"mask-y-from":L()}],"mask-image-y-to-pos":[{"mask-y-to":L()}],"mask-image-y-from-color":[{"mask-y-from":N()}],"mask-image-y-to-color":[{"mask-y-to":N()}],"mask-image-radial":[{"mask-radial":[K,G]}],"mask-image-radial-from-pos":[{"mask-radial-from":L()}],"mask-image-radial-to-pos":[{"mask-radial-to":L()}],"mask-image-radial-from-color":[{"mask-radial-from":N()}],"mask-image-radial-to-color":[{"mask-radial-to":N()}],"mask-image-radial-shape":[{"mask-radial":[`circle`,`ellipse`]}],"mask-image-radial-size":[{"mask-radial":[{closest:[`side`,`corner`],farthest:[`side`,`corner`]}]}],"mask-image-radial-pos":[{"mask-radial-at":b()}],"mask-image-conic-pos":[{"mask-conic":[W]}],"mask-image-conic-from-pos":[{"mask-conic-from":L()}],"mask-image-conic-to-pos":[{"mask-conic-to":L()}],"mask-image-conic-from-color":[{"mask-conic-from":N()}],"mask-image-conic-to-color":[{"mask-conic-to":N()}],"mask-mode":[{mask:[`alpha`,`luminance`,`match`]}],"mask-origin":[{"mask-origin":[`border`,`padding`,`content`,`fill`,`stroke`,`view`]}],"mask-position":[{mask:te()}],"mask-repeat":[{mask:ne()}],"mask-size":[{mask:P()}],"mask-type":[{"mask-type":[`alpha`,`luminance`]}],"mask-image":[{mask:[`none`,K,G]}],filter:[{filter:[``,`none`,K,G]}],blur:[{blur:oe()}],brightness:[{brightness:[W,K,G]}],contrast:[{contrast:[W,K,G]}],"drop-shadow":[{"drop-shadow":[``,`none`,p,Lt,jt]}],"drop-shadow-color":[{"drop-shadow":N()}],grayscale:[{grayscale:[``,W,K,G]}],"hue-rotate":[{"hue-rotate":[W,K,G]}],invert:[{invert:[``,W,K,G]}],saturate:[{saturate:[W,K,G]}],sepia:[{sepia:[``,W,K,G]}],"backdrop-filter":[{"backdrop-filter":[``,`none`,K,G]}],"backdrop-blur":[{"backdrop-blur":oe()}],"backdrop-brightness":[{"backdrop-brightness":[W,K,G]}],"backdrop-contrast":[{"backdrop-contrast":[W,K,G]}],"backdrop-grayscale":[{"backdrop-grayscale":[``,W,K,G]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[W,K,G]}],"backdrop-invert":[{"backdrop-invert":[``,W,K,G]}],"backdrop-opacity":[{"backdrop-opacity":[W,K,G]}],"backdrop-saturate":[{"backdrop-saturate":[W,K,G]}],"backdrop-sepia":[{"backdrop-sepia":[``,W,K,G]}],"border-collapse":[{border:[`collapse`,`separate`]}],"border-spacing":[{"border-spacing":w()}],"border-spacing-x":[{"border-spacing-x":w()}],"border-spacing-y":[{"border-spacing-y":w()}],"table-layout":[{table:[`auto`,`fixed`]}],caption:[{caption:[`top`,`bottom`]}],transition:[{transition:[``,`all`,`colors`,`opacity`,`shadow`,`transform`,`none`,K,G]}],"transition-behavior":[{transition:[`normal`,`discrete`]}],duration:[{duration:[W,`initial`,K,G]}],ease:[{ease:[`linear`,`initial`,_,K,G]}],delay:[{delay:[W,K,G]}],animate:[{animate:[`none`,v,K,G]}],backface:[{backface:[`hidden`,`visible`]}],perspective:[{perspective:[h,K,G]}],"perspective-origin":[{"perspective-origin":x()}],rotate:[{rotate:R()}],"rotate-x":[{"rotate-x":R()}],"rotate-y":[{"rotate-y":R()}],"rotate-z":[{"rotate-z":R()}],scale:[{scale:z()}],"scale-x":[{"scale-x":z()}],"scale-y":[{"scale-y":z()}],"scale-z":[{"scale-z":z()}],"scale-3d":[`scale-3d`],skew:[{skew:se()}],"skew-x":[{"skew-x":se()}],"skew-y":[{"skew-y":se()}],transform:[{transform:[K,G,``,`none`,`gpu`,`cpu`]}],"transform-origin":[{origin:x()}],"transform-style":[{transform:[`3d`,`flat`]}],translate:[{translate:B()}],"translate-x":[{"translate-x":B()}],"translate-y":[{"translate-y":B()}],"translate-z":[{"translate-z":B()}],"translate-none":[`translate-none`],accent:[{accent:N()}],appearance:[{appearance:[`none`,`auto`]}],"caret-color":[{caret:N()}],"color-scheme":[{scheme:[`normal`,`dark`,`light`,`light-dark`,`only-dark`,`only-light`]}],cursor:[{cursor:[`auto`,`default`,`pointer`,`wait`,`text`,`move`,`help`,`not-allowed`,`none`,`context-menu`,`progress`,`cell`,`crosshair`,`vertical-text`,`alias`,`copy`,`no-drop`,`grab`,`grabbing`,`all-scroll`,`col-resize`,`row-resize`,`n-resize`,`e-resize`,`s-resize`,`w-resize`,`ne-resize`,`nw-resize`,`se-resize`,`sw-resize`,`ew-resize`,`ns-resize`,`nesw-resize`,`nwse-resize`,`zoom-in`,`zoom-out`,K,G]}],"field-sizing":[{"field-sizing":[`fixed`,`content`]}],"pointer-events":[{"pointer-events":[`auto`,`none`]}],resize:[{resize:[`none`,``,`y`,`x`]}],"scroll-behavior":[{scroll:[`auto`,`smooth`]}],"scroll-m":[{"scroll-m":w()}],"scroll-mx":[{"scroll-mx":w()}],"scroll-my":[{"scroll-my":w()}],"scroll-ms":[{"scroll-ms":w()}],"scroll-me":[{"scroll-me":w()}],"scroll-mt":[{"scroll-mt":w()}],"scroll-mr":[{"scroll-mr":w()}],"scroll-mb":[{"scroll-mb":w()}],"scroll-ml":[{"scroll-ml":w()}],"scroll-p":[{"scroll-p":w()}],"scroll-px":[{"scroll-px":w()}],"scroll-py":[{"scroll-py":w()}],"scroll-ps":[{"scroll-ps":w()}],"scroll-pe":[{"scroll-pe":w()}],"scroll-pt":[{"scroll-pt":w()}],"scroll-pr":[{"scroll-pr":w()}],"scroll-pb":[{"scroll-pb":w()}],"scroll-pl":[{"scroll-pl":w()}],"snap-align":[{snap:[`start`,`end`,`center`,`align-none`]}],"snap-stop":[{snap:[`normal`,`always`]}],"snap-type":[{snap:[`none`,`x`,`y`,`both`]}],"snap-strictness":[{snap:[`mandatory`,`proximity`]}],touch:[{touch:[`auto`,`none`,`manipulation`]}],"touch-x":[{"touch-pan":[`x`,`left`,`right`]}],"touch-y":[{"touch-pan":[`y`,`up`,`down`]}],"touch-pz":[`touch-pinch-zoom`],select:[{select:[`none`,`text`,`all`,`auto`]}],"will-change":[{"will-change":[`auto`,`scroll`,`contents`,`transform`,K,G]}],fill:[{fill:[`none`,...N()]}],"stroke-w":[{stroke:[W,Mt,Dt,Ot]}],stroke:[{stroke:[`none`,...N()]}],"forced-color-adjust":[{"forced-color-adjust":[`auto`,`none`]}]},conflictingClassGroups:{overflow:[`overflow-x`,`overflow-y`],overscroll:[`overscroll-x`,`overscroll-y`],inset:[`inset-x`,`inset-y`,`start`,`end`,`top`,`right`,`bottom`,`left`],"inset-x":[`right`,`left`],"inset-y":[`top`,`bottom`],flex:[`basis`,`grow`,`shrink`],gap:[`gap-x`,`gap-y`],p:[`px`,`py`,`ps`,`pe`,`pt`,`pr`,`pb`,`pl`],px:[`pr`,`pl`],py:[`pt`,`pb`],m:[`mx`,`my`,`ms`,`me`,`mt`,`mr`,`mb`,`ml`],mx:[`mr`,`ml`],my:[`mt`,`mb`],size:[`w`,`h`],"font-size":[`leading`],"fvn-normal":[`fvn-ordinal`,`fvn-slashed-zero`,`fvn-figure`,`fvn-spacing`,`fvn-fraction`],"fvn-ordinal":[`fvn-normal`],"fvn-slashed-zero":[`fvn-normal`],"fvn-figure":[`fvn-normal`],"fvn-spacing":[`fvn-normal`],"fvn-fraction":[`fvn-normal`],"line-clamp":[`display`,`overflow`],rounded:[`rounded-s`,`rounded-e`,`rounded-t`,`rounded-r`,`rounded-b`,`rounded-l`,`rounded-ss`,`rounded-se`,`rounded-ee`,`rounded-es`,`rounded-tl`,`rounded-tr`,`rounded-br`,`rounded-bl`],"rounded-s":[`rounded-ss`,`rounded-es`],"rounded-e":[`rounded-se`,`rounded-ee`],"rounded-t":[`rounded-tl`,`rounded-tr`],"rounded-r":[`rounded-tr`,`rounded-br`],"rounded-b":[`rounded-br`,`rounded-bl`],"rounded-l":[`rounded-tl`,`rounded-bl`],"border-spacing":[`border-spacing-x`,`border-spacing-y`],"border-w":[`border-w-x`,`border-w-y`,`border-w-s`,`border-w-e`,`border-w-t`,`border-w-r`,`border-w-b`,`border-w-l`],"border-w-x":[`border-w-r`,`border-w-l`],"border-w-y":[`border-w-t`,`border-w-b`],"border-color":[`border-color-x`,`border-color-y`,`border-color-s`,`border-color-e`,`border-color-t`,`border-color-r`,`border-color-b`,`border-color-l`],"border-color-x":[`border-color-r`,`border-color-l`],"border-color-y":[`border-color-t`,`border-color-b`],translate:[`translate-x`,`translate-y`,`translate-none`],"translate-none":[`translate`,`translate-x`,`translate-y`,`translate-z`],"scroll-m":[`scroll-mx`,`scroll-my`,`scroll-ms`,`scroll-me`,`scroll-mt`,`scroll-mr`,`scroll-mb`,`scroll-ml`],"scroll-mx":[`scroll-mr`,`scroll-ml`],"scroll-my":[`scroll-mt`,`scroll-mb`],"scroll-p":[`scroll-px`,`scroll-py`,`scroll-ps`,`scroll-pe`,`scroll-pt`,`scroll-pr`,`scroll-pb`,`scroll-pl`],"scroll-px":[`scroll-pr`,`scroll-pl`],"scroll-py":[`scroll-pt`,`scroll-pb`],touch:[`touch-x`,`touch-y`,`touch-pz`],"touch-x":[`touch`],"touch-y":[`touch`],"touch-pz":[`touch`]},conflictingClassGroupModifiers:{"font-size":[`leading`]},orderSensitiveModifiers:[`*`,`**`,`after`,`backdrop`,`before`,`details-content`,`file`,`first-letter`,`first-line`,`marker`,`placeholder`,`selection`]}});function q(...e){return qt(Ue(e))}const Jt=e=>typeof e==`boolean`?`${e}`:e===0?`0`:e,Yt=Ue,Xt=(e,t)=>n=>{if(t?.variants==null)return Yt(e,n?.class,n?.className);let{variants:r,defaultVariants:i}=t,a=Object.keys(r).map(e=>{let t=n?.[e],a=i?.[e];if(t===null)return null;let o=Jt(t)||Jt(a);return r[e][o]}),o=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return r===void 0||(e[n]=r),e},{}),s=t?.compoundVariants?.reduce((e,t)=>{let{class:n,className:r,...a}=t;return Object.entries(a).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...i,...o}[t]):{...i,...o}[t]===n})?[...e,n,r]:e},[]);return Yt(e,a,s,n?.class,n?.className)};function Zt(e){return typeof e==`string`?`'${e}'`:new Qt().serialize(e)}const Qt=function(){class e{#t=new Map;compare(e,t){let n=typeof e,r=typeof t;return n===`string`&&r===`string`?e.localeCompare(t):n===`number`&&r===`number`?e-t:String.prototype.localeCompare.call(this.serialize(e,!0),this.serialize(t,!0))}serialize(e,t){if(e===null)return`null`;switch(typeof e){case`string`:return t?e:`'${e}'`;case`bigint`:return`${e}n`;case`object`:return this.$object(e);case`function`:return this.$function(e)}return String(e)}serializeObject(e){let t=Object.prototype.toString.call(e);if(t!==`[object Object]`)return this.serializeBuiltInType(t.length<10?`unknown:${t}`:t.slice(8,-1),e);let n=e.constructor,r=n===Object||n===void 0?``:n.name;if(r!==``&&globalThis[r]===n)return this.serializeBuiltInType(r,e);if(typeof e.toJSON==`function`){let t=e.toJSON();return r+(typeof t==`object`&&t?this.$object(t):`(${this.serialize(t)})`)}return this.serializeObjectEntries(r,Object.entries(e))}serializeBuiltInType(e,t){let n=this[`$`+e];if(n)return n.call(this,t);if(typeof t?.entries==`function`)return this.serializeObjectEntries(e,t.entries());throw Error(`Cannot serialize ${e}`)}serializeObjectEntries(e,t){let n=Array.from(t).sort((e,t)=>this.compare(e[0],t[0])),r=`${e}{`;for(let e=0;e<n.length;e++){let[t,i]=n[e];r+=`${this.serialize(t,!0)}:${this.serialize(i)}`,e<n.length-1&&(r+=`,`)}return r+`}`}$object(e){let t=this.#t.get(e);return t===void 0&&(this.#t.set(e,`#${this.#t.size}`),t=this.serializeObject(e),this.#t.set(e,t)),t}$function(e){let t=Function.prototype.toString.call(e);return t.slice(-15)===`[native code] }`?`${e.name||``}()[native]`:`${e.name}(${e.length})${t.replace(/\s*\n\s*/g,``)}`}$Array(e){let t=`[`;for(let n=0;n<e.length;n++)t+=this.serialize(e[n]),n<e.length-1&&(t+=`,`);return t+`]`}$Date(e){try{return`Date(${e.toISOString()})`}catch{return`Date(null)`}}$ArrayBuffer(e){return`ArrayBuffer[${new Uint8Array(e).join(`,`)}]`}$Set(e){return`Set${this.$Array(Array.from(e).sort((e,t)=>this.compare(e,t)))}`}$Map(e){return this.serializeObjectEntries(`Map`,e.entries())}}for(let t of[`Error`,`RegExp`,`URL`])e.prototype[`$`+t]=function(e){return`${t}(${e})`};for(let t of[`Int8Array`,`Uint8Array`,`Uint8ClampedArray`,`Int16Array`,`Uint16Array`,`Int32Array`,`Uint32Array`,`Float32Array`,`Float64Array`])e.prototype[`$`+t]=function(e){return`${t}[${e.join(`,`)}]`};for(let t of[`BigInt64Array`,`BigUint64Array`])e.prototype[`$`+t]=function(e){return`${t}[${e.join(`n,`)}${e.length>0?`n`:``}]`};return e}();function $t(e,t){return e===t||Zt(e)===Zt(t)}function en(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}const tn=typeof document<`u`;function nn(e,t=-1/0,n=1/0){return Math.min(n,Math.max(t,e))}function rn(e,t){let n=e,r=t.toString(),i=r.indexOf(`.`),a=i>=0?r.length-i:0;if(a>0){let e=10**a;n=Math.round(n*e)/e}return n}function an(e,t,n,r){t=Number(t),n=Number(n);let i=(e-(Number.isNaN(t)?0:t))%r,a=rn(Math.abs(i)*2>=r?e+Math.sign(i)*(r-Math.abs(i)):e-i,r);return Number.isNaN(t)?!Number.isNaN(n)&&a>n&&(a=Math.floor(rn(n/r,r))*r):a<t?a=t:!Number.isNaN(n)&&a>n&&(a=t+Math.floor(rn((n-t)/r,r))*r),a=rn(a,r),a}function J(e,t){let n=typeof e==`string`&&!t?`${e}Context`:t,r=Symbol(n);return[t=>{let n=_(r,t);if(n||n===null)return n;throw Error(`Injection \`${r.toString()}\` not found. Component must be used within ${Array.isArray(e)?`one of the following components: ${e.join(`, `)}`:`\`${e}\``}`)},e=>(A(r,e),e)]}function on(){let e=document.activeElement;if(e==null)return null;for(;e!=null&&e.shadowRoot!=null&&e.shadowRoot.activeElement!=null;)e=e.shadowRoot.activeElement;return e}function sn(e,t,n){let r=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&r.addEventListener(e,t,{once:!0}),r.dispatchEvent(i)}function cn(e){return e==null}function ln(e,t){return cn(e)?!1:Array.isArray(e)?e.some(e=>$t(e,t)):$t(e,t)}function un(e){return e?e.flatMap(e=>e.type===n?un(e.children):[e]):[]}const dn=[`INPUT`,`TEXTAREA`];function fn(e,t,n,r={}){if(!t||r.enableIgnoredElement&&dn.includes(t.nodeName))return null;let{arrowKeyOptions:i=`both`,attributeName:a=`[data-reka-collection-item]`,itemsArray:o=[],loop:s=!0,dir:c=`ltr`,preventScroll:l=!0,focus:u=!1}=r,[d,f,p,m,h,g]=[e.key===`ArrowRight`,e.key===`ArrowLeft`,e.key===`ArrowUp`,e.key===`ArrowDown`,e.key===`Home`,e.key===`End`],_=p||m,v=d||f;if(!h&&!g&&(!_&&!v||i===`vertical`&&v||i===`horizontal`&&_))return null;let y=n?Array.from(n.querySelectorAll(a)):o;if(!y.length)return null;l&&e.preventDefault();let b=null;return v||_?b=pn(y,t,{goForward:_?m:c===`ltr`?d:f,loop:s}):h?b=y.at(0)||null:g&&(b=y.at(-1)||null),u&&b?.focus(),b}function pn(e,t,n,r=e.length){if(--r===0)return null;let i=e.indexOf(t),a=n.goForward?i+1:i-1;if(!n.loop&&(a<0||a>=e.length))return null;let o=(a+e.length)%e.length,s=e[o];return s?s.hasAttribute(`disabled`)&&s.getAttribute(`disabled`)!==`false`?pn(e,s,n,r):s:null}const[mn,hn]=J(`ConfigProvider`);function gn(e){if(typeof e!=`object`||!e)return!1;let t=Object.getPrototypeOf(e);return t!==null&&t!==Object.prototype&&Object.getPrototypeOf(t)!==null||Symbol.iterator in e?!1:Symbol.toStringTag in e?Object.prototype.toString.call(e)===`[object Module]`:!0}function _n(e,t,n=`.`,r){if(!gn(t))return _n(e,{},n,r);let i=Object.assign({},t);for(let t in e){if(t===`__proto__`||t===`constructor`)continue;let a=e[t];if(a==null||r&&r(i,t,a,n))continue;Array.isArray(a)&&Array.isArray(i[t])?i[t]=[...a,...i[t]]:gn(a)&&gn(i[t])?i[t]=_n(a,i[t],(n?`${n}.`:``)+t.toString(),r):i[t]=a}return i}function vn(e){return(...t)=>t.reduce((t,n)=>_n(t,n,``,e),{})}const yn=vn(),bn=fe(()=>{let e=j(new Map),t=j(),n=o(()=>{for(let t of e.value.values())if(t)return!0;return!1}),r=mn({scrollBody:j(!0)}),i=null,a=()=>{document.body.style.paddingRight=``,document.body.style.marginRight=``,document.body.style.pointerEvents=``,document.documentElement.style.removeProperty(`--scrollbar-width`),document.body.style.overflow=t.value??``,we&&i?.(),t.value=void 0};return R(n,(e,n)=>{if(!Ce)return;if(!e){n&&a();return}t.value===void 0&&(t.value=document.body.style.overflow);let o=window.innerWidth-document.documentElement.clientWidth,s={padding:o,margin:0},c=r.scrollBody?.value?typeof r.scrollBody.value==`object`?yn({padding:r.scrollBody.value.padding===!0?o:r.scrollBody.value.padding,margin:r.scrollBody.value.margin===!0?o:r.scrollBody.value.margin},s):s:{padding:0,margin:0};o>0&&(document.body.style.paddingRight=typeof c.padding==`number`?`${c.padding}px`:String(c.padding),document.body.style.marginRight=typeof c.margin==`number`?`${c.margin}px`:String(c.margin),document.documentElement.style.setProperty(`--scrollbar-width`,`${o}px`),document.body.style.overflow=`hidden`),we&&(i=ve(document,`touchmove`,e=>Cn(e),{passive:!1})),S(()=>{document.body.style.pointerEvents=`none`,document.body.style.overflow=`hidden`})},{immediate:!0,flush:`sync`}),e});function xn(e){let t=Math.random().toString(36).substring(2,7),n=bn();n.value.set(t,e??!1);let r=o({get:()=>n.value.get(t)??!1,set:e=>n.value.set(t,e)});return De(()=>{n.value.delete(t)}),r}function Sn(e){let t=window.getComputedStyle(e);if(t.overflowX===`scroll`||t.overflowY===`scroll`||t.overflowX===`auto`&&e.clientWidth<e.scrollWidth||t.overflowY===`auto`&&e.clientHeight<e.scrollHeight)return!0;{let t=e.parentNode;return!(t instanceof Element)||t.tagName===`BODY`?!1:Sn(t)}}function Cn(e){let t=e||window.event,n=t.target;return n instanceof Element&&Sn(n)?!1:t.touches.length>1?!0:(t.preventDefault&&t.cancelable&&t.preventDefault(),!1)}function wn(e){let t=mn({dir:j(`ltr`)});return o(()=>e?.value||t.dir?.value||`ltr`)}function Tn(e){let t=m(),n=t?.type.emits,r={};return n?.length||console.warn(`No emitted event found. Please check component: ${t?.type.__name}`),n?.forEach(t=>{r[re(i(t))]=(...n)=>e(t,...n)}),r}let En=0;function Dn(){z(e=>{if(!Ce)return;let t=document.querySelectorAll(`[data-reka-focus-guard]`);document.body.insertAdjacentElement(`afterbegin`,t[0]??On()),document.body.insertAdjacentElement(`beforeend`,t[1]??On()),En++,e(()=>{En===1&&document.querySelectorAll(`[data-reka-focus-guard]`).forEach(e=>e.remove()),En--})})}function On(){let e=document.createElement(`span`);return e.setAttribute(`data-reka-focus-guard`,``),e.tabIndex=0,e.style.outline=`none`,e.style.opacity=`0`,e.style.position=`fixed`,e.style.pointerEvents=`none`,e}function kn(e){return o(()=>he(e)?!!ge(e)?.closest(`form`):!0)}function Y(){let e=m(),t=j(),n=o(()=>[`#text`,`#comment`].includes(t.value?.$el.nodeName)?t.value?.$el.nextElementSibling:ge(t)),r=Object.assign({},e.exposed),i={};for(let t in e.props)Object.defineProperty(i,t,{enumerable:!0,configurable:!0,get:()=>e.props[t]});if(Object.keys(r).length>0)for(let e in r)Object.defineProperty(i,e,{enumerable:!0,configurable:!0,get:()=>r[e]});Object.defineProperty(i,`$el`,{enumerable:!0,configurable:!0,get:()=>e.vnode.el}),e.exposed=i;function a(n){t.value=n,n&&(Object.defineProperty(i,`$el`,{enumerable:!0,configurable:!0,get:()=>n instanceof Element?n:n.$el}),e.exposed=i)}return{forwardRef:a,currentRef:t,currentElement:n}}function An(e){let t=m(),n=Object.keys(t?.type.props??{}).reduce((e,n)=>{let r=(t?.type.props[n]).default;return r!==void 0&&(e[n]=r),e},{}),r=ie(e);return o(()=>{let e={},a=t?.vnode.props??{};return Object.keys(a).forEach(t=>{e[i(t)]=a[t]}),Object.keys({...n,...e}).reduce((e,t)=>(r.value[t]!==void 0&&(e[t]=r.value[t]),e),{})})}function X(e,t){let n=An(e),r=t?Tn(t):{};return o(()=>({...n.value,...r}))}var jn=function(e){return typeof document>`u`?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},Mn=new WeakMap,Nn=new WeakMap,Pn={},Fn=0,In=function(e){return e&&(e.host||In(e.parentNode))},Ln=function(e,t){return t.map(function(t){if(e.contains(t))return t;var n=In(t);return n&&e.contains(n)?n:(console.error(`aria-hidden`,t,`in not contained inside`,e,`. Doing nothing`),null)}).filter(function(e){return!!e})},Rn=function(e,t,n,r){var i=Ln(t,Array.isArray(e)?e:[e]);Pn[n]||(Pn[n]=new WeakMap);var a=Pn[n],o=[],s=new Set,c=new Set(i),l=function(e){!e||s.has(e)||(s.add(e),l(e.parentNode))};i.forEach(l);var u=function(e){!e||c.has(e)||Array.prototype.forEach.call(e.children,function(e){if(s.has(e))u(e);else try{var t=e.getAttribute(r),i=t!==null&&t!==`false`,c=(Mn.get(e)||0)+1,l=(a.get(e)||0)+1;Mn.set(e,c),a.set(e,l),o.push(e),c===1&&i&&Nn.set(e,!0),l===1&&e.setAttribute(n,`true`),i||e.setAttribute(r,`true`)}catch(t){console.error(`aria-hidden: cannot operate on `,e,t)}})};return u(t),s.clear(),Fn++,function(){o.forEach(function(e){var t=Mn.get(e)-1,i=a.get(e)-1;Mn.set(e,t),a.set(e,i),t||(Nn.has(e)||e.removeAttribute(r),Nn.delete(e)),i||e.removeAttribute(n)}),Fn--,Fn||(Mn=new WeakMap,Mn=new WeakMap,Nn=new WeakMap,Pn={})}},zn=function(e,t,n){n===void 0&&(n=`data-aria-hidden`);var r=Array.from(Array.isArray(e)?e:[e]),i=t||jn(e);return i?(r.push.apply(r,Array.from(i.querySelectorAll(`[aria-live], script`))),Rn(r,i,n,`aria-hidden`)):function(){return null}};function Bn(e){let t;R(()=>ge(e),e=>{e?t=zn(e):t&&t()}),O(()=>{t&&t()})}let Vn=0;function Hn(t,n=`reka`){if(t)return t;if(`useId`in e)return`${n}-${e.useId?.()}`;let r=mn({useId:void 0});return r.useId?`${n}-${r.useId()}`:`${n}-${++Vn}`}function Un(e){let t=mn({locale:j(`en`)});return o(()=>e?.value||t.locale?.value||`en`)}function Wn(e){let t=j(),n=o(()=>t.value?.width??0),r=o(()=>t.value?.height??0);return D(()=>{let n=ge(e);if(n){t.value={width:n.offsetWidth,height:n.offsetHeight};let e=new ResizeObserver(e=>{if(!Array.isArray(e)||!e.length)return;let r=e[0],i,a;if(`borderBoxSize`in r){let e=r.borderBoxSize,t=Array.isArray(e)?e[0]:e;i=t.inlineSize,a=t.blockSize}else i=n.offsetWidth,a=n.offsetHeight;t.value={width:i,height:a}});return e.observe(n,{box:`border-box`}),()=>e.unobserve(n)}else t.value=void 0}),{width:n,height:r}}function Gn(e,t){let n=j(e);function r(e){return t[n.value][e]??n.value}return{state:n,dispatch:e=>{n.value=r(e)}}}function Kn(e){let t=Ee(``,1e3);return{search:t,handleTypeaheadSearch:(n,r)=>{if(t.value+=n,e)e(n);else{let e=on(),n=r.map(e=>({...e,textValue:e.value?.textValue??e.ref.textContent?.trim()??``})),i=n.find(t=>t.ref===e),a=n.map(e=>e.textValue),o=Jn(a,t.value,i?.textValue),s=n.find(e=>e.textValue===o);return s&&s.ref.focus(),s?.ref}},resetTypeahead:()=>{t.value=``}}}function qn(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function Jn(e,t,n){let r=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=qn(e,Math.max(i,0));r.length===1&&(a=a.filter(e=>e!==n));let o=a.find(e=>e.toLowerCase().startsWith(r.toLowerCase()));return o===n?void 0:o}function Yn(e,t){let n=j({}),r=j(`none`),i=j(e),a=e.value?`mounted`:`unmounted`,s,c=t.value?.ownerDocument.defaultView??pe,{state:l,dispatch:u}=Gn(a,{mounted:{UNMOUNT:`unmounted`,ANIMATION_OUT:`unmountSuspended`},unmountSuspended:{MOUNT:`mounted`,ANIMATION_END:`unmounted`},unmounted:{MOUNT:`mounted`}}),d=e=>{if(Ce){let n=new CustomEvent(e,{bubbles:!1,cancelable:!1});t.value?.dispatchEvent(n)}};R(e,async(e,i)=>{let a=i!==e;if(await S(),a){let a=r.value,o=Xn(t.value);e?(u(`MOUNT`),d(`enter`),o===`none`&&d(`after-enter`)):o===`none`||o===`undefined`||n.value?.display===`none`?(u(`UNMOUNT`),d(`leave`),d(`after-leave`)):i&&a!==o?(u(`ANIMATION_OUT`),d(`leave`)):(u(`UNMOUNT`),d(`after-leave`))}},{immediate:!0});let f=e=>{let n=Xn(t.value),r=n.includes(e.animationName),a=l.value===`mounted`?`enter`:`leave`;if(e.target===t.value&&r&&(d(`after-${a}`),u(`ANIMATION_END`),!i.value)){let e=t.value.style.animationFillMode;t.value.style.animationFillMode=`forwards`,s=c?.setTimeout(()=>{t.value?.style.animationFillMode===`forwards`&&(t.value.style.animationFillMode=e)})}e.target===t.value&&n===`none`&&u(`ANIMATION_END`)},p=e=>{e.target===t.value&&(r.value=Xn(t.value))},m=R(t,(e,t)=>{e?(n.value=getComputedStyle(e),e.addEventListener(`animationstart`,p),e.addEventListener(`animationcancel`,f),e.addEventListener(`animationend`,f)):(u(`ANIMATION_END`),s!==void 0&&c?.clearTimeout(s),t?.removeEventListener(`animationstart`,p),t?.removeEventListener(`animationcancel`,f),t?.removeEventListener(`animationend`,f))},{immediate:!0}),h=R(l,()=>{let e=Xn(t.value);r.value=l.value===`mounted`?e:`none`});return O(()=>{m(),h()}),{isPresent:o(()=>[`mounted`,`unmountSuspended`].includes(l.value))}}function Xn(e){return e&&getComputedStyle(e).animationName||`none`}var Zn=p({name:`Presence`,props:{present:{type:Boolean,required:!0},forceMount:{type:Boolean}},slots:{},setup(e,{slots:t,expose:n}){let{present:r,forceMount:i}=F(e),a=j(),{isPresent:o}=Yn(r,a);n({present:o});let s=t.default({present:o.value});s=un(s||[]);let c=m();if(s&&s?.length>1){let e=c?.parent?.type.name?`<${c.parent.type.name} />`:`component`;throw Error([`Detected an invalid children for \`${e}\` for  \`Presence\` component.`,``,"Note: Presence works similarly to `v-if` directly, but it waits for animation/transition to finished before unmounting. So it expect only one direct child of valid VNode type.",`You can apply a few solutions:`,["Provide a single child element so that `presence` directive attach correctly.",`Ensure the first child is an actual element instead of a raw text node or comment node.`].map(e=>`  - ${e}`).join(`
`)].join(`
`))}return()=>i.value||r.value||o.value?g(t.default({present:o.value})[0],{ref:e=>{let t=ge(e);return t?.hasAttribute===void 0||(t?.hasAttribute(`data-reka-popper-content-wrapper`)?a.value=t.firstElementChild:a.value=t),t}}):null}});const Qn=p({name:`PrimitiveSlot`,inheritAttrs:!1,setup(e,{attrs:n,slots:r}){return()=>{if(!r.default)return null;let e=un(r.default()),i=e.findIndex(e=>e.type!==t);if(i===-1)return e;let o=e[i];delete o.props?.ref;let s=o.props?x(n,o.props):n,c=a({...o,props:{}},s);return e.length===1?c:(e[i]=c,e)}}}),$n=[`area`,`img`,`input`],Z=p({name:`Primitive`,inheritAttrs:!1,props:{asChild:{type:Boolean,default:!1},as:{type:[String,Object],default:`div`}},setup(e,{attrs:t,slots:n}){let r=e.asChild?`template`:e.as;return typeof r==`string`&&$n.includes(r)?()=>g(r,t):r===`template`?()=>g(Qn,t,{default:n.default}):()=>g(e.as,t,{default:n.default})}});function er(){let e=j(),t=o(()=>[`#text`,`#comment`].includes(e.value?.$el.nodeName)?e.value?.$el.nextElementSibling:ge(e));return{primitiveElement:e,currentElement:t}}const[tr,nr]=J(`DialogRoot`);var rr=p({inheritAttrs:!1,__name:`DialogRoot`,props:{open:{type:Boolean,required:!1,default:void 0},defaultOpen:{type:Boolean,required:!1,default:!1},modal:{type:Boolean,required:!1,default:!0}},emits:[`update:open`],setup(e,{emit:t}){let n=e,r=xe(n,`open`,t,{defaultValue:n.defaultOpen,passive:n.open===void 0}),i=j(),a=j(),{modal:o}=F(n);return nr({open:r,modal:o,openModal:()=>{r.value=!0},onOpenChange:e=>{r.value=e},onOpenToggle:()=>{r.value=!r.value},contentId:``,titleId:``,descriptionId:``,triggerElement:i,contentElement:a}),(e,t)=>N(e.$slots,`default`,{open:I(r),close:()=>r.value=!1})}}),ir=p({__name:`DialogClose`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`button`}},setup(e){let t=e;Y();let n=tr();return(e,r)=>(k(),s(I(Z),x(t,{type:e.as===`button`?`button`:void 0,onClick:r[0]||=e=>I(n).onOpenChange(!1)}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`type`]))}});function ar(e,t){let n=t.closest(`[data-dismissable-layer]`),r=e.dataset.dismissableLayer===``?e:e.querySelector(`[data-dismissable-layer]`),i=Array.from(e.ownerDocument.querySelectorAll(`[data-dismissable-layer]`));return!!(n&&(r===n||i.indexOf(r)<i.indexOf(n)))}function or(e,t,n=!0){let r=t?.value?.ownerDocument??globalThis?.document,i=j(!1),a=j(()=>{});return z(o=>{if(!Ce||!ae(n))return;let s=async n=>{let o=n.target;if(!(!t?.value||!o)){if(ar(t.value,o)){i.value=!1;return}if(n.target&&!i.value){let t={originalEvent:n};function i(){sn(`dismissableLayer.pointerDownOutside`,e,t)}n.pointerType===`touch`?(r.removeEventListener(`click`,a.value),a.value=i,r.addEventListener(`click`,a.value,{once:!0})):i()}else r.removeEventListener(`click`,a.value);i.value=!1}},c=window.setTimeout(()=>{r.addEventListener(`pointerdown`,s)},0);o(()=>{window.clearTimeout(c),r.removeEventListener(`pointerdown`,s),r.removeEventListener(`click`,a.value)})}),{onPointerDownCapture:()=>{ae(n)&&(i.value=!0)}}}function sr(e,t,n=!0){let r=t?.value?.ownerDocument??globalThis?.document,i=j(!1);return z(a=>{if(!Ce||!ae(n))return;let o=async n=>{if(!t?.value)return;await S(),await S();let r=n.target;!t.value||!r||ar(t.value,r)||n.target&&!i.value&&sn(`dismissableLayer.focusOutside`,e,{originalEvent:n})};r.addEventListener(`focusin`,o),a(()=>r.removeEventListener(`focusin`,o))}),{onFocusCapture:()=>{ae(n)&&(i.value=!0)},onBlurCapture:()=>{ae(n)&&(i.value=!1)}}}const cr=ee({layersRoot:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set});var lr=p({__name:`DismissableLayer`,props:{disableOutsidePointerEvents:{type:Boolean,required:!1,default:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`escapeKeyDown`,`pointerDownOutside`,`focusOutside`,`interactOutside`,`dismiss`],setup(e,{emit:t}){let n=e,r=t,{forwardRef:i,currentElement:a}=Y(),c=o(()=>a.value?.ownerDocument??globalThis.document),l=o(()=>cr.layersRoot),u=o(()=>a.value?Array.from(l.value).indexOf(a.value):-1),d=o(()=>cr.layersWithOutsidePointerEventsDisabled.size>0),f=o(()=>{let e=Array.from(l.value),[t]=[...cr.layersWithOutsidePointerEventsDisabled].slice(-1),n=e.indexOf(t);return u.value>=n}),p=or(async e=>{let t=[...cr.branches].some(t=>t?.contains(e.target));!f.value||t||(r(`pointerDownOutside`,e),r(`interactOutside`,e),await S(),e.defaultPrevented||r(`dismiss`))},a),m=sr(e=>{[...cr.branches].some(t=>t?.contains(e.target))||(r(`focusOutside`,e),r(`interactOutside`,e),e.defaultPrevented||r(`dismiss`))},a);me(`Escape`,e=>{u.value===l.value.size-1&&(r(`escapeKeyDown`,e),e.defaultPrevented||r(`dismiss`))});let h;return z(e=>{a.value&&(n.disableOutsidePointerEvents&&(cr.layersWithOutsidePointerEventsDisabled.size===0&&(h=c.value.body.style.pointerEvents,c.value.body.style.pointerEvents=`none`),cr.layersWithOutsidePointerEventsDisabled.add(a.value)),l.value.add(a.value),e(()=>{n.disableOutsidePointerEvents&&cr.layersWithOutsidePointerEventsDisabled.size===1&&(c.value.body.style.pointerEvents=h)}))}),z(e=>{e(()=>{a.value&&(l.value.delete(a.value),cr.layersWithOutsidePointerEventsDisabled.delete(a.value))})}),(e,t)=>(k(),s(I(Z),{ref:I(i),"as-child":e.asChild,as:e.as,"data-dismissable-layer":``,style:T({pointerEvents:d.value?f.value?`auto`:`none`:void 0}),onFocusCapture:I(m).onFocusCapture,onBlurCapture:I(m).onBlurCapture,onPointerdownCapture:I(p).onPointerDownCapture},{default:B(()=>[N(e.$slots,`default`)]),_:3},8,[`as-child`,`as`,`style`,`onFocusCapture`,`onBlurCapture`,`onPointerdownCapture`]))}});const ur=de(()=>j([]));function dr(){let e=ur();return{add(t){let n=e.value[0];t!==n&&n?.pause(),e.value=fr(e.value,t),e.value.unshift(t)},remove(t){e.value=fr(e.value,t),e.value[0]?.resume()}}}function fr(e,t){let n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function pr(e){return e.filter(e=>e.tagName!==`A`)}const mr=`focusScope.autoFocusOnMount`,hr=`focusScope.autoFocusOnUnmount`,gr={bubbles:!1,cancelable:!0};function _r(e,{select:t=!1}={}){let n=on();for(let r of e)if(Cr(r,{select:t}),on()!==n)return!0}function vr(e){let t=yr(e),n=br(t,e),r=br(t.reverse(),e);return[n,r]}function yr(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t=e.tagName===`INPUT`&&e.type===`hidden`;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function br(e,t){for(let n of e)if(!xr(n,{upTo:t}))return n}function xr(e,{upTo:t}){if(getComputedStyle(e).visibility===`hidden`)return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display===`none`)return!0;e=e.parentElement}return!1}function Sr(e){return e instanceof HTMLInputElement&&`select`in e}function Cr(e,{select:t=!1}={}){if(e&&e.focus){let n=on();e.focus({preventScroll:!0}),e!==n&&Sr(e)&&t&&e.select()}}var wr=p({__name:`FocusScope`,props:{loop:{type:Boolean,required:!1,default:!1},trapped:{type:Boolean,required:!1,default:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`mountAutoFocus`,`unmountAutoFocus`],setup(e,{emit:t}){let n=e,r=t,{currentRef:i,currentElement:a}=Y(),o=j(null),c=dr(),l=ee({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}});z(e=>{if(!Ce)return;let t=a.value;if(!n.trapped)return;function r(e){if(l.paused||!t)return;let n=e.target;t.contains(n)?o.value=n:Cr(o.value,{select:!0})}function i(e){if(l.paused||!t)return;let n=e.relatedTarget;n!==null&&(t.contains(n)||Cr(o.value,{select:!0}))}function s(e){t.contains(o.value)||Cr(t)}document.addEventListener(`focusin`,r),document.addEventListener(`focusout`,i);let c=new MutationObserver(s);t&&c.observe(t,{childList:!0,subtree:!0}),e(()=>{document.removeEventListener(`focusin`,r),document.removeEventListener(`focusout`,i),c.disconnect()})}),z(async e=>{let t=a.value;if(await S(),!t)return;c.add(l);let n=on();if(!t.contains(n)){let e=new CustomEvent(mr,gr);t.addEventListener(mr,e=>r(`mountAutoFocus`,e)),t.dispatchEvent(e),e.defaultPrevented||(_r(pr(yr(t)),{select:!0}),on()===n&&Cr(t))}e(()=>{t.removeEventListener(mr,e=>r(`mountAutoFocus`,e));let e=new CustomEvent(hr,gr),i=e=>{r(`unmountAutoFocus`,e)};t.addEventListener(hr,i),t.dispatchEvent(e),setTimeout(()=>{e.defaultPrevented||Cr(n??document.body,{select:!0}),t.removeEventListener(hr,i),c.remove(l)},0)})});function u(e){if(!n.loop&&!n.trapped||l.paused)return;let t=e.key===`Tab`&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=on();if(t&&r){let t=e.currentTarget,[i,a]=vr(t);i&&a?!e.shiftKey&&r===a?(e.preventDefault(),n.loop&&Cr(i,{select:!0})):e.shiftKey&&r===i&&(e.preventDefault(),n.loop&&Cr(a,{select:!0})):r===t&&e.preventDefault()}}return(e,t)=>(k(),s(I(Z),{ref_key:`currentRef`,ref:i,tabindex:`-1`,"as-child":e.asChild,as:e.as,onKeydown:u},{default:B(()=>[N(e.$slots,`default`)]),_:3},8,[`as-child`,`as`]))}});const Tr=[`Enter`,` `],Er=[`ArrowDown`,`PageUp`,`Home`],Dr=[`ArrowUp`,`PageDown`,`End`],Or=[...Er,...Dr],kr={ltr:[...Tr,`ArrowRight`],rtl:[...Tr,`ArrowLeft`]},Ar={ltr:[`ArrowLeft`],rtl:[`ArrowRight`]};function jr(e){return e?`open`:`closed`}function Mr(e){return e===`indeterminate`}function Nr(e){return Mr(e)?`indeterminate`:e?`checked`:`unchecked`}function Pr(e){let t=on();for(let n of e)if(n===t||(n.focus(),on()!==t))return}function Fr(e,t){let{x:n,y:r}=e,i=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let o=t[e].x,s=t[e].y,c=t[a].x,l=t[a].y;s>r!=l>r&&n<(c-o)*(r-s)/(l-s)+o&&(i=!i)}return i}function Ir(e,t){if(!t)return!1;let n={x:e.clientX,y:e.clientY};return Fr(n,t)}function Lr(e){return e.pointerType===`mouse`}var Rr=p({__name:`DialogContentImpl`,props:{forceMount:{type:Boolean,required:!1},trapFocus:{type:Boolean,required:!1},disableOutsidePointerEvents:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`escapeKeyDown`,`pointerDownOutside`,`focusOutside`,`interactOutside`,`openAutoFocus`,`closeAutoFocus`],setup(e,{emit:t}){let n=e,r=t,i=tr(),{forwardRef:a,currentElement:o}=Y();return i.titleId||=Hn(void 0,`reka-dialog-title`),i.descriptionId||=Hn(void 0,`reka-dialog-description`),D(()=>{i.contentElement=o,on()!==document.body&&(i.triggerElement.value=on())}),(e,t)=>(k(),s(I(wr),{"as-child":``,loop:``,trapped:n.trapFocus,onMountAutoFocus:t[5]||=e=>r(`openAutoFocus`,e),onUnmountAutoFocus:t[6]||=e=>r(`closeAutoFocus`,e)},{default:B(()=>[f(I(lr),x({id:I(i).contentId,ref:I(a),as:e.as,"as-child":e.asChild,"disable-outside-pointer-events":e.disableOutsidePointerEvents,role:`dialog`,"aria-describedby":I(i).descriptionId,"aria-labelledby":I(i).titleId,"data-state":I(jr)(I(i).open.value)},e.$attrs,{onDismiss:t[0]||=e=>I(i).onOpenChange(!1),onEscapeKeyDown:t[1]||=e=>r(`escapeKeyDown`,e),onFocusOutside:t[2]||=e=>r(`focusOutside`,e),onInteractOutside:t[3]||=e=>r(`interactOutside`,e),onPointerDownOutside:t[4]||=e=>r(`pointerDownOutside`,e)}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`id`,`as`,`as-child`,`disable-outside-pointer-events`,`aria-describedby`,`aria-labelledby`,`data-state`])]),_:3},8,[`trapped`]))}}),zr=p({__name:`DialogContentModal`,props:{forceMount:{type:Boolean,required:!1},trapFocus:{type:Boolean,required:!1},disableOutsidePointerEvents:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`escapeKeyDown`,`pointerDownOutside`,`focusOutside`,`interactOutside`,`openAutoFocus`,`closeAutoFocus`],setup(e,{emit:t}){let n=e,r=t,i=tr(),a=Tn(r),{forwardRef:o,currentElement:c}=Y();return Bn(c),(e,t)=>(k(),s(Rr,x({...n,...I(a)},{ref:I(o),"trap-focus":I(i).open.value,"disable-outside-pointer-events":!0,onCloseAutoFocus:t[0]||=e=>{e.defaultPrevented||(e.preventDefault(),I(i).triggerElement.value?.focus())},onPointerDownOutside:t[1]||=e=>{let t=e.detail.originalEvent,n=t.button===0&&t.ctrlKey===!0;(t.button===2||n)&&e.preventDefault()},onFocusOutside:t[2]||=e=>{e.preventDefault()}}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`trap-focus`]))}}),Br=p({__name:`DialogContentNonModal`,props:{forceMount:{type:Boolean,required:!1},trapFocus:{type:Boolean,required:!1},disableOutsidePointerEvents:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`escapeKeyDown`,`pointerDownOutside`,`focusOutside`,`interactOutside`,`openAutoFocus`,`closeAutoFocus`],setup(e,{emit:t}){let n=e,r=Tn(t);Y();let i=tr(),a=j(!1),o=j(!1);return(e,t)=>(k(),s(Rr,x({...n,...I(r)},{"trap-focus":!1,"disable-outside-pointer-events":!1,onCloseAutoFocus:t[0]||=e=>{e.defaultPrevented||(a.value||I(i).triggerElement.value?.focus(),e.preventDefault()),a.value=!1,o.value=!1},onInteractOutside:t[1]||=e=>{e.defaultPrevented||(a.value=!0,e.detail.originalEvent.type===`pointerdown`&&(o.value=!0));let t=e.target;I(i).triggerElement.value?.contains(t)&&e.preventDefault(),e.detail.originalEvent.type===`focusin`&&o.value&&e.preventDefault()}}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),Vr=p({__name:`DialogContent`,props:{forceMount:{type:Boolean,required:!1},disableOutsidePointerEvents:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`escapeKeyDown`,`pointerDownOutside`,`focusOutside`,`interactOutside`,`openAutoFocus`,`closeAutoFocus`],setup(e,{emit:t}){let n=e,r=t,i=tr(),a=Tn(r),{forwardRef:o}=Y();return(e,t)=>(k(),s(I(Zn),{present:e.forceMount||I(i).open.value},{default:B(()=>[I(i).modal.value?(k(),s(zr,x({key:0,ref:I(o)},{...n,...I(a),...e.$attrs}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16)):(k(),s(Br,x({key:1,ref:I(o)},{...n,...I(a),...e.$attrs}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))]),_:3},8,[`present`]))}}),Hr=p({__name:`DialogDescription`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`p`}},setup(e){let t=e;Y();let n=tr();return(e,r)=>(k(),s(I(Z),x(t,{id:I(n).descriptionId}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`id`]))}}),Ur=p({__name:`DialogOverlayImpl`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=tr();return xn(!0),Y(),(e,n)=>(k(),s(I(Z),{as:e.as,"as-child":e.asChild,"data-state":I(t).open.value?`open`:`closed`,style:{"pointer-events":`auto`}},{default:B(()=>[N(e.$slots,`default`)]),_:3},8,[`as`,`as-child`,`data-state`]))}}),Wr=p({__name:`DialogOverlay`,props:{forceMount:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=tr(),{forwardRef:n}=Y();return(e,r)=>I(t)?.modal.value?(k(),s(I(Zn),{key:0,present:e.forceMount||I(t).open.value},{default:B(()=>[f(Ur,x(e.$attrs,{ref:I(n),as:e.as,"as-child":e.asChild}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`as`,`as-child`])]),_:3},8,[`present`])):c(`v-if`,!0)}}),Gr=p({__name:`Teleport`,props:{to:{type:null,required:!1,default:`body`},disabled:{type:Boolean,required:!1},defer:{type:Boolean,required:!1},forceMount:{type:Boolean,required:!1}},setup(e){let t=ye();return(e,n)=>I(t)||e.forceMount?(k(),s(r,{key:0,to:e.to,disabled:e.disabled,defer:e.defer},[N(e.$slots,`default`)],8,[`to`,`disabled`,`defer`])):c(`v-if`,!0)}}),Kr=p({__name:`DialogPortal`,props:{to:{type:null,required:!1},disabled:{type:Boolean,required:!1},defer:{type:Boolean,required:!1},forceMount:{type:Boolean,required:!1}},setup(e){let t=e;return(e,n)=>(k(),s(I(Gr),w(h(t)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),qr=p({__name:`DialogTitle`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`h2`}},setup(e){let t=e,n=tr();return Y(),(e,r)=>(k(),s(I(Z),x(t,{id:I(n).titleId}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`id`]))}}),Jr=p({__name:`DialogTrigger`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`button`}},setup(e){let t=e,n=tr(),{forwardRef:r,currentElement:i}=Y();return n.contentId||=Hn(void 0,`reka-dialog-content`),D(()=>{n.triggerElement.value=i.value}),(e,i)=>(k(),s(I(Z),x(t,{ref:I(r),type:e.as===`button`?`button`:void 0,"aria-haspopup":`dialog`,"aria-expanded":I(n).open.value||!1,"aria-controls":I(n).open.value?I(n).contentId:void 0,"data-state":I(n).open.value?`open`:`closed`,onClick:I(n).onOpenToggle}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`type`,`aria-expanded`,`aria-controls`,`data-state`,`onClick`]))}});const Yr=`data-reka-collection-item`;function Xr(e={}){let{key:t=``,isProvider:n=!1}=e,r=`${t}CollectionProvider`,i;if(n){let e=j(new Map);i={collectionRef:j(),itemMap:e},A(r,i)}else i=_(r);let a=(e=!1)=>{let t=i.collectionRef.value;if(!t)return[];let n=Array.from(t.querySelectorAll(`[${Yr}]`)),r=Array.from(i.itemMap.value.values()).sort((e,t)=>n.indexOf(e.ref)-n.indexOf(t.ref));return e?r:r.filter(e=>e.ref.dataset.disabled!==``)},s=p({name:`CollectionSlot`,setup(e,{slots:t}){let{primitiveElement:n,currentElement:r}=er();return R(r,()=>{i.collectionRef.value=r.value}),()=>g(Qn,{ref:n},t)}}),c=p({name:`CollectionItem`,inheritAttrs:!1,props:{value:{validator:()=>!0}},setup(e,{slots:t,attrs:n}){let{primitiveElement:r,currentElement:a}=er();return z(t=>{if(a.value){let n=y(a.value);i.itemMap.value.set(n,{ref:a.value,value:e.value}),t(()=>i.itemMap.value.delete(n))}}),()=>g(Qn,{...n,[Yr]:``,ref:r},t)}}),l=o(()=>Array.from(i.itemMap.value.values())),u=o(()=>i.itemMap.value.size);return{getItems:a,reactiveItems:l,itemMapSize:u,CollectionSlot:s,CollectionItem:c}}const Zr={bubbles:!1,cancelable:!0},Qr={ArrowLeft:`prev`,ArrowUp:`prev`,ArrowRight:`next`,ArrowDown:`next`,PageUp:`first`,Home:`first`,PageDown:`last`,End:`last`};function $r(e,t){return t===`rtl`?e===`ArrowLeft`?`ArrowRight`:e===`ArrowRight`?`ArrowLeft`:e:e}function ei(e,t,n){let r=$r(e.key,n);if(!(t===`vertical`&&[`ArrowLeft`,`ArrowRight`].includes(r))&&!(t===`horizontal`&&[`ArrowUp`,`ArrowDown`].includes(r)))return Qr[r]}function ti(e,t=!1){let n=on();for(let r of e)if(r===n||(r.focus({preventScroll:t}),on()!==n))return}function ni(e,t){return e.map((n,r)=>e[(t+r)%e.length])}const[ri,ii]=J(`RovingFocusGroup`);var ai=p({__name:`RovingFocusGroup`,props:{orientation:{type:String,required:!1,default:void 0},dir:{type:String,required:!1},loop:{type:Boolean,required:!1,default:!1},currentTabStopId:{type:[String,null],required:!1},defaultCurrentTabStopId:{type:String,required:!1},preventScrollOnEntryFocus:{type:Boolean,required:!1,default:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`entryFocus`,`update:currentTabStopId`],setup(e,{expose:t,emit:n}){let r=e,i=n,{loop:a,orientation:o,dir:c}=F(r),l=wn(c),u=xe(r,`currentTabStopId`,i,{defaultValue:r.defaultCurrentTabStopId,passive:r.currentTabStopId===void 0}),d=j(!1),p=j(!1),m=j(0),{getItems:h,CollectionSlot:g}=Xr({isProvider:!0});function _(e){let t=!p.value;if(e.currentTarget&&e.target===e.currentTarget&&t&&!d.value){let t=new CustomEvent(`rovingFocusGroup.onEntryFocus`,Zr);if(e.currentTarget.dispatchEvent(t),i(`entryFocus`,t),!t.defaultPrevented){let e=h().map(e=>e.ref).filter(e=>e.dataset.disabled!==``),t=e.find(e=>e.getAttribute(`data-active`)===``),n=e.find(e=>e.getAttribute(`data-highlighted`)===``),i=e.find(e=>e.id===u.value),a=[t,n,i,...e].filter(Boolean);ti(a,r.preventScrollOnEntryFocus)}}p.value=!1}function v(){setTimeout(()=>{p.value=!1},1)}return t({getItems:h}),ii({loop:a,dir:l,orientation:o,currentTabStopId:u,onItemFocus:e=>{u.value=e},onItemShiftTab:()=>{d.value=!0},onFocusableItemAdd:()=>{m.value++},onFocusableItemRemove:()=>{m.value--}}),(e,t)=>(k(),s(I(g),null,{default:B(()=>[f(I(Z),{tabindex:d.value||m.value===0?-1:0,"data-orientation":I(o),as:e.as,"as-child":e.asChild,dir:I(l),style:{outline:`none`},onMousedown:t[0]||=e=>p.value=!0,onMouseup:v,onFocus:_,onBlur:t[1]||=e=>d.value=!1},{default:B(()=>[N(e.$slots,`default`)]),_:3},8,[`tabindex`,`data-orientation`,`as`,`as-child`,`dir`])]),_:3}))}}),oi=p({__name:`RovingFocusItem`,props:{tabStopId:{type:String,required:!1},focusable:{type:Boolean,required:!1,default:!0},active:{type:Boolean,required:!1},allowShiftKey:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`span`}},setup(e){let t=e,n=ri(),r=Hn(),i=o(()=>t.tabStopId||r),a=o(()=>n.currentTabStopId.value===i.value),{getItems:c,CollectionItem:l}=Xr();D(()=>{t.focusable&&n.onFocusableItemAdd()}),O(()=>{t.focusable&&n.onFocusableItemRemove()});function u(e){if(e.key===`Tab`&&e.shiftKey){n.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let r=ei(e,n.orientation.value,n.dir.value);if(r!==void 0){if(e.metaKey||e.ctrlKey||e.altKey||!t.allowShiftKey&&e.shiftKey)return;e.preventDefault();let i=[...c().map(e=>e.ref).filter(e=>e.dataset.disabled!==``)];if(r===`last`)i.reverse();else if(r===`prev`||r===`next`){r===`prev`&&i.reverse();let t=i.indexOf(e.currentTarget);i=n.loop.value?ni(i,t+1):i.slice(t+1)}S(()=>ti(i))}}return(e,t)=>(k(),s(I(l),null,{default:B(()=>[f(I(Z),{tabindex:a.value?0:-1,"data-orientation":I(n).orientation.value,"data-active":e.active?``:void 0,"data-disabled":e.focusable?void 0:``,as:e.as,"as-child":e.asChild,onMousedown:t[0]||=t=>{e.focusable?I(n).onItemFocus(i.value):t.preventDefault()},onFocus:t[1]||=e=>I(n).onItemFocus(i.value),onKeydown:u},{default:B(()=>[N(e.$slots,`default`)]),_:3},8,[`tabindex`,`data-orientation`,`data-active`,`data-disabled`,`as`,`as-child`])]),_:3}))}}),si=p({__name:`VisuallyHidden`,props:{feature:{type:String,required:!1,default:`focusable`},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`span`}},setup(e){return(e,t)=>(k(),s(I(Z),{as:e.as,"as-child":e.asChild,"aria-hidden":e.feature===`focusable`?`true`:void 0,"data-hidden":e.feature===`fully-hidden`?``:void 0,tabindex:e.feature===`fully-hidden`?`-1`:void 0,style:{position:`absolute`,border:0,width:`1px`,height:`1px`,padding:0,margin:`-1px`,overflow:`hidden`,clip:`rect(0, 0, 0, 0)`,clipPath:`inset(50%)`,whiteSpace:`nowrap`,wordWrap:`normal`}},{default:B(()=>[N(e.$slots,`default`)]),_:3},8,[`as`,`as-child`,`aria-hidden`,`data-hidden`,`tabindex`]))}}),ci=p({inheritAttrs:!1,__name:`VisuallyHiddenInputBubble`,props:{name:{type:String,required:!0},value:{type:null,required:!0},checked:{type:Boolean,required:!1,default:void 0},required:{type:Boolean,required:!1},disabled:{type:Boolean,required:!1},feature:{type:String,required:!1,default:`fully-hidden`}},setup(e){let t=e,{primitiveElement:n,currentElement:r}=er(),i=o(()=>t.checked??t.value);return R(i,(e,t)=>{if(!r.value)return;let n=r.value,i=window.HTMLInputElement.prototype,a=Object.getOwnPropertyDescriptor(i,`value`).set;if(a&&e!==t){let t=new Event(`input`,{bubbles:!0}),r=new Event(`change`,{bubbles:!0});a.call(n,e),n.dispatchEvent(t),n.dispatchEvent(r)}}),(e,r)=>(k(),s(si,x({ref_key:`primitiveElement`,ref:n},{...t,...e.$attrs},{as:`input`}),null,16))}}),li=p({inheritAttrs:!1,__name:`VisuallyHiddenInput`,props:{name:{type:String,required:!0},value:{type:null,required:!0},checked:{type:Boolean,required:!1,default:void 0},required:{type:Boolean,required:!1},disabled:{type:Boolean,required:!1},feature:{type:String,required:!1,default:`fully-hidden`}},setup(e){let t=e,r=o(()=>typeof t.value==`object`&&Array.isArray(t.value)&&t.value.length===0&&t.required),i=o(()=>typeof t.value==`string`||typeof t.value==`number`||typeof t.value==`boolean`||t.value===null||t.value===void 0?[{name:t.name,value:t.value}]:typeof t.value==`object`&&Array.isArray(t.value)?t.value.flatMap((e,n)=>typeof e==`object`?Object.entries(e).map(([e,r])=>({name:`${t.name}[${n}][${e}]`,value:r})):{name:`${t.name}[${n}]`,value:e}):t.value!==null&&typeof t.value==`object`&&!Array.isArray(t.value)?Object.entries(t.value).map(([e,n])=>({name:`${t.name}[${e}]`,value:n})):[]);return(e,a)=>(k(),l(n,null,[c(` We render single input if it's required `),r.value?(k(),s(ci,x({key:e.name},{...t,...e.$attrs},{name:e.name,value:e.value}),null,16,[`name`,`value`])):(k(!0),l(n,{key:1},M(i.value,n=>(k(),s(ci,x({key:n.name},{ref_for:!0},{...t,...e.$attrs},{name:n.name,value:n.value}),null,16,[`name`,`value`]))),128))],2112))}});const[ui,di]=J(`CheckboxGroupRoot`);function fi(e){return e===`indeterminate`}function pi(e){return fi(e)?`indeterminate`:e?`checked`:`unchecked`}const[mi,hi]=J(`CheckboxRoot`);var gi=p({inheritAttrs:!1,__name:`CheckboxRoot`,props:{defaultValue:{type:[Boolean,String],required:!1},modelValue:{type:[Boolean,String,null],required:!1,default:void 0},disabled:{type:Boolean,required:!1},value:{type:null,required:!1,default:`on`},id:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`button`},name:{type:String,required:!1},required:{type:Boolean,required:!1}},emits:[`update:modelValue`],setup(e,{emit:t}){let n=e,r=t,{forwardRef:i,currentElement:a}=Y(),l=ui(null),u=xe(n,`modelValue`,r,{defaultValue:n.defaultValue,passive:n.modelValue===void 0}),d=o(()=>l?.disabled.value||n.disabled),f=o(()=>cn(l?.modelValue.value)?u.value===`indeterminate`?`indeterminate`:u.value:ln(l.modelValue.value,n.value));function p(){if(cn(l?.modelValue.value))u.value=fi(u.value)?!0:!u.value;else{let e=[...l.modelValue.value||[]];if(ln(e,n.value)){let t=e.findIndex(e=>$t(e,n.value));e.splice(t,1)}else e.push(n.value);l.modelValue.value=e}}let m=kn(a),h=o(()=>n.id&&a.value?document.querySelector(`[for="${n.id}"]`)?.innerText:void 0);return hi({disabled:d,state:f}),(e,t)=>(k(),s(te(I(l)?.rovingFocus.value?I(oi):I(Z)),x(e.$attrs,{id:e.id,ref:I(i),role:`checkbox`,"as-child":e.asChild,as:e.as,type:e.as===`button`?`button`:void 0,"aria-checked":I(fi)(f.value)?`mixed`:f.value,"aria-required":e.required,"aria-label":e.$attrs[`aria-label`]||h.value,"data-state":I(pi)(f.value),"data-disabled":d.value?``:void 0,disabled:d.value,focusable:I(l)?.rovingFocus.value?!d.value:void 0,onKeydown:le(V(()=>{},[`prevent`]),[`enter`]),onClick:p}),{default:B(()=>[N(e.$slots,`default`,{modelValue:I(u),state:f.value}),I(m)&&e.name&&!I(l)?(k(),s(I(li),{key:0,type:`checkbox`,checked:!!f.value,name:e.name,value:e.value,disabled:d.value,required:e.required},null,8,[`checked`,`name`,`value`,`disabled`,`required`])):c(`v-if`,!0)]),_:3},16,[`id`,`as-child`,`as`,`type`,`aria-checked`,`aria-required`,`aria-label`,`data-state`,`data-disabled`,`disabled`,`focusable`,`onKeydown`]))}}),_i=p({__name:`CheckboxIndicator`,props:{forceMount:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`span`}},setup(e){let{forwardRef:t}=Y(),n=mi();return(e,r)=>(k(),s(I(Zn),{present:e.forceMount||I(fi)(I(n).state.value)||I(n).state.value===!0},{default:B(()=>[f(I(Z),x({ref:I(t),"data-state":I(pi)(I(n).state.value),"data-disabled":I(n).disabled.value?``:void 0,style:{pointerEvents:`none`},"as-child":e.asChild,as:e.as},e.$attrs),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`data-state`,`data-disabled`,`as-child`,`as`])]),_:3},8,[`present`]))}});const[vi,yi]=J(`PopperRoot`);var bi=p({inheritAttrs:!1,__name:`PopperRoot`,setup(e){let t=j();return yi({anchor:t,onAnchorChange:e=>t.value=e}),(e,t)=>N(e.$slots,`default`)}}),xi=p({__name:`PopperAnchor`,props:{reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=e,{forwardRef:n,currentElement:r}=Y(),i=vi();return se(()=>{i.onAnchorChange(t.reference??r.value)}),(e,t)=>(k(),s(I(Z),{ref:I(n),as:e.as,"as-child":e.asChild},{default:B(()=>[N(e.$slots,`default`)]),_:3},8,[`as`,`as-child`]))}});function Si(e){return e!==null}function Ci(e){return{name:`transformOrigin`,options:e,fn(t){let{placement:n,rects:r,middlewareData:i}=t,a=i.arrow?.centerOffset!==0,o=a?0:e.arrowWidth,s=a?0:e.arrowHeight,[c,l]=wi(n),u={start:`0%`,center:`50%`,end:`100%`}[l],d=(i.arrow?.x??0)+o/2,f=(i.arrow?.y??0)+s/2,p=``,m=``;return c===`bottom`?(p=a?u:`${d}px`,m=`${-s}px`):c===`top`?(p=a?u:`${d}px`,m=`${r.floating.height+s}px`):c===`right`?(p=`${-s}px`,m=a?u:`${f}px`):c===`left`&&(p=`${r.floating.width+s}px`,m=a?u:`${f}px`),{data:{x:p,y:m}}}}}function wi(e){let[t,n=`center`]=e.split(`-`);return[t,n]}const Ti=[`top`,`right`,`bottom`,`left`],Ei=Math.min,Di=Math.max,Oi=Math.round,ki=Math.floor,Ai=e=>({x:e,y:e}),ji={left:`right`,right:`left`,bottom:`top`,top:`bottom`},Mi={start:`end`,end:`start`};function Ni(e,t,n){return Di(e,Ei(t,n))}function Pi(e,t){return typeof e==`function`?e(t):e}function Fi(e){return e.split(`-`)[0]}function Ii(e){return e.split(`-`)[1]}function Li(e){return e===`x`?`y`:`x`}function Ri(e){return e===`y`?`height`:`width`}const zi=new Set([`top`,`bottom`]);function Bi(e){return zi.has(Fi(e))?`y`:`x`}function Vi(e){return Li(Bi(e))}function Hi(e,t,n){n===void 0&&(n=!1);let r=Ii(e),i=Vi(e),a=Ri(i),o=i===`x`?r===(n?`end`:`start`)?`right`:`left`:r===`start`?`bottom`:`top`;return t.reference[a]>t.floating[a]&&(o=Zi(o)),[o,Zi(o)]}function Ui(e){let t=Zi(e);return[Wi(e),t,Wi(t)]}function Wi(e){return e.replace(/start|end/g,e=>Mi[e])}const Gi=[`left`,`right`],Ki=[`right`,`left`],qi=[`top`,`bottom`],Ji=[`bottom`,`top`];function Yi(e,t,n){switch(e){case`top`:case`bottom`:return n?t?Ki:Gi:t?Gi:Ki;case`left`:case`right`:return t?qi:Ji;default:return[]}}function Xi(e,t,n,r){let i=Ii(e),a=Yi(Fi(e),n===`start`,r);return i&&(a=a.map(e=>e+`-`+i),t&&(a=a.concat(a.map(Wi)))),a}function Zi(e){return e.replace(/left|right|bottom|top/g,e=>ji[e])}function Qi(e){return{top:0,right:0,bottom:0,left:0,...e}}function $i(e){return typeof e==`number`?{top:e,right:e,bottom:e,left:e}:Qi(e)}function ea(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function ta(e,t,n){let{reference:r,floating:i}=e,a=Bi(t),o=Vi(t),s=Ri(o),c=Fi(t),l=a===`y`,u=r.x+r.width/2-i.width/2,d=r.y+r.height/2-i.height/2,f=r[s]/2-i[s]/2,p;switch(c){case`top`:p={x:u,y:r.y-i.height};break;case`bottom`:p={x:u,y:r.y+r.height};break;case`right`:p={x:r.x+r.width,y:d};break;case`left`:p={x:r.x-i.width,y:d};break;default:p={x:r.x,y:r.y}}switch(Ii(t)){case`start`:p[o]-=f*(n&&l?-1:1);break;case`end`:p[o]+=f*(n&&l?-1:1);break}return p}const na=async(e,t,n)=>{let{placement:r=`bottom`,strategy:i=`absolute`,middleware:a=[],platform:o}=n,s=a.filter(Boolean),c=await(o.isRTL==null?void 0:o.isRTL(t)),l=await o.getElementRects({reference:e,floating:t,strategy:i}),{x:u,y:d}=ta(l,r,c),f=r,p={},m=0;for(let n=0;n<s.length;n++){let{name:a,fn:h}=s[n],{x:g,y:_,data:v,reset:y}=await h({x:u,y:d,initialPlacement:r,placement:f,strategy:i,middlewareData:p,rects:l,platform:o,elements:{reference:e,floating:t}});u=g??u,d=_??d,p={...p,[a]:{...p[a],...v}},y&&m<=50&&(m++,typeof y==`object`&&(y.placement&&(f=y.placement),y.rects&&(l=y.rects===!0?await o.getElementRects({reference:e,floating:t,strategy:i}):y.rects),{x:u,y:d}=ta(l,f,c)),n=-1)}return{x:u,y:d,placement:f,strategy:i,middlewareData:p}};async function ra(e,t){t===void 0&&(t={});let{x:n,y:r,platform:i,rects:a,elements:o,strategy:s}=e,{boundary:c=`clippingAncestors`,rootBoundary:l=`viewport`,elementContext:u=`floating`,altBoundary:d=!1,padding:f=0}=Pi(t,e),p=$i(f),m=o[d?u===`floating`?`reference`:`floating`:u],h=ea(await i.getClippingRect({element:await(i.isElement==null?void 0:i.isElement(m))??!0?m:m.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(o.floating)),boundary:c,rootBoundary:l,strategy:s})),g=u===`floating`?{x:n,y:r,width:a.floating.width,height:a.floating.height}:a.reference,_=await(i.getOffsetParent==null?void 0:i.getOffsetParent(o.floating)),v=await(i.isElement==null?void 0:i.isElement(_))&&await(i.getScale==null?void 0:i.getScale(_))||{x:1,y:1},y=ea(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:o,rect:g,offsetParent:_,strategy:s}):g);return{top:(h.top-y.top+p.top)/v.y,bottom:(y.bottom-h.bottom+p.bottom)/v.y,left:(h.left-y.left+p.left)/v.x,right:(y.right-h.right+p.right)/v.x}}const ia=e=>({name:`arrow`,options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:o,elements:s,middlewareData:c}=t,{element:l,padding:u=0}=Pi(e,t)||{};if(l==null)return{};let d=$i(u),f={x:n,y:r},p=Vi(i),m=Ri(p),h=await o.getDimensions(l),g=p===`y`,_=g?`top`:`left`,v=g?`bottom`:`right`,y=g?`clientHeight`:`clientWidth`,b=a.reference[m]+a.reference[p]-f[p]-a.floating[m],x=f[p]-a.reference[p],S=await(o.getOffsetParent==null?void 0:o.getOffsetParent(l)),C=S?S[y]:0;(!C||!await(o.isElement==null?void 0:o.isElement(S)))&&(C=s.floating[y]||a.floating[m]);let w=b/2-x/2,T=C/2-h[m]/2-1,E=Ei(d[_],T),D=Ei(d[v],T),O=E,k=C-h[m]-D,A=C/2-h[m]/2+w,ee=Ni(O,A,k),j=!c.arrow&&Ii(i)!=null&&A!==ee&&a.reference[m]/2-(A<O?E:D)-h[m]/2<0,M=j?A<O?A-O:A-k:0;return{[p]:f[p]+M,data:{[p]:ee,centerOffset:A-ee-M,...j&&{alignmentOffset:M}},reset:j}}}),aa=function(e){return e===void 0&&(e={}),{name:`flip`,options:e,async fn(t){var n;let{placement:r,middlewareData:i,rects:a,initialPlacement:o,platform:s,elements:c}=t,{mainAxis:l=!0,crossAxis:u=!0,fallbackPlacements:d,fallbackStrategy:f=`bestFit`,fallbackAxisSideDirection:p=`none`,flipAlignment:m=!0,...h}=Pi(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};let g=Fi(r),_=Bi(o),v=Fi(o)===o,y=await(s.isRTL==null?void 0:s.isRTL(c.floating)),b=d||(v||!m?[Zi(o)]:Ui(o)),x=p!==`none`;!d&&x&&b.push(...Xi(o,m,p,y));let S=[o,...b],C=await ra(t,h),w=[],T=i.flip?.overflows||[];if(l&&w.push(C[g]),u){let e=Hi(r,a,y);w.push(C[e[0]],C[e[1]])}if(T=[...T,{placement:r,overflows:w}],!w.every(e=>e<=0)){let e=(i.flip?.index||0)+1,t=S[e];if(t&&(!(u===`alignment`&&_!==Bi(t))||T.every(e=>Bi(e.placement)===_?e.overflows[0]>0:!0)))return{data:{index:e,overflows:T},reset:{placement:t}};let n=T.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0]?.placement;if(!n)switch(f){case`bestFit`:{let e=T.filter(e=>{if(x){let t=Bi(e.placement);return t===_||t===`y`}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0]?.[0];e&&(n=e);break}case`initialPlacement`:n=o;break}if(r!==n)return{reset:{placement:n}}}return{}}}};function oa(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function sa(e){return Ti.some(t=>e[t]>=0)}const ca=function(e){return e===void 0&&(e={}),{name:`hide`,options:e,async fn(t){let{rects:n}=t,{strategy:r=`referenceHidden`,...i}=Pi(e,t);switch(r){case`referenceHidden`:{let e=await ra(t,{...i,elementContext:`reference`}),r=oa(e,n.reference);return{data:{referenceHiddenOffsets:r,referenceHidden:sa(r)}}}case`escaped`:{let e=await ra(t,{...i,altBoundary:!0}),r=oa(e,n.floating);return{data:{escapedOffsets:r,escaped:sa(r)}}}default:return{}}}}},la=new Set([`left`,`top`]);async function ua(e,t){let{placement:n,platform:r,elements:i}=e,a=await(r.isRTL==null?void 0:r.isRTL(i.floating)),o=Fi(n),s=Ii(n),c=Bi(n)===`y`,l=la.has(o)?-1:1,u=a&&c?-1:1,d=Pi(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:m}=typeof d==`number`?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&typeof m==`number`&&(p=s===`end`?m*-1:m),c?{x:p*u,y:f*l}:{x:f*l,y:p*u}}const da=function(e){return e===void 0&&(e=0),{name:`offset`,options:e,async fn(t){var n;let{x:r,y:i,placement:a,middlewareData:o}=t,s=await ua(t,e);return a===o.offset?.placement&&(n=o.arrow)!=null&&n.alignmentOffset?{}:{x:r+s.x,y:i+s.y,data:{...s,placement:a}}}}},fa=function(e){return e===void 0&&(e={}),{name:`shift`,options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:o=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=Pi(e,t),l={x:n,y:r},u=await ra(t,c),d=Bi(Fi(i)),f=Li(d),p=l[f],m=l[d];if(a){let e=f===`y`?`top`:`left`,t=f===`y`?`bottom`:`right`,n=p+u[e],r=p-u[t];p=Ni(n,p,r)}if(o){let e=d===`y`?`top`:`left`,t=d===`y`?`bottom`:`right`,n=m+u[e],r=m-u[t];m=Ni(n,m,r)}let h=s.fn({...t,[f]:p,[d]:m});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[f]:a,[d]:o}}}}}},pa=function(e){return e===void 0&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:a,middlewareData:o}=t,{offset:s=0,mainAxis:c=!0,crossAxis:l=!0}=Pi(e,t),u={x:n,y:r},d=Bi(i),f=Li(d),p=u[f],m=u[d],h=Pi(s,t),g=typeof h==`number`?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(c){let e=f===`y`?`height`:`width`,t=a.reference[f]-a.floating[e]+g.mainAxis,n=a.reference[f]+a.reference[e]-g.mainAxis;p<t?p=t:p>n&&(p=n)}if(l){let e=f===`y`?`width`:`height`,t=la.has(Fi(i)),n=a.reference[d]-a.floating[e]+(t&&o.offset?.[d]||0)+(t?0:g.crossAxis),r=a.reference[d]+a.reference[e]+(t?0:o.offset?.[d]||0)-(t?g.crossAxis:0);m<n?m=n:m>r&&(m=r)}return{[f]:p,[d]:m}}}},ma=function(e){return e===void 0&&(e={}),{name:`size`,options:e,async fn(t){var n,r;let{placement:i,rects:a,platform:o,elements:s}=t,{apply:c=()=>{},...l}=Pi(e,t),u=await ra(t,l),d=Fi(i),f=Ii(i),p=Bi(i)===`y`,{width:m,height:h}=a.floating,g,_;d===`top`||d===`bottom`?(g=d,_=f===(await(o.isRTL==null?void 0:o.isRTL(s.floating))?`start`:`end`)?`left`:`right`):(_=d,g=f===`end`?`top`:`bottom`);let v=h-u.top-u.bottom,y=m-u.left-u.right,b=Ei(h-u[g],v),x=Ei(m-u[_],y),S=!t.middlewareData.shift,C=b,w=x;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(w=y),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(C=v),S&&!f){let e=Di(u.left,0),t=Di(u.right,0),n=Di(u.top,0),r=Di(u.bottom,0);p?w=m-2*(e!==0||t!==0?e+t:Di(u.left,u.right)):C=h-2*(n!==0||r!==0?n+r:Di(u.top,u.bottom))}await c({...t,availableWidth:w,availableHeight:C});let T=await o.getDimensions(s.floating);return m!==T.width||h!==T.height?{reset:{rects:!0}}:{}}}};function ha(){return typeof window<`u`}function ga(e){return ya(e)?(e.nodeName||``).toLowerCase():`#document`}function _a(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function va(e){return((ya(e)?e.ownerDocument:e.document)||window.document)?.documentElement}function ya(e){return ha()?e instanceof Node||e instanceof _a(e).Node:!1}function ba(e){return ha()?e instanceof Element||e instanceof _a(e).Element:!1}function xa(e){return ha()?e instanceof HTMLElement||e instanceof _a(e).HTMLElement:!1}function Sa(e){return!ha()||typeof ShadowRoot>`u`?!1:e instanceof ShadowRoot||e instanceof _a(e).ShadowRoot}const Ca=new Set([`inline`,`contents`]);function wa(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=La(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!Ca.has(i)}const Ta=new Set([`table`,`td`,`th`]);function Ea(e){return Ta.has(ga(e))}const Da=[`:popover-open`,`:modal`];function Oa(e){return Da.some(t=>{try{return e.matches(t)}catch{return!1}})}const ka=[`transform`,`translate`,`scale`,`rotate`,`perspective`],Aa=[`transform`,`translate`,`scale`,`rotate`,`perspective`,`filter`],ja=[`paint`,`layout`,`strict`,`content`];function Ma(e){let t=Pa(),n=ba(e)?La(e):e;return ka.some(e=>n[e]?n[e]!==`none`:!1)||(n.containerType?n.containerType!==`normal`:!1)||!t&&(n.backdropFilter?n.backdropFilter!==`none`:!1)||!t&&(n.filter?n.filter!==`none`:!1)||Aa.some(e=>(n.willChange||``).includes(e))||ja.some(e=>(n.contain||``).includes(e))}function Na(e){let t=za(e);for(;xa(t)&&!Ia(t);){if(Ma(t))return t;if(Oa(t))return null;t=za(t)}return null}function Pa(){return typeof CSS>`u`||!CSS.supports?!1:CSS.supports(`-webkit-backdrop-filter`,`none`)}const Fa=new Set([`html`,`body`,`#document`]);function Ia(e){return Fa.has(ga(e))}function La(e){return _a(e).getComputedStyle(e)}function Ra(e){return ba(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function za(e){if(ga(e)===`html`)return e;let t=e.assignedSlot||e.parentNode||Sa(e)&&e.host||va(e);return Sa(t)?t.host:t}function Ba(e){let t=za(e);return Ia(t)?e.ownerDocument?e.ownerDocument.body:e.body:xa(t)&&wa(t)?t:Ba(t)}function Va(e,t,n){t===void 0&&(t=[]),n===void 0&&(n=!0);let r=Ba(e),i=r===e.ownerDocument?.body,a=_a(r);if(i){let e=Ha(a);return t.concat(a,a.visualViewport||[],wa(r)?r:[],e&&n?Va(e):[])}return t.concat(r,Va(r,[],n))}function Ha(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Ua(e){let t=La(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=xa(e),a=i?e.offsetWidth:n,o=i?e.offsetHeight:r,s=Oi(n)!==a||Oi(r)!==o;return s&&(n=a,r=o),{width:n,height:r,$:s}}function Wa(e){return ba(e)?e:e.contextElement}function Ga(e){let t=Wa(e);if(!xa(t))return Ai(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:a}=Ua(t),o=(a?Oi(n.width):n.width)/r,s=(a?Oi(n.height):n.height)/i;return(!o||!Number.isFinite(o))&&(o=1),(!s||!Number.isFinite(s))&&(s=1),{x:o,y:s}}const Ka=Ai(0);function qa(e){let t=_a(e);return!Pa()||!t.visualViewport?Ka:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Ja(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==_a(e)?!1:t}function Ya(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);let i=e.getBoundingClientRect(),a=Wa(e),o=Ai(1);t&&(r?ba(r)&&(o=Ga(r)):o=Ga(e));let s=Ja(a,n,r)?qa(a):Ai(0),c=(i.left+s.x)/o.x,l=(i.top+s.y)/o.y,u=i.width/o.x,d=i.height/o.y;if(a){let e=_a(a),t=r&&ba(r)?_a(r):r,n=e,i=Ha(n);for(;i&&r&&t!==n;){let e=Ga(i),t=i.getBoundingClientRect(),r=La(i),a=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,o=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,l*=e.y,u*=e.x,d*=e.y,c+=a,l+=o,n=_a(i),i=Ha(n)}}return ea({width:u,height:d,x:c,y:l})}function Xa(e,t){let n=Ra(e).scrollLeft;return t?t.left+n:Ya(va(e)).left+n}function Za(e,t){let n=e.getBoundingClientRect(),r=n.left+t.scrollLeft-Xa(e,n),i=n.top+t.scrollTop;return{x:r,y:i}}function Qa(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,a=i===`fixed`,o=va(r),s=t?Oa(t.floating):!1;if(r===o||s&&a)return n;let c={scrollLeft:0,scrollTop:0},l=Ai(1),u=Ai(0),d=xa(r);if((d||!d&&!a)&&((ga(r)!==`body`||wa(o))&&(c=Ra(r)),xa(r))){let e=Ya(r);l=Ga(r),u.x=e.x+r.clientLeft,u.y=e.y+r.clientTop}let f=o&&!d&&!a?Za(o,c):Ai(0);return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-c.scrollLeft*l.x+u.x+f.x,y:n.y*l.y-c.scrollTop*l.y+u.y+f.y}}function $a(e){return Array.from(e.getClientRects())}function eo(e){let t=va(e),n=Ra(e),r=e.ownerDocument.body,i=Di(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),a=Di(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),o=-n.scrollLeft+Xa(e),s=-n.scrollTop;return La(r).direction===`rtl`&&(o+=Di(t.clientWidth,r.clientWidth)-i),{width:i,height:a,x:o,y:s}}function to(e,t){let n=_a(e),r=va(e),i=n.visualViewport,a=r.clientWidth,o=r.clientHeight,s=0,c=0;if(i){a=i.width,o=i.height;let e=Pa();(!e||e&&t===`fixed`)&&(s=i.offsetLeft,c=i.offsetTop)}let l=Xa(r);if(l<=0){let e=r.ownerDocument,t=e.body,n=getComputedStyle(t),i=e.compatMode===`CSS1Compat`&&parseFloat(n.marginLeft)+parseFloat(n.marginRight)||0,o=Math.abs(r.clientWidth-t.clientWidth-i);o<=25&&(a-=o)}else l<=25&&(a+=l);return{width:a,height:o,x:s,y:c}}const no=new Set([`absolute`,`fixed`]);function ro(e,t){let n=Ya(e,!0,t===`fixed`),r=n.top+e.clientTop,i=n.left+e.clientLeft,a=xa(e)?Ga(e):Ai(1),o=e.clientWidth*a.x,s=e.clientHeight*a.y,c=i*a.x,l=r*a.y;return{width:o,height:s,x:c,y:l}}function io(e,t,n){let r;if(t===`viewport`)r=to(e,n);else if(t===`document`)r=eo(va(e));else if(ba(t))r=ro(t,n);else{let n=qa(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return ea(r)}function ao(e,t){let n=za(e);return n===t||!ba(n)||Ia(n)?!1:La(n).position===`fixed`||ao(n,t)}function oo(e,t){let n=t.get(e);if(n)return n;let r=Va(e,[],!1).filter(e=>ba(e)&&ga(e)!==`body`),i=null,a=La(e).position===`fixed`,o=a?za(e):e;for(;ba(o)&&!Ia(o);){let t=La(o),n=Ma(o);!n&&t.position===`fixed`&&(i=null),(a?!n&&!i:!n&&t.position===`static`&&i&&no.has(i.position)||wa(o)&&!n&&ao(e,o))?r=r.filter(e=>e!==o):i=t,o=za(o)}return t.set(e,r),r}function so(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[...n===`clippingAncestors`?Oa(t)?[]:oo(t,this._c):[].concat(n),r],o=a[0],s=a.reduce((e,n)=>{let r=io(t,n,i);return e.top=Di(r.top,e.top),e.right=Ei(r.right,e.right),e.bottom=Ei(r.bottom,e.bottom),e.left=Di(r.left,e.left),e},io(t,o,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}}function co(e){let{width:t,height:n}=Ua(e);return{width:t,height:n}}function lo(e,t,n){let r=xa(t),i=va(t),a=n===`fixed`,o=Ya(e,!0,a,t),s={scrollLeft:0,scrollTop:0},c=Ai(0);function l(){c.x=Xa(i)}if(r||!r&&!a)if((ga(t)!==`body`||wa(i))&&(s=Ra(t)),r){let e=Ya(t,!0,a,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else i&&l();a&&!r&&i&&l();let u=i&&!r&&!a?Za(i,s):Ai(0),d=o.left+s.scrollLeft-c.x-u.x,f=o.top+s.scrollTop-c.y-u.y;return{x:d,y:f,width:o.width,height:o.height}}function uo(e){return La(e).position===`static`}function fo(e,t){if(!xa(e)||La(e).position===`fixed`)return null;if(t)return t(e);let n=e.offsetParent;return va(e)===n&&(n=n.ownerDocument.body),n}function po(e,t){let n=_a(e);if(Oa(e))return n;if(!xa(e)){let t=za(e);for(;t&&!Ia(t);){if(ba(t)&&!uo(t))return t;t=za(t)}return n}let r=fo(e,t);for(;r&&Ea(r)&&uo(r);)r=fo(r,t);return r&&Ia(r)&&uo(r)&&!Ma(r)?n:r||Na(e)||n}const mo=async function(e){let t=this.getOffsetParent||po,n=this.getDimensions,r=await n(e.floating);return{reference:lo(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function ho(e){return La(e).direction===`rtl`}const go={convertOffsetParentRelativeRectToViewportRelativeRect:Qa,getDocumentElement:va,getClippingRect:so,getOffsetParent:po,getElementRects:mo,getClientRects:$a,getDimensions:co,getScale:Ga,isElement:ba,isRTL:ho};function _o(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function vo(e,t){let n=null,r,i=va(e);function a(){var e;clearTimeout(r),(e=n)==null||e.disconnect(),n=null}function o(s,c){s===void 0&&(s=!1),c===void 0&&(c=1),a();let l=e.getBoundingClientRect(),{left:u,top:d,width:f,height:p}=l;if(s||t(),!f||!p)return;let m=ki(d),h=ki(i.clientWidth-(u+f)),g=ki(i.clientHeight-(d+p)),_=ki(u),v={rootMargin:-m+`px `+-h+`px `+-g+`px `+-_+`px`,threshold:Di(0,Ei(1,c))||1},y=!0;function b(t){let n=t[0].intersectionRatio;if(n!==c){if(!y)return o();n?o(!1,n):r=setTimeout(()=>{o(!1,1e-7)},1e3)}n===1&&!_o(l,e.getBoundingClientRect())&&o(),y=!1}try{n=new IntersectionObserver(b,{...v,root:i.ownerDocument})}catch{n=new IntersectionObserver(b,v)}n.observe(e)}return o(!0),a}function yo(e,t,n,r){r===void 0&&(r={});let{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:o=typeof ResizeObserver==`function`,layoutShift:s=typeof IntersectionObserver==`function`,animationFrame:c=!1}=r,l=Wa(e),u=i||a?[...l?Va(l):[],...Va(t)]:[];u.forEach(e=>{i&&e.addEventListener(`scroll`,n,{passive:!0}),a&&e.addEventListener(`resize`,n)});let d=l&&s?vo(l,n):null,f=-1,p=null;o&&(p=new ResizeObserver(e=>{let[r]=e;r&&r.target===l&&p&&(p.unobserve(t),cancelAnimationFrame(f),f=requestAnimationFrame(()=>{var e;(e=p)==null||e.observe(t)})),n()}),l&&!c&&p.observe(l),p.observe(t));let m,h=c?Ya(e):null;c&&g();function g(){let t=Ya(e);h&&!_o(h,t)&&n(),h=t,m=requestAnimationFrame(g)}return n(),()=>{var e;u.forEach(e=>{i&&e.removeEventListener(`scroll`,n),a&&e.removeEventListener(`resize`,n)}),d?.(),(e=p)==null||e.disconnect(),p=null,c&&cancelAnimationFrame(m)}}const bo=da,xo=fa,So=aa,Co=ma,wo=ca,To=ia,Eo=pa,Do=(e,t,n)=>{let r=new Map,i={platform:go,...n},a={...i.platform,_c:r};return na(e,t,{...i,platform:a})};var Q=ze({Vue:()=>e,Vue2:()=>jo,del:()=>Po,install:()=>Mo,isVue2:()=>ko,isVue3:()=>Ao,set:()=>No});import*as Oo from"vue";Ve(Q,Oo);var ko=!1,Ao=!0,jo=void 0;function Mo(){}function No(e,t,n){return Array.isArray(e)?(e.length=Math.max(e.length,t),e.splice(t,1,n),n):(e[t]=n,n)}function Po(e,t){if(Array.isArray(e)){e.splice(t,1);return}delete e[t]}function Fo(e){return typeof e==`object`&&!!e&&`$el`in e}function Io(e){if(Fo(e)){let t=e.$el;return ya(t)&&ga(t)===`#comment`?null:t}return e}function Lo(e){return typeof e==`function`?e():(0,Q.unref)(e)}function Ro(e){return{name:`arrow`,options:e,fn(t){let n=Io(Lo(e.element));return n==null?{}:To({element:n,padding:e.padding}).fn(t)}}}function zo(e){return typeof window>`u`?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Bo(e,t){let n=zo(e);return Math.round(t*n)/n}function Vo(e,t,n){n===void 0&&(n={});let r=n.whileElementsMounted,i=(0,Q.computed)(()=>Lo(n.open)??!0),a=(0,Q.computed)(()=>Lo(n.middleware)),o=(0,Q.computed)(()=>Lo(n.placement)??`bottom`),s=(0,Q.computed)(()=>Lo(n.strategy)??`absolute`),c=(0,Q.computed)(()=>Lo(n.transform)??!0),l=(0,Q.computed)(()=>Io(e.value)),u=(0,Q.computed)(()=>Io(t.value)),d=(0,Q.ref)(0),f=(0,Q.ref)(0),p=(0,Q.ref)(s.value),m=(0,Q.ref)(o.value),h=(0,Q.shallowRef)({}),g=(0,Q.ref)(!1),_=(0,Q.computed)(()=>{let e={position:p.value,left:`0`,top:`0`};if(!u.value)return e;let t=Bo(u.value,d.value),n=Bo(u.value,f.value);return c.value?{...e,transform:`translate(`+t+`px, `+n+`px)`,...zo(u.value)>=1.5&&{willChange:`transform`}}:{position:p.value,left:t+`px`,top:n+`px`}}),v;function y(){if(l.value==null||u.value==null)return;let e=i.value;Do(l.value,u.value,{middleware:a.value,placement:o.value,strategy:s.value}).then(t=>{d.value=t.x,f.value=t.y,p.value=t.strategy,m.value=t.placement,h.value=t.middlewareData,g.value=e!==!1})}function b(){typeof v==`function`&&(v(),v=void 0)}function x(){if(b(),r===void 0){y();return}if(l.value!=null&&u.value!=null){v=r(l.value,u.value,y);return}}function S(){i.value||(g.value=!1)}return(0,Q.watch)([a,o,s,i],y,{flush:`sync`}),(0,Q.watch)([l,u],x,{flush:`sync`}),(0,Q.watch)(i,S,{flush:`sync`}),(0,Q.getCurrentScope)()&&(0,Q.onScopeDispose)(b),{x:(0,Q.shallowReadonly)(d),y:(0,Q.shallowReadonly)(f),strategy:(0,Q.shallowReadonly)(p),placement:(0,Q.shallowReadonly)(m),middlewareData:(0,Q.shallowReadonly)(h),isPositioned:(0,Q.shallowReadonly)(g),floatingStyles:_,update:y}}const Ho={side:`bottom`,sideOffset:0,sideFlip:!0,align:`center`,alignOffset:0,alignFlip:!0,arrowPadding:0,avoidCollisions:!0,collisionBoundary:()=>[],collisionPadding:0,sticky:`partial`,hideWhenDetached:!1,positionStrategy:`fixed`,updatePositionStrategy:`optimized`,prioritizePosition:!1},[Uo,Wo]=J(`PopperContent`);var Go=p({inheritAttrs:!1,__name:`PopperContent`,props:b({side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},{...Ho}),emits:[`placed`],setup(e,{emit:t}){let n=e,r=t,i=vi(),{forwardRef:a,currentElement:s}=Y(),c=j(),u=j(),{width:d,height:p}=Wn(u),m=o(()=>n.side+(n.align===`center`?``:`-${n.align}`)),h=o(()=>typeof n.collisionPadding==`number`?n.collisionPadding:{top:0,right:0,bottom:0,left:0,...n.collisionPadding}),g=o(()=>Array.isArray(n.collisionBoundary)?n.collisionBoundary:[n.collisionBoundary]),_=o(()=>({padding:h.value,boundary:g.value.filter(Si),altBoundary:g.value.length>0})),v=o(()=>({mainAxis:n.sideFlip,crossAxis:n.alignFlip})),y=ue(()=>[bo({mainAxis:n.sideOffset+p.value,alignmentAxis:n.alignOffset}),n.prioritizePosition&&n.avoidCollisions&&So({..._.value,...v.value}),n.avoidCollisions&&xo({mainAxis:!0,crossAxis:!!n.prioritizePosition,limiter:n.sticky===`partial`?Eo():void 0,..._.value}),!n.prioritizePosition&&n.avoidCollisions&&So({..._.value,...v.value}),Co({..._.value,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:i,height:a}=t.reference,o=e.floating.style;o.setProperty(`--reka-popper-available-width`,`${n}px`),o.setProperty(`--reka-popper-available-height`,`${r}px`),o.setProperty(`--reka-popper-anchor-width`,`${i}px`),o.setProperty(`--reka-popper-anchor-height`,`${a}px`)}}),u.value&&Ro({element:u.value,padding:n.arrowPadding}),Ci({arrowWidth:d.value,arrowHeight:p.value}),n.hideWhenDetached&&wo({strategy:`referenceHidden`,..._.value})]),b=o(()=>n.reference??i.anchor.value),{floatingStyles:S,placement:C,isPositioned:w,middlewareData:E,update:D}=Vo(b,c,{strategy:n.positionStrategy,placement:m,whileElementsMounted:(...e)=>yo(...e,{layoutShift:!n.disableUpdateOnLayoutShift,animationFrame:n.updatePositionStrategy===`always`}),middleware:y}),O=o(()=>wi(C.value)[0]),A=o(()=>wi(C.value)[1]);se(()=>{w.value&&r(`placed`)});let ee=o(()=>E.value.arrow?.centerOffset!==0),M=j(``);z(()=>{s.value&&(M.value=window.getComputedStyle(s.value).zIndex)});let te=o(()=>E.value.arrow?.x??0),ne=o(()=>E.value.arrow?.y??0);return Wo({placedSide:O,onArrowChange:e=>u.value=e,arrowX:te,arrowY:ne,shouldHideArrow:ee}),(e,t)=>(k(),l(`div`,{ref_key:`floatingRef`,ref:c,"data-reka-popper-content-wrapper":``,style:T({...I(S),transform:I(w)?I(S).transform:`translate(0, -200%)`,minWidth:`max-content`,zIndex:M.value,"--reka-popper-transform-origin":[I(E).transformOrigin?.x,I(E).transformOrigin?.y].join(` `),...I(E).hide?.referenceHidden&&{visibility:`hidden`,pointerEvents:`none`}})},[f(I(Z),x({ref:I(a)},e.$attrs,{"as-child":n.asChild,as:e.as,"data-side":O.value,"data-align":A.value,style:{animation:I(w)?void 0:`none`}}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`as-child`,`as`,`data-side`,`data-align`,`style`])],4))}});function Ko(e){let t=mn({nonce:j()});return o(()=>e?.value||t.nonce?.value)}var qo=p({__name:`MenuAnchor`,props:{reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=e;return(e,n)=>(k(),s(I(xi),w(h(t)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}});function Jo(){let e=j(!1);return D(()=>{ve(`keydown`,()=>{e.value=!0},{capture:!0,passive:!0}),ve([`pointerdown`,`pointermove`],()=>{e.value=!1},{capture:!0,passive:!0})}),e}const Yo=fe(Jo),[Xo,Zo]=J([`MenuRoot`,`MenuSub`],`MenuContext`),[Qo,$o]=J(`MenuRoot`);var es=p({__name:`MenuRoot`,props:{open:{type:Boolean,required:!1,default:!1},dir:{type:String,required:!1},modal:{type:Boolean,required:!1,default:!0}},emits:[`update:open`],setup(e,{emit:t}){let n=e,r=t,{modal:i,dir:a}=F(n),o=wn(a),c=xe(n,`open`,r),l=j(),u=Yo();return Zo({open:c,onOpenChange:e=>{c.value=e},content:l,onContentChange:e=>{l.value=e}}),$o({onClose:()=>{c.value=!1},isUsingKeyboardRef:u,dir:o,modal:i}),(e,t)=>(k(),s(I(bi),null,{default:B(()=>[N(e.$slots,`default`)]),_:3}))}});const[ts,ns]=J(`MenuContent`);var rs=p({__name:`MenuContentImpl`,props:b({loop:{type:Boolean,required:!1},disableOutsidePointerEvents:{type:Boolean,required:!1},disableOutsideScroll:{type:Boolean,required:!1},trapFocus:{type:Boolean,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},{...Ho}),emits:[`escapeKeyDown`,`pointerDownOutside`,`focusOutside`,`interactOutside`,`entryFocus`,`openAutoFocus`,`closeAutoFocus`,`dismiss`],setup(e,{emit:t}){let n=e,r=t,i=Xo(),a=Qo(),{trapFocus:o,disableOutsidePointerEvents:c,loop:l}=F(n);Dn(),xn(c.value);let u=j(``),d=j(0),p=j(0),m=j(null),h=j(`right`),g=j(0),_=j(null),v=j(),{forwardRef:y,currentElement:b}=Y(),{handleTypeaheadSearch:x}=Kn();R(b,e=>{i.onContentChange(e)}),O(()=>{window.clearTimeout(d.value)});function S(e){return h.value===m.value?.side&&Ir(e,m.value?.area)}async function C(e){r(`openAutoFocus`,e),!e.defaultPrevented&&(e.preventDefault(),b.value?.focus({preventScroll:!0}))}function w(e){if(e.defaultPrevented)return;let t=e.target.closest(`[data-reka-menu-content]`)===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=e.key.length===1,i=fn(e,on(),b.value,{loop:l.value,arrowKeyOptions:`vertical`,dir:a?.dir.value,focus:!0,attributeName:`[data-reka-collection-item]:not([data-disabled])`});if(i)return i?.focus();if(e.code===`Space`)return;let o=v.value?.getItems()??[];if(t&&(e.key===`Tab`&&e.preventDefault(),!n&&r&&x(e.key,o)),e.target!==b.value||!Or.includes(e.key))return;e.preventDefault();let s=[...o.map(e=>e.ref)];Dr.includes(e.key)&&s.reverse(),Pr(s)}function T(e){e?.currentTarget?.contains?.(e.target)||(window.clearTimeout(d.value),u.value=``)}function E(e){if(!Lr(e))return;let t=e.target,n=g.value!==e.clientX;(e?.currentTarget)?.contains(t)&&n&&(h.value=e.clientX>g.value?`right`:`left`,g.value=e.clientX)}return ns({onItemEnter:e=>!!S(e),onItemLeave:e=>{S(e)||(b.value?.focus(),_.value=null)},onTriggerLeave:e=>!!S(e),searchRef:u,pointerGraceTimerRef:p,onPointerGraceIntentChange:e=>{m.value=e}}),(e,t)=>(k(),s(I(wr),{"as-child":``,trapped:I(o),onMountAutoFocus:C,onUnmountAutoFocus:t[7]||=e=>r(`closeAutoFocus`,e)},{default:B(()=>[f(I(lr),{"as-child":``,"disable-outside-pointer-events":I(c),onEscapeKeyDown:t[2]||=e=>r(`escapeKeyDown`,e),onPointerDownOutside:t[3]||=e=>r(`pointerDownOutside`,e),onFocusOutside:t[4]||=e=>r(`focusOutside`,e),onInteractOutside:t[5]||=e=>r(`interactOutside`,e),onDismiss:t[6]||=e=>r(`dismiss`)},{default:B(()=>[f(I(ai),{ref_key:`rovingFocusGroupRef`,ref:v,"current-tab-stop-id":_.value,"onUpdate:currentTabStopId":t[0]||=e=>_.value=e,"as-child":``,orientation:`vertical`,dir:I(a).dir.value,loop:I(l),onEntryFocus:t[1]||=e=>{r(`entryFocus`,e),I(a).isUsingKeyboardRef.value||e.preventDefault()}},{default:B(()=>[f(I(Go),{ref:I(y),role:`menu`,as:e.as,"as-child":e.asChild,"aria-orientation":`vertical`,"data-reka-menu-content":``,"data-state":I(jr)(I(i).open.value),dir:I(a).dir.value,side:e.side,"side-offset":e.sideOffset,align:e.align,"align-offset":e.alignOffset,"avoid-collisions":e.avoidCollisions,"collision-boundary":e.collisionBoundary,"collision-padding":e.collisionPadding,"arrow-padding":e.arrowPadding,"prioritize-position":e.prioritizePosition,"position-strategy":e.positionStrategy,"update-position-strategy":e.updatePositionStrategy,sticky:e.sticky,"hide-when-detached":e.hideWhenDetached,reference:e.reference,onKeydown:w,onBlur:T,onPointermove:E},{default:B(()=>[N(e.$slots,`default`)]),_:3},8,[`as`,`as-child`,`data-state`,`dir`,`side`,`side-offset`,`align`,`align-offset`,`avoid-collisions`,`collision-boundary`,`collision-padding`,`arrow-padding`,`prioritize-position`,`position-strategy`,`update-position-strategy`,`sticky`,`hide-when-detached`,`reference`])]),_:3},8,[`current-tab-stop-id`,`dir`,`loop`])]),_:3},8,[`disable-outside-pointer-events`])]),_:3},8,[`trapped`]))}}),os=p({inheritAttrs:!1,__name:`MenuItemImpl`,props:{disabled:{type:Boolean,required:!1},textValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=e,n=ts(),{forwardRef:r}=Y(),{CollectionItem:i}=Xr(),a=j(!1);async function o(e){e.defaultPrevented||Lr(e)&&(t.disabled?n.onItemLeave(e):n.onItemEnter(e)||e.currentTarget?.focus({preventScroll:!0}))}async function c(e){await S(),!e.defaultPrevented&&Lr(e)&&n.onItemLeave(e)}return(e,t)=>(k(),s(I(i),{value:{textValue:e.textValue}},{default:B(()=>[f(I(Z),x({ref:I(r),role:`menuitem`,tabindex:`-1`},e.$attrs,{as:e.as,"as-child":e.asChild,"aria-disabled":e.disabled||void 0,"data-disabled":e.disabled?``:void 0,"data-highlighted":a.value?``:void 0,onPointermove:o,onPointerleave:c,onFocus:t[0]||=async t=>{await S(),!(t.defaultPrevented||e.disabled)&&(a.value=!0)},onBlur:t[1]||=async e=>{await S(),!e.defaultPrevented&&(a.value=!1)}}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`as`,`as-child`,`aria-disabled`,`data-disabled`,`data-highlighted`])]),_:3},8,[`value`]))}}),ss=p({__name:`MenuItem`,props:{disabled:{type:Boolean,required:!1},textValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`select`],setup(e,{emit:t}){let n=e,r=t,{forwardRef:i,currentElement:a}=Y(),o=Qo(),c=ts(),l=j(!1);async function u(){let e=a.value;if(!n.disabled&&e){let e=new CustomEvent(`menu.itemSelect`,{bubbles:!0,cancelable:!0});r(`select`,e),await S(),e.defaultPrevented?l.value=!1:o.onClose()}}return(e,t)=>(k(),s(os,x(n,{ref:I(i),onClick:u,onPointerdown:t[0]||=()=>{l.value=!0},onPointerup:t[1]||=async e=>{await S(),!e.defaultPrevented&&(l.value||e.currentTarget?.click())},onKeydown:t[2]||=async t=>{let n=I(c).searchRef.value!==``;e.disabled||n&&t.key===` `||I(Tr).includes(t.key)&&(t.currentTarget.click(),t.preventDefault())}}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}});const[cs,ls]=J([`MenuCheckboxItem`,`MenuRadioItem`],`MenuItemIndicatorContext`);var us=p({__name:`MenuItemIndicator`,props:{forceMount:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`span`}},setup(e){let t=cs({modelValue:j(!1)});return(e,n)=>(k(),s(I(Zn),{present:e.forceMount||I(Mr)(I(t).modelValue.value)||I(t).modelValue.value===!0},{default:B(()=>[f(I(Z),{as:e.as,"as-child":e.asChild,"data-state":I(Nr)(I(t).modelValue.value)},{default:B(()=>[N(e.$slots,`default`)]),_:3},8,[`as`,`as-child`,`data-state`])]),_:3},8,[`present`]))}}),ds=p({__name:`MenuCheckboxItem`,props:{modelValue:{type:[Boolean,String],required:!1,default:!1},disabled:{type:Boolean,required:!1},textValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`select`,`update:modelValue`],setup(e,{emit:t}){let n=e,r=t,i=xe(n,`modelValue`,r);return ls({modelValue:i}),(e,t)=>(k(),s(ss,x({role:`menuitemcheckbox`},n,{"aria-checked":I(Mr)(I(i))?`mixed`:I(i),"data-state":I(Nr)(I(i)),onSelect:t[0]||=async e=>{r(`select`,e),I(Mr)(I(i))?i.value=!0:i.value=!I(i)}}),{default:B(()=>[N(e.$slots,`default`,{modelValue:I(i)})]),_:3},16,[`aria-checked`,`data-state`]))}}),fs=p({__name:`MenuRootContentModal`,props:{loop:{type:Boolean,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`escapeKeyDown`,`pointerDownOutside`,`focusOutside`,`interactOutside`,`entryFocus`,`openAutoFocus`,`closeAutoFocus`],setup(e,{emit:t}){let n=e,r=t,i=X(n,r),a=Xo(),{forwardRef:o,currentElement:c}=Y();return Bn(c),(e,t)=>(k(),s(rs,x(I(i),{ref:I(o),"trap-focus":I(a).open.value,"disable-outside-pointer-events":I(a).open.value,"disable-outside-scroll":!0,onDismiss:t[0]||=e=>I(a).onOpenChange(!1),onFocusOutside:t[1]||=V(e=>r(`focusOutside`,e),[`prevent`])}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`trap-focus`,`disable-outside-pointer-events`]))}}),ps=p({__name:`MenuRootContentNonModal`,props:{loop:{type:Boolean,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`escapeKeyDown`,`pointerDownOutside`,`focusOutside`,`interactOutside`,`entryFocus`,`openAutoFocus`,`closeAutoFocus`],setup(e,{emit:t}){let n=X(e,t),r=Xo();return(e,t)=>(k(),s(rs,x(I(n),{"trap-focus":!1,"disable-outside-pointer-events":!1,"disable-outside-scroll":!1,onDismiss:t[0]||=e=>I(r).onOpenChange(!1)}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),ms=p({__name:`MenuContent`,props:{forceMount:{type:Boolean,required:!1},loop:{type:Boolean,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`escapeKeyDown`,`pointerDownOutside`,`focusOutside`,`interactOutside`,`entryFocus`,`openAutoFocus`,`closeAutoFocus`],setup(e,{emit:t}){let n=X(e,t),r=Xo(),i=Qo();return(e,t)=>(k(),s(I(Zn),{present:e.forceMount||I(r).open.value},{default:B(()=>[I(i).modal.value?(k(),s(fs,w(x({key:0},{...e.$attrs,...I(n)})),{default:B(()=>[N(e.$slots,`default`)]),_:3},16)):(k(),s(ps,w(x({key:1},{...e.$attrs,...I(n)})),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))]),_:3},8,[`present`]))}}),hs=p({__name:`MenuGroup`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=e;return(e,n)=>(k(),s(I(Z),x({role:`group`},t),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),gs=p({__name:`MenuLabel`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`div`}},setup(e){let t=e;return(e,n)=>(k(),s(I(Z),w(h(t)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),_s=p({__name:`MenuPortal`,props:{to:{type:null,required:!1},disabled:{type:Boolean,required:!1},defer:{type:Boolean,required:!1},forceMount:{type:Boolean,required:!1}},setup(e){let t=e;return(e,n)=>(k(),s(I(Gr),w(h(t)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}});const[vs,ys]=J(`MenuRadioGroup`);var bs=p({__name:`MenuRadioGroup`,props:{modelValue:{type:String,required:!1,default:``},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`update:modelValue`],setup(e,{emit:t}){let n=e,r=xe(n,`modelValue`,t);return ys({modelValue:r,onValueChange:e=>{r.value=e}}),(e,t)=>(k(),s(hs,w(h(n)),{default:B(()=>[N(e.$slots,`default`,{modelValue:I(r)})]),_:3},16))}}),xs=p({__name:`MenuRadioItem`,props:{value:{type:String,required:!0},disabled:{type:Boolean,required:!1},textValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`select`],setup(e,{emit:t}){let n=e,r=t,{value:i}=F(n),a=vs(),c=o(()=>a.modelValue.value===i?.value);return ls({modelValue:c}),(e,t)=>(k(),s(ss,x({role:`menuitemradio`},n,{"aria-checked":c.value,"data-state":I(Nr)(c.value),onSelect:t[0]||=async e=>{r(`select`,e),I(a).onValueChange(I(i))}}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`aria-checked`,`data-state`]))}}),Ss=p({__name:`MenuSeparator`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=e;return(e,n)=>(k(),s(I(Z),x(t,{role:`separator`,"aria-orientation":`horizontal`}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}});const[Cs,ws]=J(`MenuSub`);var Ts=p({__name:`MenuSub`,props:{open:{type:Boolean,required:!1,default:void 0}},emits:[`update:open`],setup(e,{emit:t}){let n=e,r=xe(n,`open`,t,{defaultValue:!1,passive:n.open===void 0}),i=Xo(),a=j(),o=j();return z(e=>{i?.open.value===!1&&(r.value=!1),e(()=>r.value=!1)}),Zo({open:r,onOpenChange:e=>{r.value=e},content:o,onContentChange:e=>{o.value=e}}),ws({triggerId:``,contentId:``,trigger:a,onTriggerChange:e=>{a.value=e}}),(e,t)=>(k(),s(I(bi),null,{default:B(()=>[N(e.$slots,`default`)]),_:3}))}}),Es=p({__name:`MenuSubContent`,props:{forceMount:{type:Boolean,required:!1},loop:{type:Boolean,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1,default:!0},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`escapeKeyDown`,`pointerDownOutside`,`focusOutside`,`interactOutside`,`entryFocus`,`openAutoFocus`,`closeAutoFocus`],setup(e,{emit:t}){let n=X(e,t),r=Xo(),i=Qo(),a=Cs(),{forwardRef:o,currentElement:c}=Y();return a.contentId||=Hn(void 0,`reka-menu-sub-content`),(e,t)=>(k(),s(I(Zn),{present:e.forceMount||I(r).open.value},{default:B(()=>[f(rs,x(I(n),{id:I(a).contentId,ref:I(o),"aria-labelledby":I(a).triggerId,align:`start`,side:I(i).dir.value===`rtl`?`left`:`right`,"disable-outside-pointer-events":!1,"disable-outside-scroll":!1,"trap-focus":!1,onOpenAutoFocus:t[0]||=V(e=>{I(i).isUsingKeyboardRef.value&&I(c)?.focus()},[`prevent`]),onCloseAutoFocus:t[1]||=V(()=>{},[`prevent`]),onFocusOutside:t[2]||=e=>{e.defaultPrevented||e.target!==I(a).trigger.value&&I(r).onOpenChange(!1)},onEscapeKeyDown:t[3]||=e=>{I(i).onClose(),e.preventDefault()},onKeydown:t[4]||=e=>{let t=e.currentTarget?.contains(e.target),n=I(Ar)[I(i).dir.value].includes(e.key);t&&n&&(I(r).onOpenChange(!1),I(a).trigger.value?.focus(),e.preventDefault())}}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`id`,`aria-labelledby`,`side`])]),_:3},8,[`present`]))}}),Ds=p({__name:`MenuSubTrigger`,props:{disabled:{type:Boolean,required:!1},textValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=e,n=Xo(),r=Qo(),i=Cs(),a=ts(),o=j(null);i.triggerId||=Hn(void 0,`reka-menu-sub-trigger`);function c(){o.value&&window.clearTimeout(o.value),o.value=null}O(()=>{c()});function l(e){Lr(e)&&(a.onItemEnter(e)||!t.disabled&&!n.open.value&&!o.value&&(a.onPointerGraceIntentChange(null),o.value=window.setTimeout(()=>{n.onOpenChange(!0),c()},100)))}async function u(e){if(!Lr(e))return;c();let t=n.content.value?.getBoundingClientRect();if(t?.width){let r=n.content.value?.dataset.side,i=r===`right`,o=i?-5:5,s=t[i?`left`:`right`],c=t[i?`right`:`left`];a.onPointerGraceIntentChange({area:[{x:e.clientX+o,y:e.clientY},{x:s,y:t.top},{x:c,y:t.top},{x:c,y:t.bottom},{x:s,y:t.bottom}],side:r}),window.clearTimeout(a.pointerGraceTimerRef.value),a.pointerGraceTimerRef.value=window.setTimeout(()=>a.onPointerGraceIntentChange(null),300)}else{if(a.onTriggerLeave(e))return;a.onPointerGraceIntentChange(null)}}async function d(e){let i=a.searchRef.value!==``;t.disabled||i&&e.key===` `||kr[r.dir.value].includes(e.key)&&(n.onOpenChange(!0),await S(),n.content.value?.focus(),e.preventDefault())}return(e,r)=>(k(),s(qo,{"as-child":``},{default:B(()=>[f(os,x(t,{id:I(i).triggerId,ref:e=>{I(i)?.onTriggerChange(e?.$el)},"aria-haspopup":`menu`,"aria-expanded":I(n).open.value,"aria-controls":I(i).contentId,"data-state":I(jr)(I(n).open.value),onClick:r[0]||=async e=>{t.disabled||e.defaultPrevented||(e.currentTarget.focus(),I(n).open.value||I(n).onOpenChange(!0))},onPointermove:l,onPointerleave:u,onKeydown:d}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`id`,`aria-expanded`,`aria-controls`,`data-state`])]),_:3}))}}),Os=p({__name:`ContextMenuCheckboxItem`,props:{modelValue:{type:[Boolean,String],required:!1},disabled:{type:Boolean,required:!1},textValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`select`,`update:modelValue`],setup(e,{emit:t}){let n=e,r=Tn(t);return Y(),(e,t)=>(k(),s(I(ds),w(h({...n,...I(r)})),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}});const[ks,As]=J(`ContextMenuRoot`);var js=p({inheritAttrs:!1,__name:`ContextMenuRoot`,props:{pressOpenDelay:{type:Number,required:!1,default:700},dir:{type:String,required:!1},modal:{type:Boolean,required:!1,default:!0}},emits:[`update:open`],setup(e,{emit:t}){let n=e,r=t,{dir:i,modal:a,pressOpenDelay:o}=F(n);Y();let c=wn(i),l=j(!1),u=j();return As({open:l,onOpenChange:e=>{l.value=e},dir:c,modal:a,triggerElement:u,pressOpenDelay:o}),R(l,e=>{r(`update:open`,e)}),(e,t)=>(k(),s(I(es),{open:l.value,"onUpdate:open":t[0]||=e=>l.value=e,dir:I(c),modal:I(a)},{default:B(()=>[N(e.$slots,`default`)]),_:3},8,[`open`,`dir`,`modal`]))}}),Ms=p({__name:`ContextMenuContent`,props:{forceMount:{type:Boolean,required:!1},loop:{type:Boolean,required:!1},sideFlip:{type:Boolean,required:!1},alignOffset:{type:Number,required:!1,default:0},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1,default:!0},collisionBoundary:{type:null,required:!1,default:()=>[]},collisionPadding:{type:[Number,Object],required:!1,default:0},sticky:{type:String,required:!1,default:`partial`},hideWhenDetached:{type:Boolean,required:!1,default:!1},positionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`escapeKeyDown`,`pointerDownOutside`,`focusOutside`,`interactOutside`,`closeAutoFocus`],setup(e,{emit:t}){let n=X(e,t);Y();let r=ks(),i=j(!1);return(e,t)=>(k(),s(I(ms),x(I(n),{side:`right`,"side-offset":2,align:`start`,"update-position-strategy":`always`,style:{"--reka-context-menu-content-transform-origin":`var(--reka-popper-transform-origin)`,"--reka-context-menu-content-available-width":`var(--reka-popper-available-width)`,"--reka-context-menu-content-available-height":`var(--reka-popper-available-height)`,"--reka-context-menu-trigger-width":`var(--reka-popper-anchor-width)`,"--reka-context-menu-trigger-height":`var(--reka-popper-anchor-height)`},onCloseAutoFocus:t[0]||=e=>{!e.defaultPrevented&&i.value&&e.preventDefault(),i.value=!1},onInteractOutside:t[1]||=e=>{e.detail.originalEvent.button===2&&e.target===I(r).triggerElement.value&&e.preventDefault(),!e.defaultPrevented&&!I(r).modal.value&&(i.value=!0)}}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),Ns=p({__name:`ContextMenuGroup`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=e;return Y(),(e,n)=>(k(),s(I(hs),w(h(t)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),Ps=p({__name:`ContextMenuItem`,props:{disabled:{type:Boolean,required:!1},textValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`select`],setup(e,{emit:t}){let n=e,r=Tn(t);return Y(),(e,t)=>(k(),s(I(ss),w(h({...n,...I(r)})),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),Fs=p({__name:`ContextMenuItemIndicator`,props:{forceMount:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=e;return Y(),(e,n)=>(k(),s(I(us),w(h(t)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),Is=p({__name:`ContextMenuLabel`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=e;return Y(),(e,n)=>(k(),s(I(gs),w(h(t)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),Ls=p({__name:`ContextMenuPortal`,props:{to:{type:null,required:!1},disabled:{type:Boolean,required:!1},defer:{type:Boolean,required:!1},forceMount:{type:Boolean,required:!1}},setup(e){let t=e;return(e,n)=>(k(),s(I(_s),w(h(t)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),Rs=p({__name:`ContextMenuRadioGroup`,props:{modelValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`update:modelValue`],setup(e,{emit:t}){let n=e,r=Tn(t);return Y(),(e,t)=>(k(),s(I(bs),w(h({...n,...I(r)})),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),zs=p({__name:`ContextMenuRadioItem`,props:{value:{type:String,required:!0},disabled:{type:Boolean,required:!1},textValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`select`],setup(e,{emit:t}){let n=e,r=Tn(t);return Y(),(e,t)=>(k(),s(I(xs),w(h({...n,...I(r)})),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),Bs=p({__name:`ContextMenuSeparator`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=e;return Y(),(e,n)=>(k(),s(I(Ss),w(h(t)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),Vs=p({__name:`ContextMenuSub`,props:{defaultOpen:{type:Boolean,required:!1},open:{type:Boolean,required:!1,default:void 0}},emits:[`update:open`],setup(e,{emit:t}){let n=e,r=t;Y();let i=xe(n,`open`,r,{defaultValue:n.defaultOpen,passive:n.open===void 0});return(e,t)=>(k(),s(I(Ts),{open:I(i),"onUpdate:open":t[0]||=e=>v(i)?i.value=e:null},{default:B(()=>[N(e.$slots,`default`,{open:I(i)})]),_:3},8,[`open`]))}}),Hs=p({__name:`ContextMenuSubContent`,props:{forceMount:{type:Boolean,required:!1},loop:{type:Boolean,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`escapeKeyDown`,`pointerDownOutside`,`focusOutside`,`interactOutside`,`entryFocus`,`openAutoFocus`,`closeAutoFocus`],setup(e,{emit:t}){let n=X(e,t);return Y(),(e,t)=>(k(),s(I(Es),x(I(n),{style:{"--reka-context-menu-content-transform-origin":`var(--reka-popper-transform-origin)`,"--reka-context-menu-content-available-width":`var(--reka-popper-available-width)`,"--reka-context-menu-content-available-height":`var(--reka-popper-available-height)`,"--reka-context-menu-trigger-width":`var(--reka-popper-anchor-width)`,"--reka-context-menu-trigger-height":`var(--reka-popper-anchor-height)`}}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),Us=p({__name:`ContextMenuSubTrigger`,props:{disabled:{type:Boolean,required:!1},textValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=e;return Y(),(e,n)=>(k(),s(I(Ds),w(h(t)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}});function Ws(e){return e.pointerType!==`mouse`}var Gs=p({inheritAttrs:!1,__name:`ContextMenuTrigger`,props:{disabled:{type:Boolean,required:!1,default:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`span`}},setup(e){let{disabled:t}=F(e),{forwardRef:r,currentElement:i}=Y(),a=ks(),s=j({x:0,y:0}),c=o(()=>({getBoundingClientRect:()=>({width:0,height:0,left:s.value.x,right:s.value.x,top:s.value.y,bottom:s.value.y,...s.value})})),u=j(0);function d(){window.clearTimeout(u.value)}function p(e){s.value={x:e.clientX,y:e.clientY},a.onOpenChange(!0)}async function m(e){t.value||(await S(),e.defaultPrevented||(d(),p(e),e.preventDefault()))}async function h(e){t.value||(await S(),Ws(e)&&!e.defaultPrevented&&(d(),u.value=window.setTimeout(()=>p(e),a.pressOpenDelay.value)))}async function g(e){t.value||(await S(),Ws(e)&&!e.defaultPrevented&&d())}return D(()=>{i.value&&(a.triggerElement.value=i.value)}),(e,i)=>(k(),l(n,null,[f(I(qo),{as:`template`,reference:c.value},null,8,[`reference`]),f(I(Z),x({ref:I(r),as:e.as,"as-child":e.asChild,"data-state":I(a).open.value?`open`:`closed`,"data-disabled":I(t)?``:void 0,style:{WebkitTouchCallout:`none`,pointerEvents:`auto`}},e.$attrs,{onContextmenu:m,onPointerdown:h,onPointermove:g,onPointercancel:g,onPointerup:g}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`as`,`as-child`,`data-state`,`data-disabled`])],64))}}),Ks=p({__name:`Label`,props:{for:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`label`}},setup(e){let t=e;return Y(),(e,n)=>(k(),s(I(Z),x(t,{onMousedown:n[0]||=e=>{!e.defaultPrevented&&e.detail>1&&e.preventDefault()}}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}});let qs=new Map,Js=!1;try{Js=new Intl.NumberFormat(`de-DE`,{signDisplay:`exceptZero`}).resolvedOptions().signDisplay===`exceptZero`}catch{}let Ys=!1;try{Ys=new Intl.NumberFormat(`de-DE`,{style:`unit`,unit:`degree`}).resolvedOptions().style===`unit`}catch{}const Xs={degree:{narrow:{default:`°`,"ja-JP":` 度`,"zh-TW":`度`,"sl-SI":` °`}}};var Zs=class{format(e){let t=``;if(t=!Js&&this.options.signDisplay!=null?$s(this.numberFormatter,this.options.signDisplay,e):this.numberFormatter.format(e),this.options.style===`unit`&&!Ys){let{unit:e,unitDisplay:n=`short`,locale:r}=this.resolvedOptions();if(!e)return t;let i=Xs[e]?.[n];t+=i[r]||i.default}return t}formatToParts(e){return this.numberFormatter.formatToParts(e)}formatRange(e,t){if(typeof this.numberFormatter.formatRange==`function`)return this.numberFormatter.formatRange(e,t);if(t<e)throw RangeError(`End date must be >= start date`);return`${this.format(e)} \u{2013} ${this.format(t)}`}formatRangeToParts(e,t){if(typeof this.numberFormatter.formatRangeToParts==`function`)return this.numberFormatter.formatRangeToParts(e,t);if(t<e)throw RangeError(`End date must be >= start date`);let n=this.numberFormatter.formatToParts(e),r=this.numberFormatter.formatToParts(t);return[...n.map(e=>({...e,source:`startRange`})),{type:`literal`,value:` – `,source:`shared`},...r.map(e=>({...e,source:`endRange`}))]}resolvedOptions(){let e=this.numberFormatter.resolvedOptions();return!Js&&this.options.signDisplay!=null&&(e={...e,signDisplay:this.options.signDisplay}),!Ys&&this.options.style===`unit`&&(e={...e,style:`unit`,unit:this.options.unit,unitDisplay:this.options.unitDisplay}),e}constructor(e,t={}){this.numberFormatter=Qs(e,t),this.options=t}};function Qs(e,t={}){let{numberingSystem:n}=t;if(n&&e.includes(`-nu-`)&&(e.includes(`-u-`)||(e+=`-u-`),e+=`-nu-${n}`),t.style===`unit`&&!Ys){let{unit:e,unitDisplay:n=`short`}=t;if(!e)throw Error(`unit option must be provided with style: "unit"`);if(!Xs[e]?.[n])throw Error(`Unsupported unit ${e} with unitDisplay = ${n}`);t={...t,style:`decimal`}}let r=e+(t?Object.entries(t).sort((e,t)=>e[0]<t[0]?-1:1).join():``);if(qs.has(r))return qs.get(r);let i=new Intl.NumberFormat(e,t);return qs.set(r,i),i}function $s(e,t,n){if(t===`auto`)return e.format(n);if(t===`never`)return e.format(Math.abs(n));{let r=!1;if(t===`always`?r=n>0||Object.is(n,0):t===`exceptZero`&&(Object.is(n,-0)||Object.is(n,0)?n=Math.abs(n):r=n>0),r){let t=e.format(-n),r=e.format(n),i=t.replace(r,``).replace(/\u200e|\u061C/,``);return[...i].length!==1&&console.warn(`@react-aria/i18n polyfill for NumberFormat signDisplay: Unsupported case`),t.replace(r,`!!!`).replace(i,`+`).replace(`!!!`,r)}else return e.format(n)}}const ec=RegExp(`^.*\\(.*\\).*$`),tc=[`latn`,`arab`,`hanidec`,`deva`,`beng`,`fullwide`];var nc=class{parse(e){return ic(this.locale,this.options,e).parse(e)}isValidPartialNumber(e,t,n){return ic(this.locale,this.options,e).isValidPartialNumber(e,t,n)}getNumberingSystem(e){return ic(this.locale,this.options,e).options.numberingSystem}constructor(e,t={}){this.locale=e,this.options=t}};const rc=new Map;function ic(e,t,n){let r=ac(e,t);if(!e.includes(`-nu-`)&&!r.isValidPartialNumber(n)){for(let i of tc)if(i!==r.options.numberingSystem){let r=ac(e+(e.includes(`-u-`)?`-nu-`:`-u-nu-`)+i,t);if(r.isValidPartialNumber(n))return r}}return r}function ac(e,t){let n=e+(t?Object.entries(t).sort((e,t)=>e[0]<t[0]?-1:1).join():``),r=rc.get(n);return r||(r=new oc(e,t),rc.set(n,r)),r}var oc=class{parse(e){let t=this.sanitize(e);if(this.symbols.group&&(t=uc(t,this.symbols.group,``)),this.symbols.decimal&&(t=t.replace(this.symbols.decimal,`.`)),this.symbols.minusSign&&(t=t.replace(this.symbols.minusSign,`-`)),t=t.replace(this.symbols.numeral,this.symbols.index),this.options.style===`percent`){let e=t.indexOf(`-`);t=t.replace(`-`,``),t=t.replace(`+`,``);let n=t.indexOf(`.`);n===-1&&(n=t.length),t=t.replace(`.`,``),t=n-2==0?`0.${t}`:n-2==-1?`0.0${t}`:n-2==-2?`0.00`:`${t.slice(0,n-2)}.${t.slice(n-2)}`,e>-1&&(t=`-${t}`)}let n=t?+t:NaN;if(isNaN(n))return NaN;if(this.options.style===`percent`){let e={...this.options,style:`decimal`,minimumFractionDigits:Math.min((this.options.minimumFractionDigits??0)+2,20),maximumFractionDigits:Math.min((this.options.maximumFractionDigits??0)+2,20)};return new nc(this.locale,e).parse(new Zs(this.locale,e).format(n))}return this.options.currencySign===`accounting`&&ec.test(e)&&(n=-1*n),n}sanitize(e){return e=e.replace(this.symbols.literals,``),this.symbols.minusSign&&(e=e.replace(`-`,this.symbols.minusSign)),this.options.numberingSystem===`arab`&&(this.symbols.decimal&&(e=e.replace(`,`,this.symbols.decimal),e=e.replace(`،`,this.symbols.decimal)),this.symbols.group&&(e=uc(e,`.`,this.symbols.group))),this.symbols.group===`’`&&e.includes(`'`)&&(e=uc(e,`'`,this.symbols.group)),this.options.locale===`fr-FR`&&this.symbols.group&&(e=uc(e,` `,this.symbols.group),e=uc(e,/\u00A0/g,this.symbols.group)),e}isValidPartialNumber(e,t=-1/0,n=1/0){return e=this.sanitize(e),this.symbols.minusSign&&e.startsWith(this.symbols.minusSign)&&t<0?e=e.slice(this.symbols.minusSign.length):this.symbols.plusSign&&e.startsWith(this.symbols.plusSign)&&n>0&&(e=e.slice(this.symbols.plusSign.length)),this.symbols.group&&e.startsWith(this.symbols.group)||this.symbols.decimal&&e.indexOf(this.symbols.decimal)>-1&&this.options.maximumFractionDigits===0?!1:(this.symbols.group&&(e=uc(e,this.symbols.group,``)),e=e.replace(this.symbols.numeral,``),this.symbols.decimal&&(e=e.replace(this.symbols.decimal,``)),e.length===0)}constructor(e,t={}){this.locale=e,t.roundingIncrement!==1&&t.roundingIncrement!=null&&(t.maximumFractionDigits==null&&t.minimumFractionDigits==null?(t.maximumFractionDigits=0,t.minimumFractionDigits=0):t.maximumFractionDigits==null?t.maximumFractionDigits=t.minimumFractionDigits:t.minimumFractionDigits??=t.maximumFractionDigits),this.formatter=new Intl.NumberFormat(e,t),this.options=this.formatter.resolvedOptions(),this.symbols=lc(e,this.formatter,this.options,t),this.options.style===`percent`&&((this.options.minimumFractionDigits??0)>18||(this.options.maximumFractionDigits??0)>18)&&console.warn(`NumberParser cannot handle percentages with greater than 18 decimal places, please reduce the number in your options.`)}};const sc=new Set([`decimal`,`fraction`,`integer`,`minusSign`,`plusSign`,`group`]),cc=[0,4,2,1,11,20,3,7,100,21,.1,1.1];function lc(e,t,n,r){let i=new Intl.NumberFormat(e,{...n,minimumSignificantDigits:1,maximumSignificantDigits:21,roundingIncrement:1,roundingPriority:`auto`,roundingMode:`halfExpand`}),a=i.formatToParts(-10000.111),o=i.formatToParts(10000.111),s=cc.map(e=>i.formatToParts(e)),c=a.find(e=>e.type===`minusSign`)?.value??`-`,l=o.find(e=>e.type===`plusSign`)?.value;!l&&(r?.signDisplay===`exceptZero`||r?.signDisplay===`always`)&&(l=`+`);let u=new Intl.NumberFormat(e,{...n,minimumFractionDigits:2,maximumFractionDigits:2}).formatToParts(.001).find(e=>e.type===`decimal`)?.value,d=a.find(e=>e.type===`group`)?.value,f=a.filter(e=>!sc.has(e.type)).map(e=>dc(e.value)),p=s.flatMap(e=>e.filter(e=>!sc.has(e.type)).map(e=>dc(e.value))),m=[...new Set([...f,...p])].sort((e,t)=>t.length-e.length),h=m.length===0?RegExp(`[\\p{White_Space}]`,`gu`):RegExp(`${m.join(`|`)}|[\\p{White_Space}]`,`gu`),g=[...new Intl.NumberFormat(n.locale,{useGrouping:!1}).format(9876543210)].reverse(),_=new Map(g.map((e,t)=>[e,t])),v=RegExp(`[${g.join(``)}]`,`g`);return{minusSign:c,plusSign:l,decimal:u,group:d,literals:h,numeral:v,index:e=>String(_.get(e))}}function uc(e,t,n){return e.replaceAll?e.replaceAll(t,n):e.split(t).join(n)}function dc(e){return e.replace(/[.*+?^${}()|[\]\\]/g,`\\$&`)}function fc(e){let{disabled:t}=e,n=j(),r=Se(),i=()=>window.clearTimeout(n.value),a=e=>{i(),!t.value&&(r.trigger(),n.value=window.setTimeout(()=>{a(60)},e))},s=()=>{a(400)},c=()=>{i()},l=j(!1),u=o(()=>ge(e.target)),d=e=>{e.button!==0||l.value||(e.preventDefault(),l.value=!0,s())},f=()=>{l.value=!1,c()};return Ce&&(ve(u||window,`pointerdown`,d),ve(window,`pointerup`,f),ve(window,`pointercancel`,f)),{isPressed:l,onTrigger:r.on}}function pc(e,t=j({})){return Te(()=>new Zs(e.value,t.value))}function mc(e,t=j({})){return Te(()=>new nc(e.value,t.value))}function hc(e,t,n){let r=e===`+`?t+n:t-n;if(t%1!=0||n%1!=0){let i=t.toString().split(`.`),a=n.toString().split(`.`),o=i[1]&&i[1].length||0,s=a[1]&&a[1].length||0,c=10**Math.max(o,s);t=Math.round(t*c),n=Math.round(n*c),r=e===`+`?t+n:t-n,r/=c}return r}const[gc,_c]=J(`NumberFieldRoot`);var vc=p({inheritAttrs:!1,__name:`NumberFieldRoot`,props:{defaultValue:{type:Number,required:!1,default:void 0},modelValue:{type:[Number,null],required:!1},min:{type:Number,required:!1},max:{type:Number,required:!1},step:{type:Number,required:!1,default:1},stepSnapping:{type:Boolean,required:!1,default:!0},formatOptions:{type:null,required:!1},locale:{type:String,required:!1},disabled:{type:Boolean,required:!1},readonly:{type:Boolean,required:!1},disableWheelChange:{type:Boolean,required:!1},invertWheelChange:{type:Boolean,required:!1},id:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`div`},name:{type:String,required:!1},required:{type:Boolean,required:!1}},emits:[`update:modelValue`],setup(e,{emit:t}){let n=e,r=t,{disabled:i,readonly:a,disableWheelChange:l,invertWheelChange:u,min:d,max:f,step:p,stepSnapping:m,formatOptions:h,id:g,locale:_}=F(n),v=xe(n,`modelValue`,r,{defaultValue:n.defaultValue,passive:n.modelValue===void 0}),{primitiveElement:y,currentElement:b}=er(),S=Un(_),C=kn(b),w=j(),T=o(()=>!cn(v.value)&&(L(v.value)===d.value||d.value&&!isNaN(v.value)?hc(`-`,v.value,p.value)<d.value:!1)),E=o(()=>!cn(v.value)&&(L(v.value)===f.value||f.value&&!isNaN(v.value)?hc(`+`,v.value,p.value)>f.value:!1));function D(e,t=1){if(w.value?.focus(),n.disabled||n.readonly)return;let r=te.parse(w.value?.value??``);isNaN(r)?v.value=d.value??0:e===`increase`?v.value=L(r+(p.value??1)*t):v.value=L(r-(p.value??1)*t)}function O(e=1){D(`increase`,e)}function A(e=1){D(`decrease`,e)}function ee(e){e===`min`&&d.value!==void 0?v.value=L(d.value):e===`max`&&f.value!==void 0&&(v.value=L(f.value))}let M=pc(S,h),te=mc(S,h),ne=o(()=>M.resolvedOptions().maximumFractionDigits>0?`decimal`:`numeric`),P=pc(S,h),re=o(()=>cn(v.value)||isNaN(v.value)?``:P.format(v.value));function ie(e){return te.isValidPartialNumber(e,d.value,f.value)}function ae(e){w.value&&(w.value.value=e)}function L(e){let t;return t=p.value===void 0||isNaN(p.value)||!m.value?nn(e,d.value,f.value):an(e,d.value,f.value,p.value),t=te.parse(M.format(t)),t}function oe(e){let t=te.parse(e);return v.value=isNaN(t)?void 0:L(t),e.length?ae(re.value):ae(e)}return _c({modelValue:v,handleDecrease:A,handleIncrease:O,handleMinMaxValue:ee,inputMode:ne,inputEl:w,onInputElement:e=>w.value=e,textValue:re,validate:ie,applyInputValue:oe,disabled:i,readonly:a,disableWheelChange:l,invertWheelChange:u,max:f,min:d,isDecreaseDisabled:T,isIncreaseDisabled:E,id:g}),(e,t)=>(k(),s(I(Z),x(e.$attrs,{ref_key:`primitiveElement`,ref:y,role:`group`,as:e.as,"as-child":e.asChild,"data-disabled":I(i)?``:void 0,"data-readonly":I(a)?``:void 0}),{default:B(()=>[N(e.$slots,`default`,{modelValue:I(v),textValue:re.value}),I(C)&&e.name?(k(),s(I(li),{key:0,type:`text`,value:I(v),name:e.name,disabled:I(i),readonly:I(a),required:e.required},null,8,[`value`,`name`,`disabled`,`readonly`,`required`])):c(`v-if`,!0)]),_:3},16,[`as`,`as-child`,`data-disabled`,`data-readonly`]))}}),yc=p({__name:`NumberFieldDecrement`,props:{disabled:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`button`}},setup(e){let t=e,n=gc(),r=o(()=>n.disabled?.value||n.readonly.value||t.disabled||n.isDecreaseDisabled.value),{primitiveElement:i,currentElement:a}=er(),{isPressed:c,onTrigger:l}=fc({target:a,disabled:r});return l(()=>{n.handleDecrease()}),(e,n)=>(k(),s(I(Z),x(t,{ref_key:`primitiveElement`,ref:i,tabindex:`-1`,"aria-label":`Decrease`,type:e.as===`button`?`button`:void 0,style:{userSelect:I(c)?`none`:void 0},disabled:r.value?``:void 0,"data-disabled":r.value?``:void 0,"data-pressed":I(c)?`true`:void 0,onContextmenu:n[0]||=V(()=>{},[`prevent`])}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`type`,`style`,`disabled`,`data-disabled`,`data-pressed`]))}}),bc=p({__name:`NumberFieldIncrement`,props:{disabled:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`button`}},setup(e){let t=e,n=gc(),r=o(()=>n.disabled?.value||n.readonly.value||t.disabled||n.isIncreaseDisabled.value),{primitiveElement:i,currentElement:a}=er(),{isPressed:c,onTrigger:l}=fc({target:a,disabled:r});return l(()=>{n.handleIncrease()}),(e,n)=>(k(),s(I(Z),x(t,{ref_key:`primitiveElement`,ref:i,tabindex:`-1`,"aria-label":`Increase`,type:e.as===`button`?`button`:void 0,style:{userSelect:I(c)?`none`:void 0},disabled:r.value?``:void 0,"data-disabled":r.value?``:void 0,"data-pressed":I(c)?`true`:void 0,onContextmenu:n[0]||=V(()=>{},[`prevent`])}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`type`,`style`,`disabled`,`data-disabled`,`data-pressed`]))}}),xc=p({__name:`NumberFieldInput`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`input`}},setup(e){let t=e,{primitiveElement:n,currentElement:r}=er(),i=gc();function a(e){i.disableWheelChange.value||e.target===on()&&(Math.abs(e.deltaY)<=Math.abs(e.deltaX)||(e.preventDefault(),e.deltaY>0?i.invertWheelChange.value?i.handleDecrease():i.handleIncrease():e.deltaY<0&&(i.invertWheelChange.value?i.handleIncrease():i.handleDecrease())))}D(()=>{i.onInputElement(r.value)});let o=j(i.textValue.value);R(()=>i.textValue.value,()=>{o.value=i.textValue.value},{immediate:!0,deep:!0});function c(){requestAnimationFrame(()=>{o.value=i.textValue.value})}return(e,r)=>(k(),s(I(Z),x(t,{id:I(i).id.value,ref_key:`primitiveElement`,ref:n,value:o.value,role:`spinbutton`,type:`text`,tabindex:`0`,inputmode:I(i).inputMode.value,disabled:I(i).disabled.value?``:void 0,"data-disabled":I(i).disabled.value?``:void 0,readonly:I(i).readonly.value?``:void 0,"data-readonly":I(i).readonly.value?``:void 0,autocomplete:`off`,autocorrect:`off`,spellcheck:`false`,"aria-roledescription":`Number field`,"aria-valuenow":I(i).modelValue.value,"aria-valuemin":I(i).min.value,"aria-valuemax":I(i).max.value,onKeydown:[r[0]||=le(V(e=>I(i).handleIncrease(),[`prevent`]),[`up`]),r[1]||=le(V(e=>I(i).handleDecrease(),[`prevent`]),[`down`]),r[2]||=le(V(e=>I(i).handleIncrease(10),[`prevent`]),[`page-up`]),r[3]||=le(V(e=>I(i).handleDecrease(10),[`prevent`]),[`page-down`]),r[4]||=le(V(e=>I(i).handleMinMaxValue(`min`),[`prevent`]),[`home`]),r[5]||=le(V(e=>I(i).handleMinMaxValue(`max`),[`prevent`]),[`end`]),r[8]||=le(e=>I(i).applyInputValue(e.target?.value),[`enter`])],onWheel:a,onBeforeinput:r[6]||=e=>{let t=e.target,n=t.value.slice(0,t.selectionStart??void 0)+(e.data??``)+t.value.slice(t.selectionEnd??void 0);I(i).validate(n)||e.preventDefault()},onInput:r[7]||=e=>{o.value=e.target.value},onChange:c,onBlur:r[9]||=e=>I(i).applyInputValue(e.target?.value)}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`id`,`value`,`inputmode`,`disabled`,`data-disabled`,`readonly`,`data-readonly`,`aria-valuenow`,`aria-valuemin`,`aria-valuemax`]))}});function Sc(e,t,n){sn(`radio.select`,n,{originalEvent:e,value:t})}var Cc=p({__name:`Radio`,props:{id:{type:String,required:!1},value:{type:null,required:!1},disabled:{type:Boolean,required:!1,default:!1},checked:{type:Boolean,required:!1,default:void 0},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`button`},name:{type:String,required:!1},required:{type:Boolean,required:!1}},emits:[`update:checked`,`select`],setup(e,{emit:t}){let n=e,r=t,i=xe(n,`checked`,r,{passive:n.checked===void 0}),{value:a}=F(n),{forwardRef:l,currentElement:u}=Y(),d=kn(u),f=o(()=>n.id&&u.value?document.querySelector(`[for="${n.id}"]`)?.innerText??n.value:void 0);function p(e){n.disabled||Sc(e,n.value,e=>{r(`select`,e),!e?.defaultPrevented&&(i.value=!0,d.value&&e.stopPropagation())})}return(e,t)=>(k(),s(I(Z),x(e.$attrs,{id:e.id,ref:I(l),role:`radio`,type:e.as===`button`?`button`:void 0,as:e.as,"aria-checked":I(i),"aria-label":f.value,"as-child":e.asChild,disabled:e.disabled?``:void 0,"data-state":I(i)?`checked`:`unchecked`,"data-disabled":e.disabled?``:void 0,value:I(a),required:e.required,name:e.name,onClick:V(p,[`stop`])}),{default:B(()=>[N(e.$slots,`default`,{checked:I(i)}),I(d)&&e.name?(k(),s(I(li),{key:0,type:`radio`,tabindex:`-1`,value:I(a),checked:!!I(i),name:e.name,disabled:e.disabled,required:e.required},null,8,[`value`,`checked`,`name`,`disabled`,`required`])):c(`v-if`,!0)]),_:3},16,[`id`,`type`,`as`,`aria-checked`,`aria-label`,`as-child`,`disabled`,`data-state`,`data-disabled`,`value`,`required`,`name`]))}});const[wc,Tc]=J(`RadioGroupRoot`);var Ec=p({__name:`RadioGroupRoot`,props:{modelValue:{type:null,required:!1},defaultValue:{type:null,required:!1},disabled:{type:Boolean,required:!1,default:!1},orientation:{type:String,required:!1,default:void 0},dir:{type:String,required:!1},loop:{type:Boolean,required:!1,default:!0},asChild:{type:Boolean,required:!1},as:{type:null,required:!1},name:{type:String,required:!1},required:{type:Boolean,required:!1,default:!1}},emits:[`update:modelValue`],setup(e,{emit:t}){let n=e,r=t,{forwardRef:i,currentElement:a}=Y(),o=xe(n,`modelValue`,r,{defaultValue:n.defaultValue,passive:n.modelValue===void 0}),{disabled:l,loop:u,orientation:d,name:p,required:m,dir:h}=F(n),g=wn(h),_=kn(a);return Tc({modelValue:o,changeModelValue:e=>{o.value=e},disabled:l,loop:u,orientation:d,name:p?.value,required:m}),(e,t)=>(k(),s(I(ai),{"as-child":``,orientation:I(d),dir:I(g),loop:I(u)},{default:B(()=>[f(I(Z),{ref:I(i),role:`radiogroup`,"data-disabled":I(l)?``:void 0,"as-child":e.asChild,as:e.as,"aria-orientation":I(d),"aria-required":I(m),dir:I(g)},{default:B(()=>[N(e.$slots,`default`,{modelValue:I(o)}),I(_)&&I(p)?(k(),s(I(li),{key:0,required:I(m),disabled:I(l),value:I(o),name:I(p)},null,8,[`required`,`disabled`,`value`,`name`])):c(`v-if`,!0)]),_:3},8,[`data-disabled`,`as-child`,`as`,`aria-orientation`,`aria-required`,`dir`])]),_:3},8,[`orientation`,`dir`,`loop`]))}});const[Dc,Oc]=J(`RadioGroupItem`);var kc=p({inheritAttrs:!1,__name:`RadioGroupItem`,props:{id:{type:String,required:!1},value:{type:null,required:!1},disabled:{type:Boolean,required:!1,default:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`button`},name:{type:String,required:!1},required:{type:Boolean,required:!1}},emits:[`select`],setup(e,{emit:t}){let n=e,r=t,{forwardRef:i,currentElement:a}=Y(),c=wc(),l=o(()=>c.disabled.value||n.disabled),u=o(()=>c.required.value||n.required),d=o(()=>$t(c.modelValue?.value,n.value));Oc({disabled:l,checked:d});let p=j(!1),m=[`ArrowUp`,`ArrowDown`,`ArrowLeft`,`ArrowRight`];ve(`keydown`,e=>{m.includes(e.key)&&(p.value=!0)}),ve(`keyup`,()=>{p.value=!1});function h(){setTimeout(()=>{p.value&&a.value?.click()},0)}return(e,t)=>(k(),s(I(oi),{checked:d.value,disabled:l.value,"as-child":``,focusable:!l.value,active:d.value},{default:B(()=>[f(Cc,x({...e.$attrs,...n},{ref:I(i),checked:d.value,required:u.value,disabled:l.value,"onUpdate:checked":t[0]||=t=>I(c).changeModelValue(e.value),onSelect:t[1]||=e=>r(`select`,e),onKeydown:t[2]||=le(V(()=>{},[`prevent`]),[`enter`]),onFocus:h}),{default:B(()=>[N(e.$slots,`default`,{checked:d.value,required:u.value,disabled:l.value})]),_:3},16,[`checked`,`required`,`disabled`])]),_:3},8,[`checked`,`disabled`,`focusable`,`active`]))}}),Ac=p({__name:`RadioGroupIndicator`,props:{forceMount:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`span`}},setup(e){let{forwardRef:t}=Y(),n=Dc();return(e,r)=>(k(),s(I(Zn),{present:e.forceMount||I(n).checked.value},{default:B(()=>[f(I(Z),x({ref:I(t),"data-state":I(n).checked.value?`checked`:`unchecked`,"data-disabled":I(n).disabled.value?``:void 0,"as-child":e.asChild,as:e.as},e.$attrs),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`data-state`,`data-disabled`,`as-child`,`as`])]),_:3},8,[`present`]))}}),jc=p({__name:`BubbleSelect`,props:{autocomplete:{type:String,required:!1},autofocus:{type:Boolean,required:!1},disabled:{type:Boolean,required:!1},form:{type:String,required:!1},multiple:{type:Boolean,required:!1},name:{type:String,required:!1},required:{type:Boolean,required:!1},size:{type:Number,required:!1},value:{type:null,required:!1}},setup(e){let t=e,n=j();return R(()=>t.value,(e,t)=>{let r=window.HTMLSelectElement.prototype,i=Object.getOwnPropertyDescriptor(r,`value`).set;if(e!==t&&i&&n.value){let t=new Event(`change`,{bubbles:!0});i.call(n.value,e),n.value.dispatchEvent(t)}}),(e,r)=>(k(),s(I(si),{"as-child":``},{default:B(()=>[u(`select`,x({ref_key:`selectElement`,ref:n},t),[N(e.$slots,`default`)],16)]),_:3}))}});const Mc=[` `,`Enter`,`ArrowUp`,`ArrowDown`],Nc=[` `,`Enter`];function Pc(e,t,n){return e===void 0?!1:Array.isArray(e)?e.some(e=>Fc(e,t,n)):Fc(e,t,n)}function Fc(e,t,n){return e===void 0||t===void 0?!1:typeof e==`string`?e===t:typeof n==`function`?n(e,t):typeof n==`string`?e?.[n]===t?.[n]:$t(e,t)}function Ic(e){return e==null||e===``||Array.isArray(e)&&e.length===0}const Lc={key:0,value:``},[Rc,zc]=J(`SelectRoot`);var Bc=p({inheritAttrs:!1,__name:`SelectRoot`,props:{open:{type:Boolean,required:!1,default:void 0},defaultOpen:{type:Boolean,required:!1},defaultValue:{type:null,required:!1},modelValue:{type:null,required:!1,default:void 0},by:{type:[String,Function],required:!1},dir:{type:String,required:!1},multiple:{type:Boolean,required:!1},autocomplete:{type:String,required:!1},disabled:{type:Boolean,required:!1},name:{type:String,required:!1},required:{type:Boolean,required:!1}},emits:[`update:modelValue`,`update:open`],setup(e,{emit:t}){let r=e,i=t,{required:a,disabled:u,multiple:d,dir:f}=F(r),p=xe(r,`modelValue`,i,{defaultValue:r.defaultValue??(d.value?[]:void 0),passive:r.modelValue===void 0,deep:!0}),m=xe(r,`open`,i,{defaultValue:r.defaultOpen,passive:r.open===void 0}),h=j(),g=j(),_=j({x:0,y:0}),v=o(()=>d.value&&Array.isArray(p.value)?p.value?.length===0:cn(p.value));Xr({isProvider:!0});let y=wn(f),b=kn(h),S=j(new Set),C=o(()=>Array.from(S.value).map(e=>e.value).join(`;`));function w(e){if(d.value){let t=Array.isArray(p.value)?[...p.value]:[],n=t.findIndex(t=>Fc(t,e,r.by));n===-1?t.push(e):t.splice(n,1),p.value=[...t]}else p.value=e}function T(e){return Array.from(S.value).find(t=>Pc(e,t.value,r.by))}return zc({triggerElement:h,onTriggerChange:e=>{h.value=e},valueElement:g,onValueElementChange:e=>{g.value=e},contentId:``,modelValue:p,onValueChange:w,by:r.by,open:m,multiple:d,required:a,onOpenChange:e=>{m.value=e},dir:y,triggerPointerDownPosRef:_,disabled:u,isEmptyModelValue:v,optionsSet:S,onOptionAdd:e=>{let t=T(e.value);t&&S.value.delete(t),S.value.add(e)},onOptionRemove:e=>{let t=T(e.value);t&&S.value.delete(t)}}),(e,t)=>(k(),s(I(bi),null,{default:B(()=>[N(e.$slots,`default`,{modelValue:I(p),open:I(m)}),I(b)?(k(),s(jc,{key:C.value,"aria-hidden":`true`,tabindex:`-1`,multiple:I(d),required:I(a),name:e.name,autocomplete:e.autocomplete,disabled:I(u),value:I(p)},{default:B(()=>[I(cn)(I(p))?(k(),l(`option`,Lc)):c(`v-if`,!0),(k(!0),l(n,null,M(Array.from(S.value),e=>(k(),l(`option`,x({key:e.value??``},{ref_for:!0},e),null,16))),128))]),_:1},8,[`multiple`,`required`,`name`,`autocomplete`,`disabled`,`value`])):c(`v-if`,!0)]),_:3}))}}),Vc=p({__name:`SelectPopperPosition`,props:{side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1,default:`start`},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1,default:10},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=An(e);return(e,n)=>(k(),s(I(Go),x(I(t),{style:{boxSizing:`border-box`,"--reka-select-content-transform-origin":`var(--reka-popper-transform-origin)`,"--reka-select-content-available-width":`var(--reka-popper-available-width)`,"--reka-select-content-available-height":`var(--reka-popper-available-height)`,"--reka-select-trigger-width":`var(--reka-popper-anchor-width)`,"--reka-select-trigger-height":`var(--reka-popper-anchor-height)`}}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}});const Hc={onViewportChange:()=>{},itemTextRefCallback:()=>{},itemRefCallback:()=>{}},[Uc,Wc]=J(`SelectContent`);var Gc=p({__name:`SelectContentImpl`,props:{position:{type:String,required:!1,default:`item-aligned`},bodyLock:{type:Boolean,required:!1,default:!0},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1,default:`start`},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`closeAutoFocus`,`escapeKeyDown`,`pointerDownOutside`],setup(e,{emit:t}){let n=e,r=t,i=Rc();Dn(),xn(n.bodyLock);let{CollectionSlot:a,getItems:c}=Xr(),l=j();Bn(l);let{search:u,handleTypeaheadSearch:d}=Kn(),p=j(),m=j(),h=j(),g=j(!1),_=j(!1),v=j(!1);function y(){m.value&&l.value&&Pr([m.value,l.value])}R(g,()=>{y()});let{onOpenChange:b,triggerPointerDownPosRef:S}=i;z(e=>{if(!l.value)return;let t={x:0,y:0},n=e=>{t={x:Math.abs(Math.round(e.pageX)-(S.value?.x??0)),y:Math.abs(Math.round(e.pageY)-(S.value?.y??0))}},r=e=>{e.pointerType!==`touch`&&(t.x<=10&&t.y<=10?e.preventDefault():l.value?.contains(e.target)||b(!1),document.removeEventListener(`pointermove`,n),S.value=null)};S.value!==null&&(document.addEventListener(`pointermove`,n),document.addEventListener(`pointerup`,r,{capture:!0,once:!0})),e(()=>{document.removeEventListener(`pointermove`,n),document.removeEventListener(`pointerup`,r,{capture:!0})})});function C(e){let t=e.ctrlKey||e.altKey||e.metaKey;if(e.key===`Tab`&&e.preventDefault(),!t&&e.key.length===1&&d(e.key,c()),[`ArrowUp`,`ArrowDown`,`Home`,`End`].includes(e.key)){let t=[...c().map(e=>e.ref)];if([`ArrowUp`,`End`].includes(e.key)&&(t=t.slice().reverse()),[`ArrowUp`,`ArrowDown`].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>Pr(t)),e.preventDefault()}}let w=o(()=>n.position===`popper`?n:{}),T=An(w.value);return Wc({content:l,viewport:p,onViewportChange:e=>{p.value=e},itemRefCallback:(e,t,n)=>{let r=!_.value&&!n,a=Pc(i.modelValue.value,t,i.by);if(i.multiple.value){if(v.value)return;(a||r)&&(m.value=e,a&&(v.value=!0))}else (a||r)&&(m.value=e);r&&(_.value=!0)},selectedItem:m,selectedItemText:h,onItemLeave:()=>{l.value?.focus()},itemTextRefCallback:(e,t,n)=>{let r=!_.value&&!n;(Pc(i.modelValue.value,t,i.by)||r)&&(h.value=e)},focusSelectedItem:y,position:n.position,isPositioned:g,searchRef:u}),(e,t)=>(k(),s(I(a),null,{default:B(()=>[f(I(wr),{"as-child":``,onMountAutoFocus:t[6]||=V(()=>{},[`prevent`]),onUnmountAutoFocus:t[7]||=e=>{r(`closeAutoFocus`,e),!e.defaultPrevented&&(I(i).triggerElement.value?.focus({preventScroll:!0}),e.preventDefault())}},{default:B(()=>[f(I(lr),{"as-child":``,"disable-outside-pointer-events":``,onFocusOutside:t[2]||=V(()=>{},[`prevent`]),onDismiss:t[3]||=e=>I(i).onOpenChange(!1),onEscapeKeyDown:t[4]||=e=>r(`escapeKeyDown`,e),onPointerDownOutside:t[5]||=e=>r(`pointerDownOutside`,e)},{default:B(()=>[(k(),s(te(e.position===`popper`?Vc:Jc),x({...e.$attrs,...I(T)},{id:I(i).contentId,ref:e=>{let t=I(ge)(e);t?.hasAttribute(`data-reka-popper-content-wrapper`)?l.value=t.firstElementChild:l.value=t},role:`listbox`,"data-state":I(i).open.value?`open`:`closed`,dir:I(i).dir.value,style:{display:`flex`,flexDirection:`column`,outline:`none`},onContextmenu:t[0]||=V(()=>{},[`prevent`]),onPlaced:t[1]||=e=>g.value=!0,onKeydown:C}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`id`,`data-state`,`dir`,`onKeydown`]))]),_:3})]),_:3})]),_:3}))}});const[Kc,qc]=J(`SelectItemAlignedPosition`);var Jc=p({inheritAttrs:!1,__name:`SelectItemAlignedPosition`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`placed`],setup(e,{emit:t}){let n=e,r=t,{getItems:i}=Xr(),a=Rc(),o=Uc(),s=j(!1),c=j(!0),u=j(),{forwardRef:d,currentElement:p}=Y(),{viewport:m,selectedItem:h,selectedItemText:g,focusSelectedItem:_}=o;function v(){if(a.triggerElement.value&&a.valueElement.value&&u.value&&p.value&&m?.value&&h?.value&&g?.value){let e=a.triggerElement.value.getBoundingClientRect(),t=p.value.getBoundingClientRect(),n=a.valueElement.value.getBoundingClientRect(),o=g.value.getBoundingClientRect();if(a.dir.value!==`rtl`){let r=o.left-t.left,i=n.left-r,a=e.left-i,s=e.width+a,c=Math.max(s,t.width),l=window.innerWidth-10,d=nn(i,10,Math.max(10,l-c));u.value.style.minWidth=`${s}px`,u.value.style.left=`${d}px`}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,a=window.innerWidth-e.right-i,s=e.width+a,c=Math.max(s,t.width),l=window.innerWidth-10,d=nn(i,10,Math.max(10,l-c));u.value.style.minWidth=`${s}px`,u.value.style.right=`${d}px`}let c=i().map(e=>e.ref),l=window.innerHeight-20,d=m.value.scrollHeight,f=window.getComputedStyle(p.value),_=Number.parseInt(f.borderTopWidth,10),v=Number.parseInt(f.paddingTop,10),y=Number.parseInt(f.borderBottomWidth,10),b=Number.parseInt(f.paddingBottom,10),x=_+v+d+b+y,S=Math.min(h.value.offsetHeight*5,x),C=window.getComputedStyle(m.value),w=Number.parseInt(C.paddingTop,10),T=Number.parseInt(C.paddingBottom,10),E=e.top+e.height/2-10,D=l-E,O=h.value.offsetHeight/2,k=h.value.offsetTop+O,A=_+v+k,ee=x-A;if(A<=E){let e=h.value===c[c.length-1];u.value.style.bottom=`0px`;let t=p.value.clientHeight-m.value.offsetTop-m.value.offsetHeight,n=Math.max(D,O+(e?T:0)+t+y),r=A+n;u.value.style.height=`${r}px`}else{let e=h.value===c[0];u.value.style.top=`0px`;let t=Math.max(E,_+m.value.offsetTop+(e?w:0)+O)+ee;u.value.style.height=`${t}px`,m.value.scrollTop=A-E+m.value.offsetTop}u.value.style.margin=`10px 0`,u.value.style.minHeight=`${S}px`,u.value.style.maxHeight=`${l}px`,r(`placed`),requestAnimationFrame(()=>s.value=!0)}}let y=j(``);D(async()=>{await S(),v(),p.value&&(y.value=window.getComputedStyle(p.value).zIndex)});function b(e){e&&c.value===!0&&(v(),_?.(),c.value=!1)}return be(a.triggerElement,()=>{v()}),qc({contentWrapper:u,shouldExpandOnScrollRef:s,onScrollButtonChange:b}),(e,t)=>(k(),l(`div`,{ref_key:`contentWrapperElement`,ref:u,style:T({display:`flex`,flexDirection:`column`,position:`fixed`,zIndex:y.value})},[f(I(Z),x({ref:I(d),style:{boxSizing:`border-box`,maxHeight:`100%`}},{...e.$attrs,...n}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16)],4))}}),Yc=p({inheritAttrs:!1,__name:`SelectProvider`,props:{context:{type:Object,required:!0}},setup(e){return zc(e.context),Wc(Hc),(e,t)=>N(e.$slots,`default`)}});const Xc={key:1};var Zc=p({inheritAttrs:!1,__name:`SelectContent`,props:{forceMount:{type:Boolean,required:!1},position:{type:String,required:!1},bodyLock:{type:Boolean,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`closeAutoFocus`,`escapeKeyDown`,`pointerDownOutside`],setup(e,{emit:t}){let n=e,i=X(n,t),a=Rc(),u=j();D(()=>{u.value=new DocumentFragment});let d=j(),p=o(()=>n.forceMount||a.open.value),m=j(p.value);return R(p,()=>{setTimeout(()=>m.value=p.value)}),(e,t)=>p.value||m.value||d.value?.present?(k(),s(I(Zn),{key:0,ref_key:`presenceRef`,ref:d,present:p.value},{default:B(()=>[f(Gc,w(h({...I(i),...e.$attrs})),{default:B(()=>[N(e.$slots,`default`)]),_:3},16)]),_:3},8,[`present`])):u.value?(k(),l(`div`,Xc,[(k(),s(r,{to:u.value},[f(Yc,{context:I(a)},{default:B(()=>[N(e.$slots,`default`)]),_:3},8,[`context`])],8,[`to`]))])):c(`v-if`,!0)}});const[Qc,$c]=J(`SelectGroup`);var el=p({__name:`SelectGroup`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=e,n=Hn(void 0,`reka-select-group`);return $c({id:n}),(e,r)=>(k(),s(I(Z),x({role:`group`},t,{"aria-labelledby":I(n)}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`aria-labelledby`]))}}),tl=p({__name:`SelectIcon`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`span`}},setup(e){return(e,t)=>(k(),s(I(Z),{"aria-hidden":`true`,as:e.as,"as-child":e.asChild},{default:B(()=>[N(e.$slots,`default`,{},()=>[t[0]||=d(`▼`)])]),_:3},8,[`as`,`as-child`]))}});const[nl,rl]=J(`SelectItem`);var il=p({__name:`SelectItem`,props:{value:{type:null,required:!0},disabled:{type:Boolean,required:!1},textValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`select`],setup(e,{emit:t}){let n=e,r=t,{disabled:i}=F(n),a=Rc(),c=Uc(),{forwardRef:l,currentElement:u}=Y(),{CollectionItem:d}=Xr(),p=o(()=>Pc(a.modelValue?.value,n.value,a.by)),m=j(!1),h=j(n.textValue??``),g=Hn(void 0,`reka-select-item-text`);async function _(e){if(e.defaultPrevented)return;let t={originalEvent:e,value:n.value};sn(`select.select`,v,t)}async function v(e){await S(),r(`select`,e),!e.defaultPrevented&&(i.value||(a.onValueChange(n.value),a.multiple.value||a.onOpenChange(!1)))}async function y(e){await S(),!e.defaultPrevented&&(i.value?c.onItemLeave?.():e.currentTarget?.focus({preventScroll:!0}))}async function b(e){await S(),!e.defaultPrevented&&e.currentTarget===on()&&c.onItemLeave?.()}async function x(e){await S(),!e.defaultPrevented&&(c.searchRef?.value!==``&&e.key===` `||(Nc.includes(e.key)&&_(e),e.key===` `&&e.preventDefault()))}if(n.value===``)throw Error(`A <SelectItem /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.`);return D(()=>{u.value&&c.itemRefCallback(u.value,n.value,n.disabled)}),rl({value:n.value,disabled:i,textId:g,isSelected:p,onItemTextChange:e=>{h.value=((h.value||e?.textContent)??``).trim()}}),(e,t)=>(k(),s(I(d),{value:{textValue:h.value}},{default:B(()=>[f(I(Z),{ref:I(l),role:`option`,"aria-labelledby":I(g),"data-highlighted":m.value?``:void 0,"aria-selected":p.value,"data-state":p.value?`checked`:`unchecked`,"aria-disabled":I(i)||void 0,"data-disabled":I(i)?``:void 0,tabindex:I(i)?void 0:-1,as:e.as,"as-child":e.asChild,onFocus:t[0]||=e=>m.value=!0,onBlur:t[1]||=e=>m.value=!1,onPointerup:_,onPointerdown:t[2]||=e=>{e.currentTarget.focus({preventScroll:!0})},onTouchend:t[3]||=V(()=>{},[`prevent`,`stop`]),onPointermove:y,onPointerleave:b,onKeydown:x},{default:B(()=>[N(e.$slots,`default`)]),_:3},8,[`aria-labelledby`,`data-highlighted`,`aria-selected`,`data-state`,`aria-disabled`,`data-disabled`,`tabindex`,`as`,`as-child`])]),_:3},8,[`value`]))}}),al=p({__name:`SelectItemIndicator`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`span`}},setup(e){let t=e,n=nl();return(e,r)=>I(n).isSelected.value?(k(),s(I(Z),x({key:0,"aria-hidden":`true`},t),{default:B(()=>[N(e.$slots,`default`)]),_:3},16)):c(`v-if`,!0)}}),ol=p({inheritAttrs:!1,__name:`SelectItemText`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`span`}},setup(e){let t=e,n=Rc(),r=Uc(),i=nl(),{forwardRef:a,currentElement:c}=Y(),l=o(()=>({value:i.value,disabled:i.disabled.value,textContent:c.value?.textContent??i.value?.toString()??``}));return D(()=>{c.value&&(i.onItemTextChange(c.value),r.itemTextRefCallback(c.value,i.value,i.disabled.value),n.onOptionAdd(l.value))}),O(()=>{n.onOptionRemove(l.value)}),(e,n)=>(k(),s(I(Z),x({id:I(i).textId,ref:I(a)},{...t,...e.$attrs}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`id`]))}}),sl=p({__name:`SelectLabel`,props:{for:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`div`}},setup(e){let t=e,n=Qc({id:``});return(e,r)=>(k(),s(I(Z),x(t,{id:I(n).id}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`id`]))}}),cl=p({__name:`SelectPortal`,props:{to:{type:null,required:!1},disabled:{type:Boolean,required:!1},defer:{type:Boolean,required:!1},forceMount:{type:Boolean,required:!1}},setup(e){let t=e;return(e,n)=>(k(),s(I(Gr),w(h(t)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),ll=p({__name:`SelectScrollButtonImpl`,emits:[`autoScroll`],setup(e,{emit:t}){let n=t,{getItems:r}=Xr(),i=Uc(),a=j(null);function o(){a.value!==null&&(window.clearInterval(a.value),a.value=null)}z(()=>{r().map(e=>e.ref).find(e=>e===on())?.scrollIntoView({block:`nearest`})});function c(){a.value===null&&(a.value=window.setInterval(()=>{n(`autoScroll`)},50))}function l(){i.onItemLeave?.(),a.value===null&&(a.value=window.setInterval(()=>{n(`autoScroll`)},50))}return E(()=>o()),(e,t)=>(k(),s(I(Z),x({"aria-hidden":`true`,style:{flexShrink:0}},e.$parent?.$props,{onPointerdown:c,onPointermove:l,onPointerleave:t[0]||=()=>{o()}}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),ul=p({__name:`SelectScrollDownButton`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=Uc(),n=t.position===`item-aligned`?Kc():void 0,{forwardRef:r,currentElement:i}=Y(),a=j(!1);return z(e=>{if(t.viewport?.value&&t.isPositioned?.value){let n=t.viewport.value;function r(){let e=n.scrollHeight-n.clientHeight;a.value=Math.ceil(n.scrollTop)<e}r(),n.addEventListener(`scroll`,r),e(()=>n.removeEventListener(`scroll`,r))}}),R(i,()=>{i.value&&n?.onScrollButtonChange(i.value)}),(e,n)=>a.value?(k(),s(ll,{key:0,ref:I(r),onAutoScroll:n[0]||=()=>{let{viewport:e,selectedItem:n}=I(t);e?.value&&n?.value&&(e.value.scrollTop=e.value.scrollTop+n.value.offsetHeight)}},{default:B(()=>[N(e.$slots,`default`)]),_:3},512)):c(`v-if`,!0)}}),dl=p({__name:`SelectScrollUpButton`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=Uc(),n=t.position===`item-aligned`?Kc():void 0,{forwardRef:r,currentElement:i}=Y(),a=j(!1);return z(e=>{if(t.viewport?.value&&t.isPositioned?.value){let n=t.viewport.value;function r(){a.value=n.scrollTop>0}r(),n.addEventListener(`scroll`,r),e(()=>n.removeEventListener(`scroll`,r))}}),R(i,()=>{i.value&&n?.onScrollButtonChange(i.value)}),(e,n)=>a.value?(k(),s(ll,{key:0,ref:I(r),onAutoScroll:n[0]||=()=>{let{viewport:e,selectedItem:n}=I(t);e?.value&&n?.value&&(e.value.scrollTop=e.value.scrollTop-n.value.offsetHeight)}},{default:B(()=>[N(e.$slots,`default`)]),_:3},512)):c(`v-if`,!0)}}),fl=p({__name:`SelectSeparator`,props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=e;return(e,n)=>(k(),s(I(Z),x({"aria-hidden":`true`},t),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),pl=p({__name:`SelectTrigger`,props:{disabled:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`button`}},setup(e){let t=e,n=Rc(),{forwardRef:r,currentElement:i}=Y(),a=o(()=>n.disabled?.value||t.disabled);n.contentId||=Hn(void 0,`reka-select-content`),D(()=>{n.onTriggerChange(i.value)});let{getItems:c}=Xr(),{search:l,handleTypeaheadSearch:u,resetTypeahead:d}=Kn();function p(){a.value||(n.onOpenChange(!0),d())}function m(e){p(),n.triggerPointerDownPosRef.value={x:Math.round(e.pageX),y:Math.round(e.pageY)}}return(e,t)=>(k(),s(I(xi),{"as-child":``,reference:e.reference},{default:B(()=>[f(I(Z),{ref:I(r),role:`combobox`,type:e.as===`button`?`button`:void 0,"aria-controls":I(n).contentId,"aria-expanded":I(n).open.value||!1,"aria-required":I(n).required?.value,"aria-autocomplete":`none`,disabled:a.value,dir:I(n)?.dir.value,"data-state":I(n)?.open.value?`open`:`closed`,"data-disabled":a.value?``:void 0,"data-placeholder":I(Ic)(I(n).modelValue?.value)?``:void 0,"as-child":e.asChild,as:e.as,onClick:t[0]||=e=>{(e?.currentTarget)?.focus()},onPointerdown:t[1]||=e=>{if(e.pointerType===`touch`)return e.preventDefault();let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),e.button===0&&e.ctrlKey===!1&&(m(e),e.preventDefault())},onPointerup:t[2]||=V(e=>{e.pointerType===`touch`&&m(e)},[`prevent`]),onKeydown:t[3]||=e=>{let t=I(l)!==``;!(e.ctrlKey||e.altKey||e.metaKey)&&e.key.length===1&&t&&e.key===` `||(I(u)(e.key,I(c)()),I(Mc).includes(e.key)&&(p(),e.preventDefault()))}},{default:B(()=>[N(e.$slots,`default`)]),_:3},8,[`type`,`aria-controls`,`aria-expanded`,`aria-required`,`disabled`,`dir`,`data-state`,`data-disabled`,`data-placeholder`,`as-child`,`as`])]),_:3},8,[`reference`]))}}),ml=p({__name:`SelectValue`,props:{placeholder:{type:String,required:!1,default:``},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:`span`}},setup(e){let t=e,{forwardRef:n,currentElement:r}=Y(),i=Rc();D(()=>{i.valueElement=r});let a=o(()=>{let e=[],t=Array.from(i.optionsSet.value),n=e=>t.find(t=>Pc(e,t.value,i.by));return e=Array.isArray(i.modelValue.value)?i.modelValue.value.map(e=>n(e)?.textContent??``):[n(i.modelValue.value)?.textContent??``],e.filter(Boolean)}),c=o(()=>a.value.length?a.value.join(`, `):t.placeholder);return(e,r)=>(k(),s(I(Z),{ref:I(n),as:e.as,"as-child":e.asChild,style:{pointerEvents:`none`},"data-placeholder":a.value.length?void 0:t.placeholder},{default:B(()=>[N(e.$slots,`default`,{selectedLabel:a.value,modelValue:I(i).modelValue.value},()=>[d(P(c.value),1)])]),_:3},8,[`as`,`as-child`,`data-placeholder`]))}}),hl=p({__name:`SelectViewport`,props:{nonce:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){let t=e,{nonce:r}=F(t),i=Ko(r),a=Uc(),o=a.position===`item-aligned`?Kc():void 0,{forwardRef:s,currentElement:c}=Y();D(()=>{a?.onViewportChange(c.value)});let u=j(0);function p(e){let t=e.currentTarget,{shouldExpandOnScrollRef:n,contentWrapper:r}=o??{};if(n?.value&&r?.value){let e=Math.abs(u.value-t.scrollTop);if(e>0){let n=window.innerHeight-20,i=Number.parseFloat(r.value.style.minHeight),a=Number.parseFloat(r.value.style.height),o=Math.max(i,a);if(o<n){let i=o+e,a=Math.min(n,i),s=i-a;r.value.style.height=`${a}px`,r.value.style.bottom===`0px`&&(t.scrollTop=s>0?s:0,r.value.style.justifyContent=`flex-end`)}}}u.value=t.scrollTop}return(e,r)=>(k(),l(n,null,[f(I(Z),x({ref:I(s),"data-reka-select-viewport":``,role:`presentation`},{...e.$attrs,...t},{style:{position:`relative`,flex:1,overflow:`hidden auto`},onScroll:p}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16),f(I(Z),{as:`style`,nonce:I(i)},{default:B(()=>r[0]||=[d(` /* Hide scrollbars cross-browser and enable momentum scroll for touch devices */ [data-reka-select-viewport] { scrollbar-width:none; -ms-overflow-style: none; -webkit-overflow-scrolling: touch; } [data-reka-select-viewport]::-webkit-scrollbar { display: none; } `)]),_:1,__:[0]},8,[`nonce`])],64))}});function $(e,t=`Assertion failed!`){if(!e)throw console.error(t),Error(t)}function gl(e,t=document){return tn?t instanceof HTMLElement&&t?.dataset?.panelGroupId===e?t:t.querySelector(`[data-panel-group][data-panel-group-id="${e}"]`)||null:null}function _l(e,t=document){return tn&&t.querySelector(`[data-panel-resize-handle-id="${e}"]`)||null}function vl(e,t,n=document){return tn?yl(e,n).findIndex(e=>e.getAttribute(`data-panel-resize-handle-id`)===t)??null:null}function yl(e,t=document){return tn?Array.from(t.querySelectorAll(`[data-panel-resize-handle-id][data-panel-group-id="${e}"]`)):[]}function bl(e,t,n,r=document){let i=_l(t,r),a=yl(e,r),o=i?a.indexOf(i):-1,s=n[o]?.id??null,c=n[o+1]?.id??null;return[s,c]}function xl(e){return e.type===`keydown`}function Sl(e){return e.type.startsWith(`mouse`)}function Cl(e){return e.type.startsWith(`touch`)}function wl(e){if(Sl(e))return{x:e.clientX,y:e.clientY};if(Cl(e)){let t=e.touches[0];if(t&&t.clientX&&t.clientY)return{x:t.clientX,y:t.clientY}}return{x:1/0,y:1/0}}function Tl(e,t){let n=e===`horizontal`,{x:r,y:i}=wl(t);return n?r:i}function El(e,t,n,r,i){let a=n===`horizontal`,o=_l(t,i);$(o);let s=o.getAttribute(`data-panel-group-id`);$(s);let{initialCursorPosition:c}=r,l=Tl(n,e),u=gl(s,i);$(u);let d=u.getBoundingClientRect(),f=a?d.width:d.height;return(l-c)/f*100}function Dl(e,t,n,r,i,a){if(xl(e)){let t=n===`horizontal`,r=0;r=e.shiftKey?100:i??10;let a=0;switch(e.key){case`ArrowDown`:a=t?0:r;break;case`ArrowLeft`:a=t?-r:0;break;case`ArrowRight`:a=t?r:0;break;case`ArrowUp`:a=t?0:-r;break;case`End`:a=100;break;case`Home`:a=-100;break}return a}else return r==null?0:El(e,t,n,r,a)}function Ol({layout:e,panelsArray:t,pivotIndices:n}){let r=0,i=100,a=0,o=0,s=n[0];$(s!=null),t.forEach((e,t)=>{let{constraints:n}=e,{maxSize:c=100,minSize:l=0}=n;t===s?(r=l,i=c):(a+=l,o+=c)});let c=Math.min(i,100-a),l=Math.max(r,100-o),u=e[s];return{valueMax:c,valueMin:l,valueNow:u}}function kl({panelDataArray:e}){let t=Array.from({length:e.length}),n=e.map(e=>e.constraints),r=0,i=100;for(let a=0;a<e.length;a++){let e=n[a];$(e);let{defaultSize:o}=e;o!=null&&(r++,t[a]=o,i-=o)}for(let a=0;a<e.length;a++){let o=n[a];$(o);let{defaultSize:s}=o;if(s!=null)continue;let c=e.length-r,l=i/c;r++,t[a]=l,i-=l}return t}function Al(e,t,n){t.forEach((t,r)=>{let i=e[r];$(i);let{callbacks:a,constraints:o,id:s}=i,{collapsedSize:c=0,collapsible:l}=o,u=n[s];if(u==null||t!==u){n[s]=t;let{onCollapse:e,onExpand:r,onResize:i}=a;i&&i(t,u),l&&(e||r)&&(r&&(u==null||u===c)&&t!==c&&r(),e&&(u==null||u!==c)&&t===c&&e())}})}function jl(e,t=10){let n=null;return(...r)=>{n!==null&&clearTimeout(n),n=setTimeout(()=>{e(...r)},t)}}function Ml(e,t,n=10){e=Number.parseFloat(e.toFixed(n)),t=Number.parseFloat(t.toFixed(n));let r=e-t;return r===0?0:r>0?1:-1}function Nl(e,t,n){return Ml(e,t,n)===0}function Pl({panelConstraints:e,panelIndex:t,size:n}){let r=e[t];$(r!=null);let{collapsedSize:i=0,collapsible:a,maxSize:o=100,minSize:s=0}=r;if(Ml(n,s)<0)if(a){let e=(i+s)/2;n=Ml(n,e)<0?i:s}else n=s;return n=Math.min(o,n),n=Number.parseFloat(n.toFixed(10)),n}function Fl(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function Il({delta:e,layout:t,panelConstraints:n,pivotIndices:r,trigger:i}){if(Nl(e,0))return t;let a=[...t],[o,s]=r;$(o!=null),$(s!=null);let c=0;if(i===`keyboard`){{let r=e<0?s:o,i=n[r];if($(i),i.collapsible){let i=t[r];$(i!=null);let a=n[r];$(a);let{collapsedSize:o=0,minSize:s=0}=a;if(Nl(i,o)){let t=s-i;Ml(t,Math.abs(e))>0&&(e=e<0?0-t:t)}}}{let r=e<0?o:s,i=n[r];$(i);let{collapsible:a}=i;if(a){let i=t[r];$(i!=null);let a=n[r];$(a);let{collapsedSize:o=0,minSize:s=0}=a;if(Nl(i,s)){let t=i-o;Ml(t,Math.abs(e))>0&&(e=e<0?0-t:t)}}}}{let r=e<0?1:-1,i=e<0?s:o,a=0;for(;;){let e=t[i];$(e!=null);let o=Pl({panelConstraints:n,panelIndex:i,size:100})-e;if(a+=o,i+=r,i<0||i>=n.length)break}let c=Math.min(Math.abs(e),Math.abs(a));e=e<0?0-c:c}{let r=e<0?o:s;for(;r>=0&&r<n.length;){let i=Math.abs(e)-Math.abs(c),o=t[r];$(o!=null);let s=o-i,l=Pl({panelConstraints:n,panelIndex:r,size:s});if(!Nl(o,l)&&(c+=o-l,a[r]=l,c.toPrecision(3).localeCompare(Math.abs(e).toPrecision(3),void 0,{numeric:!0})>=0))break;e<0?r--:r++}}if(Nl(c,0))return t;{let r=e<0?s:o,i=t[r];$(i!=null);let l=i+c,u=Pl({panelConstraints:n,panelIndex:r,size:l});if(a[r]=u,!Nl(u,l)){let t=l-u,r=e<0?s:o;for(;r>=0&&r<n.length;){let i=a[r];$(i!=null);let o=i+t,s=Pl({panelConstraints:n,panelIndex:r,size:o});if(Nl(i,s)||(t-=s-i,a[r]=s),Nl(t,0))break;e>0?r--:r++}}}let l=a.reduce((e,t)=>t+e,0);return Nl(l,100)?a:t}function Ll(e,t,n){let r=vl(e,t,n);return r==null?[-1,-1]:[r,r+1]}function Rl(e,t,n){return n?e.x<t.x+t.width&&e.x+e.width>t.x&&e.y<t.y+t.height&&e.y+e.height>t.y:e.x<=t.x+t.width&&e.x+e.width>=t.x&&e.y<=t.y+t.height&&e.y+e.height>=t.y}function zl(e,t){if(e===t)throw Error(`Cannot compare node with itself`);let n={a:Gl(e),b:Gl(t)},r;for(;n.a.at(-1)===n.b.at(-1);)e=n.a.pop(),t=n.b.pop(),r=e;$(r);let i={a:Wl(Ul(n.a)),b:Wl(Ul(n.b))};if(i.a===i.b){let e=r.childNodes,t={a:n.a.at(-1),b:n.b.at(-1)},i=e.length;for(;i--;){let n=e[i];if(n===t.a)return 1;if(n===t.b)return-1}}return Math.sign(i.a-i.b)}const Bl=/\b(?:position|zIndex|opacity|transform|webkitTransform|mixBlendMode|filter|webkitFilter|isolation)\b/;function Vl(e){let t=getComputedStyle(Kl(e)).display;return t===`flex`||t===`inline-flex`}function Hl(e){let t=getComputedStyle(e);return!!(t.position===`fixed`||t.zIndex!==`auto`&&(t.position!==`static`||Vl(e))||+t.opacity<1||`transform`in t&&t.transform!==`none`||`webkitTransform`in t&&t.webkitTransform!==`none`||`mixBlendMode`in t&&t.mixBlendMode!==`normal`||`filter`in t&&t.filter!==`none`||`webkitFilter`in t&&t.webkitFilter!==`none`||`isolation`in t&&t.isolation===`isolate`||Bl.test(t.willChange)||t.webkitOverflowScrolling===`touch`)}function Ul(e){let t=e.length;for(;t--;){let n=e[t];if($(n),Hl(n))return n}return null}function Wl(e){return e&&Number(getComputedStyle(e).zIndex)||0}function Gl(e){let t=[];for(;e;)t.push(e),e=Kl(e);return t}function Kl(e){return e.parentNode instanceof DocumentFragment&&e.parentNode?.host||e.parentNode}function ql(){if(typeof matchMedia==`function`)return matchMedia(`(pointer:coarse)`).matches?`coarse`:`fine`}const Jl=ql()===`coarse`,Yl=[];let Xl=!1;const Zl=new Map,Ql=new Map,$l=new Set;function eu(e,t,n,r,i,a){let{ownerDocument:o}=t,s={direction:n,element:t,hitAreaMargins:r,nonce:i,setResizeHandlerState:a},c=Zl.get(o)??0;return Zl.set(o,c+1),$l.add(s),su(),function(){Ql.delete(e),$l.delete(s);let t=Zl.get(o)??1;Zl.set(o,t-1),su(),fu(),t===1&&Zl.delete(o)}}function tu(e){let{target:t}=e,{x:n,y:r}=wl(e);Xl=!0,iu({target:t,x:n,y:r}),su(),Yl.length>0&&(cu(`down`,e),e.preventDefault())}function nu(e){let{x:t,y:n}=wl(e);if(!Xl){let{target:r}=e;iu({target:r,x:t,y:n})}cu(`move`,e),ou(),Yl.length>0&&e.preventDefault()}function ru(e){let{target:t}=e,{x:n,y:r}=wl(e);Ql.clear(),Xl=!1,Yl.length>0&&e.preventDefault(),cu(`up`,e),iu({target:t,x:n,y:r}),ou(),su()}function iu({target:e,x:t,y:n}){Yl.splice(0);let r=null;e instanceof HTMLElement&&(r=e),$l.forEach(e=>{let{element:i,hitAreaMargins:a}=e,o=i.getBoundingClientRect(),{bottom:s,left:c,right:l,top:u}=o,d=Jl?a.coarse:a.fine;if(t>=c-d&&t<=l+d&&n>=u-d&&n<=s+d){if(r!==null&&i!==r&&!i.contains(r)&&!r.contains(i)&&zl(r,i)>0){let e=r,t=!1;for(;e&&!e.contains(i);){if(Rl(e.getBoundingClientRect(),o,!0)){t=!0;break}e=e.parentElement}if(t)return}Yl.push(e)}})}function au(e,t){Ql.set(e,t)}function ou(){let e=!1,t=!1,n;Yl.forEach(r=>{let{direction:i,nonce:a}=r;i.value===`horizontal`?e=!0:t=!0,n=a.value});let r=0;Ql.forEach(e=>{r|=e}),e&&t?pu(`intersection`,r,n):e?pu(`horizontal`,r,n):t?pu(`vertical`,r,n):fu()}function su(){Zl.forEach((e,t)=>{let{body:n}=t;n.removeEventListener(`contextmenu`,ru),n.removeEventListener(`mousedown`,tu),n.removeEventListener(`mouseleave`,nu),n.removeEventListener(`mousemove`,nu),n.removeEventListener(`touchmove`,nu),n.removeEventListener(`touchstart`,tu)}),window.removeEventListener(`mouseup`,ru),window.removeEventListener(`touchcancel`,ru),window.removeEventListener(`touchend`,ru),$l.size>0&&(Xl?(Yl.length>0&&Zl.forEach((e,t)=>{let{body:n}=t;e>0&&(n.addEventListener(`contextmenu`,ru),n.addEventListener(`mouseleave`,nu),n.addEventListener(`mousemove`,nu),n.addEventListener(`touchmove`,nu,{passive:!1}))}),window.addEventListener(`mouseup`,ru),window.addEventListener(`touchcancel`,ru),window.addEventListener(`touchend`,ru)):Zl.forEach((e,t)=>{let{body:n}=t;e>0&&(n.addEventListener(`mousedown`,tu),n.addEventListener(`mousemove`,nu),n.addEventListener(`touchmove`,nu,{passive:!1}),n.addEventListener(`touchstart`,tu))}))}function cu(e,t){$l.forEach(n=>{let{setResizeHandlerState:r}=n,i=Yl.includes(n);r(e,i,t)})}let lu=null,uu=null;function du(e,t){if(t){let e=(t&1)!=0,n=(t&2)!=0,r=(t&4)!=0,i=(t&8)!=0;if(e)return r?`se-resize`:i?`ne-resize`:`e-resize`;if(n)return r?`sw-resize`:i?`nw-resize`:`w-resize`;if(r)return`s-resize`;if(i)return`n-resize`}switch(e){case`horizontal`:return`ew-resize`;case`intersection`:return`move`;case`vertical`:return`ns-resize`}}function fu(){uu!==null&&(document.head.removeChild(uu),lu=null,uu=null)}function pu(e,t,n){let r=du(e,t);lu!==r&&(lu=r,uu===null&&(uu=document.createElement(`style`),n&&(uu.nonce=n),document.head.appendChild(uu)),uu.innerHTML=`*{cursor: ${r}!important;}`)}function mu({defaultSize:e,dragState:t,layout:n,panelData:r,panelIndex:i,precision:a=3}){let o=n[i],s;return s=o==null?e===void 0?`1`:e.toPrecision(a):r.length===1?`1`:o.toPrecision(a),{flexBasis:0,flexGrow:s,flexShrink:1,overflow:`hidden`,pointerEvents:t===null?void 0:`none`}}function hu({layout:e,panelConstraints:t}){let n=[...e],r=n.reduce((e,t)=>e+t,0);if(n.length!==t.length)throw Error(`Invalid ${t.length} panel layout: ${n.map(e=>`${e}%`).join(`, `)}`);if(!Nl(r,100)){console.warn(`WARNING: Invalid layout total size: ${n.map(e=>`${e}%`).join(`, `)}. Layout normalization will be applied.`);for(let e=0;e<t.length;e++){let t=n[e];$(t!=null),n[e]=100/r*t}}let i=0;for(let e=0;e<t.length;e++){let r=n[e];$(r!=null);let a=Pl({panelConstraints:t,panelIndex:e,size:r});r!==a&&(i+=r-a,n[e]=a)}if(!Nl(i,0))for(let e=0;e<t.length;e++){let r=n[e];$(r!=null);let a=r+i,o=Pl({panelConstraints:t,panelIndex:e,size:a});if(r!==o&&(i-=o-r,n[e]=o,Nl(i,0)))break}return n}function gu({eagerValuesRef:e,groupId:t,layout:n,panelDataArray:r,panelGroupElement:i,setLayout:a}){z(e=>{let a=i.value;if(!a)return;let o=yl(t,a);for(let e=0;e<r.length-1;e++){let{valueMax:t,valueMin:i,valueNow:a}=Ol({layout:n.value,panelsArray:r,pivotIndices:[e,e+1]}),s=o[e];if(s!=null){let n=r[e];$(n),s.setAttribute(`aria-controls`,n.id),s.setAttribute(`aria-valuemax`,`${Math.round(t)}`),s.setAttribute(`aria-valuemin`,`${Math.round(i)}`),s.setAttribute(`aria-valuenow`,a==null?``:`${Math.round(a)}`)}}e(()=>{o.forEach(e=>{e.removeAttribute(`aria-controls`),e.removeAttribute(`aria-valuemax`),e.removeAttribute(`aria-valuemin`),e.removeAttribute(`aria-valuenow`)})})}),z(r=>{let o=i.value;if(!o)return;let s=e.value;$(s);let{panelDataArray:c}=s,l=gl(t,o);$(l!=null,`No group found for id "${t}"`);let u=yl(t,o);$(u);let d=u.map(e=>{let r=e.getAttribute(`data-panel-resize-handle-id`);$(r);let[i,s]=bl(t,r,c,o);if(i==null||s==null)return()=>{};let l=e=>{if(!e.defaultPrevented)switch(e.key){case`Enter`:{e.preventDefault();let s=c.findIndex(e=>e.id===i);if(s>=0){let e=c[s];$(e);let i=n.value[s],{collapsedSize:l=0,collapsible:u,minSize:d=0}=e.constraints;if(i!=null&&u){let e=Il({delta:Nl(i,l)?d-l:l-i,layout:n.value,panelConstraints:c.map(e=>e.constraints),pivotIndices:Ll(t,r,o),trigger:`keyboard`});n.value!==e&&a(e)}}break}}};return e.addEventListener(`keydown`,l),()=>{e.removeEventListener(`keydown`,l)}});r(()=>{d.forEach(e=>e())})})}function _u(e){try{if(typeof localStorage<`u`)e.getItem=e=>localStorage.getItem(e),e.setItem=(e,t)=>{localStorage.setItem(e,t)};else throw TypeError(`localStorage not supported in this environment`)}catch(t){console.error(t),e.getItem=()=>null,e.setItem=()=>{}}}function vu(e){return`reka:${e}`}function yu(e){return e.map(e=>{let{constraints:t,id:n,idIsFromProps:r,order:i}=e;return r?n:i?`${i}:${JSON.stringify(t)}`:JSON.stringify(t)}).sort((e,t)=>e.localeCompare(t)).join(`,`)}function bu(e,t){try{let n=vu(e),r=t.getItem(n);if(r){let e=JSON.parse(r);if(typeof e==`object`&&e)return e}}catch{}return null}function xu(e,t,n){let r=bu(e,n)??{},i=yu(t);return r[i]??null}function Su(e,t,n,r,i){let a=vu(e),o=yu(t),s=bu(e,i)??{};s[o]={expandToSizes:Object.fromEntries(n.entries()),layout:r};try{i.setItem(a,JSON.stringify(s))}catch(e){console.error(e)}}const Cu={getItem:e=>(_u(Cu),Cu.getItem(e)),setItem:(e,t)=>{_u(Cu),Cu.setItem(e,t)}},[wu,Tu]=J(`PanelGroup`);var Eu=p({__name:`SplitterGroup`,props:{id:{type:[String,null],required:!1},autoSaveId:{type:[String,null],required:!1,default:null},direction:{type:String,required:!0},keyboardResizeBy:{type:[Number,null],required:!1,default:10},storage:{type:Object,required:!1,default:()=>Cu},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`layout`],setup(e,{emit:t}){let n=e,r=t,i={},{direction:a}=F(n),c=Hn(n.id,`reka-splitter-group`),l=wn(),{forwardRef:u,currentElement:d}=Y(),f=j(null),p=j([]),m=j({}),h=j(new Map),g=j(0),_=o(()=>({autoSaveId:n.autoSaveId,direction:n.direction,dragState:f.value,id:c,keyboardResizeBy:n.keyboardResizeBy,storage:n.storage})),v=j({layout:p.value,panelDataArray:[],panelDataArrayChanged:!1}),y=e=>p.value=e;gu({eagerValuesRef:v,groupId:c,layout:p,panelDataArray:v.value.panelDataArray,setLayout:y,panelGroupElement:d}),z(()=>{let{panelDataArray:e}=v.value,{autoSaveId:t}=n;if(t){if(p.value.length===0||p.value.length!==e.length)return;let r=i[t];r||(r=jl(Su,100),i[t]=r);let a=[...e],o=new Map(h.value);r(t,a,o,p.value,n.storage)}});function b(e,t){let{panelDataArray:n}=v.value,r=P(n,e);return mu({defaultSize:t,dragState:f.value,layout:p.value,panelData:n,panelIndex:r})}function x(e){let{panelDataArray:t}=v.value;t.push(e),t.sort((e,t)=>{let n=e.order,r=t.order;return n==null&&r==null?0:n==null?-1:r==null?1:n-r}),v.value.panelDataArrayChanged=!0}R(()=>v.value.panelDataArrayChanged,()=>{if(v.value.panelDataArrayChanged){v.value.panelDataArrayChanged=!1;let{autoSaveId:e,storage:t}=_.value,{layout:n,panelDataArray:i}=v.value,a=null;if(e){let n=xu(e,i,t);n&&(h.value=new Map(Object.entries(n.expandToSizes)),a=n.layout)}a===null&&(a=kl({panelDataArray:i}));let o=hu({layout:a,panelConstraints:i.map(e=>e.constraints)});en(n,o)||(y(o),v.value.layout=o,r(`layout`,o),Al(i,o,m.value))}});function S(e){return function(t){t.preventDefault();let n=d.value;if(!n)return()=>null;let{direction:i,dragState:a,id:o,keyboardResizeBy:s}=_.value,{layout:c,panelDataArray:u}=v.value,{initialLayout:f}=a??{},p=Ll(o,e,n),h=Dl(t,e,i,a,s,n);if(h===0)return;let b=i===`horizontal`;l.value===`rtl`&&b&&(h=-h);let x=u.map(e=>e.constraints),S=Il({delta:h,layout:f??c,panelConstraints:x,pivotIndices:p,trigger:xl(t)?`keyboard`:`mouse-or-touch`}),C=!Fl(c,S);(Sl(t)||Cl(t))&&g.value!==h&&(g.value=h,C?au(e,0):b?au(e,h<0?1:2):au(e,h<0?4:8)),C&&(y(S),v.value.layout=S,r(`layout`,S),Al(u,S,m.value))}}function C(e,t){let{layout:n,panelDataArray:i}=v.value,a=i.map(e=>e.constraints),{panelSize:o,pivotIndices:s}=re(i,e,n);$(o!=null);let c=P(i,e)===i.length-1?o-t:t-o,l=Il({delta:c,layout:n,panelConstraints:a,pivotIndices:s,trigger:`imperative-api`});Fl(n,l)||(y(l),v.value.layout=l,r(`layout`,l),Al(i,l,m.value))}function w(e,t){let{layout:n,panelDataArray:r}=v.value,i=P(r,e);r[i]=e,v.value.panelDataArrayChanged=!0;let{collapsedSize:a=0,collapsible:o}=t,{collapsedSize:s=0,collapsible:c,maxSize:l=100,minSize:u=0}=e.constraints,{panelSize:d}=re(r,e,n);d!==null&&(o&&c&&d===a?a!==s&&C(e,s):d<u?C(e,u):d>l&&C(e,l))}function E(e,t){let{direction:n}=_.value,{layout:r}=v.value;if(!d.value)return;let i=_l(e,d.value);$(i);let a=Tl(n,t);f.value={dragHandleId:e,dragHandleRect:i.getBoundingClientRect(),initialCursorPosition:a,initialLayout:r}}function D(){f.value=null}function O(e){let{panelDataArray:t}=v.value,n=P(t,e);n>=0&&(t.splice(n,1),delete m.value[e.id],v.value.panelDataArrayChanged=!0)}function A(e){let{layout:t,panelDataArray:n}=v.value;if(e.constraints.collapsible){let i=n.map(e=>e.constraints),{collapsedSize:a=0,panelSize:o,pivotIndices:s}=re(n,e,t);if($(o!=null,`Panel size not found for panel "${e.id}"`),o!==a){h.value.set(e.id,o);let c=P(n,e)===n.length-1?o-a:a-o,l=Il({delta:c,layout:t,panelConstraints:i,pivotIndices:s,trigger:`imperative-api`});Fl(t,l)||(y(l),v.value.layout=l,r(`layout`,l),Al(n,l,m.value))}}}function ee(e){let{layout:t,panelDataArray:n}=v.value;if(e.constraints.collapsible){let i=n.map(e=>e.constraints),{collapsedSize:a=0,panelSize:o,minSize:s=0,pivotIndices:c}=re(n,e,t);if(o===a){let a=h.value.get(e.id),l=a!=null&&a>=s?a:s,u=P(n,e)===n.length-1?o-l:l-o,d=Il({delta:u,layout:t,panelConstraints:i,pivotIndices:c,trigger:`imperative-api`});Fl(t,d)||(y(d),v.value.layout=d,r(`layout`,d),Al(n,d,m.value))}}}function M(e){let{layout:t,panelDataArray:n}=v.value,{panelSize:r}=re(n,e,t);return $(r!=null,`Panel size not found for panel "${e.id}"`),r}function te(e){let{layout:t,panelDataArray:n}=v.value,{collapsedSize:r=0,collapsible:i,panelSize:a}=re(n,e,t);return i?a===void 0?e.constraints.defaultSize===e.constraints.collapsedSize:a===r:!1}function ne(e){let{layout:t,panelDataArray:n}=v.value,{collapsedSize:r=0,collapsible:i,panelSize:a}=re(n,e,t);return $(a!=null,`Panel size not found for panel "${e.id}"`),!i||a>r}Tu({direction:a,dragState:f.value,groupId:c,reevaluatePanelConstraints:w,registerPanel:x,registerResizeHandle:S,resizePanel:C,startDragging:E,stopDragging:D,unregisterPanel:O,panelGroupElement:d,collapsePanel:A,expandPanel:ee,isPanelCollapsed:te,isPanelExpanded:ne,getPanelSize:M,getPanelStyle:b});function P(e,t){return e.findIndex(e=>e===t||e.id===t.id)}function re(e,t,n){let r=P(e,t),i=r===e.length-1?[r-1,r]:[r,r+1],a=n[r];return{...t.constraints,panelSize:a,pivotIndices:i}}return(e,t)=>(k(),s(I(Z),{ref:I(u),as:e.as,"as-child":e.asChild,style:T({display:`flex`,flexDirection:I(a)===`horizontal`?`row`:`column`,height:`100%`,overflow:`hidden`,width:`100%`}),"data-panel-group":``,"data-orientation":I(a),"data-panel-group-id":I(c)},{default:B(()=>[N(e.$slots,`default`,{layout:p.value})]),_:3},8,[`as`,`as-child`,`style`,`data-orientation`,`data-panel-group-id`]))}}),Du=p({__name:`SplitterPanel`,props:{collapsedSize:{type:Number,required:!1},collapsible:{type:Boolean,required:!1},defaultSize:{type:Number,required:!1},id:{type:String,required:!1},maxSize:{type:Number,required:!1},minSize:{type:Number,required:!1},order:{type:Number,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`collapse`,`expand`,`resize`],setup(e,{expose:t,emit:n}){let r=e,i=n,a=wu();if(a===null)throw Error(`SplitterPanel components must be rendered within a SplitterGroup container`);let{collapsePanel:c,expandPanel:l,getPanelSize:u,getPanelStyle:d,isPanelCollapsed:f,resizePanel:p,groupId:m,reevaluatePanelConstraints:h,registerPanel:g,unregisterPanel:_}=a,v=Hn(r.id,`reka-splitter-panel`),y=o(()=>({callbacks:{onCollapse:()=>i(`collapse`),onExpand:()=>i(`expand`),onResize:(...e)=>i(`resize`,...e)},constraints:{collapsedSize:r.collapsedSize&&Number.parseFloat(r.collapsedSize.toFixed(10)),collapsible:r.collapsible,defaultSize:r.defaultSize,maxSize:r.maxSize,minSize:r.minSize},id:v,idIsFromProps:r.id!==void 0,order:r.order}));R(()=>y.value.constraints,(e,t)=>{(t.collapsedSize!==e.collapsedSize||t.collapsible!==e.collapsible||t.maxSize!==e.maxSize||t.minSize!==e.minSize)&&h(y.value,t)},{deep:!0}),D(()=>{let e=y.value;g(e),O(()=>{_(e)})});let b=o(()=>d(y.value,r.defaultSize)),x=o(()=>f(y.value)),S=o(()=>!x.value);function C(){c(y.value)}function w(){l(y.value)}function E(e){p(y.value,e)}return t({collapse:C,expand:w,getSize(){return u(y.value)},resize:E,isCollapsed:x,isExpanded:S}),(e,t)=>(k(),s(I(Z),{id:I(v),style:T(b.value),as:e.as,"as-child":e.asChild,"data-panel":``,"data-panel-collapsible":e.collapsible||void 0,"data-panel-group-id":I(m),"data-panel-id":I(v),"data-panel-size":Number.parseFloat(`${b.value.flexGrow}`).toFixed(1),"data-state":e.collapsible?x.value?`collapsed`:`expanded`:void 0},{default:B(()=>[N(e.$slots,`default`,{isCollapsed:x.value,isExpanded:S.value,expand:w,collapse:C,resize:E})]),_:3},8,[`id`,`style`,`as`,`as-child`,`data-panel-collapsible`,`data-panel-group-id`,`data-panel-id`,`data-panel-size`,`data-state`]))}});function Ou({disabled:e,handleId:t,resizeHandler:n,panelGroupElement:r}){z(i=>{let a=r.value;if(e.value||n.value===null||a===null)return;let o=_l(t,a);if(o==null)return;let s=e=>{if(!e.defaultPrevented)switch(e.key){case`ArrowDown`:case`ArrowLeft`:case`ArrowRight`:case`ArrowUp`:case`End`:case`Home`:e.preventDefault(),n.value?.(e);break;case`F6`:{e.preventDefault();let n=o.getAttribute(`data-panel-group-id`);$(n);let r=yl(n,a),i=vl(n,t,a);$(i!==null);let s=e.shiftKey?i>0?i-1:r.length-1:i+1<r.length?i+1:0;r[s].focus();break}}};o.addEventListener(`keydown`,s),i(()=>{o.removeEventListener(`keydown`,s)})})}var ku=p({__name:`SplitterResizeHandle`,props:{id:{type:String,required:!1},hitAreaMargins:{type:Object,required:!1},tabindex:{type:Number,required:!1,default:0},disabled:{type:Boolean,required:!1},nonce:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:[`dragging`],setup(e,{emit:t}){let n=e,r=t,{forwardRef:i,currentElement:a}=Y(),{disabled:o}=F(n),c=wu();if(c===null)throw Error(`PanelResizeHandle components must be rendered within a PanelGroup container`);let{direction:l,groupId:u,registerResizeHandle:d,startDragging:f,stopDragging:p,panelGroupElement:m}=c,h=Hn(n.id,`reka-splitter-resize-handle`),g=j(`inactive`),_=j(!1),v=j(null),{nonce:y}=F(n),b=Ko(y);return R(o,()=>{tn&&(o.value?v.value=null:v.value=d(h))},{immediate:!0}),z(e=>{if(o.value||v.value===null)return;let t=a.value;t&&($(t),e(eu(h,t,l,{coarse:n.hitAreaMargins?.coarse??15,fine:n.hitAreaMargins?.fine??5},b,(e,t,n)=>{if(t)switch(e){case`down`:g.value=`drag`,f(h,n),r(`dragging`,!0);break;case`move`:g.value!==`drag`&&(g.value=`hover`),v.value?.(n);break;case`up`:g.value=`hover`,p(),r(`dragging`,!1);break}else g.value=`inactive`})))}),Ou({disabled:o,resizeHandler:v,handleId:h,panelGroupElement:m}),(e,t)=>(k(),s(I(Z),{id:I(h),ref:I(i),style:{touchAction:`none`,userSelect:`none`},as:e.as,"as-child":e.asChild,role:`separator`,"data-resize-handle":``,tabindex:e.tabindex,"data-state":g.value,"data-disabled":I(o)?``:void 0,"data-orientation":I(l),"data-panel-group-id":I(u),"data-resize-handle-active":g.value===`drag`?`pointer`:_.value?`keyboard`:void 0,"data-resize-handle-state":g.value,"data-panel-resize-handle-enabled":!I(o),"data-panel-resize-handle-id":I(h),onBlur:t[0]||=e=>_.value=!1,onFocus:t[1]||=e=>_.value=!1},{default:B(()=>[N(e.$slots,`default`)]),_:3},8,[`id`,`as`,`as-child`,`tabindex`,`data-state`,`data-disabled`,`data-orientation`,`data-panel-group-id`,`data-resize-handle-active`,`data-resize-handle-state`,`data-panel-resize-handle-enabled`,`data-panel-resize-handle-id`]))}}),Au=p({__name:`Button`,props:{variant:{},size:{},disabled:{type:Boolean},class:{},asChild:{type:Boolean},as:{default:`button`}},emits:[`click`],setup(e,{emit:t}){let n=e,r=t,i=e=>{n.disabled||r(`click`,e)};return(t,r)=>(k(),s(I(Z),{"data-slot":`button`,as:e.as,"as-child":e.asChild,"data-disabled":e.disabled,class:C(I(q)(I(ju)({variant:e.variant,size:e.size}),n.class)),onClick:V(i,[`stop`,`prevent`])},{default:B(()=>[N(t.$slots,`default`)]),_:3},8,[`as`,`as-child`,`data-disabled`,`class`]))}});const ju=Xt(q(`inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive`,`data-[disabled=true]:cursor-not-allowed data-[disabled=true]:opacity-50`),{variants:{variant:{default:`bg-primary text-primary-foreground shadow-xs [&:not([data-disabled=true])]:hover:bg-primary/90`,destructive:`bg-destructive text-white shadow-xs [&:not([data-disabled=true])]:hover:bg-destructive/90 [&:not([data-disabled=true])]:focus-visible:ring-destructive/20 dark:[&:not([data-disabled=true])]:focus-visible:ring-destructive/40 dark:bg-destructive/60`,outline:`border bg-background shadow-xs [&:not([data-disabled=true])]:hover:bg-accent [&:not([data-disabled=true])]:hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:[&:not([data-disabled=true])]:hover:bg-input/50`,secondary:`bg-secondary text-secondary-foreground shadow-xs [&:not([data-disabled=true])]:hover:bg-secondary/80`,ghost:`hover:bg-accent hover:text-accent-foreground dark:[&:not([data-disabled=true])]:hover:bg-accent/50`,link:`text-primary underline-offset-4 [&:not([data-disabled=true])]:hover:underline`},size:{default:`h-9 px-4 py-2 has-[>svg]:px-3`,sm:`h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5`,lg:`h-10 rounded-md px-6 has-[>svg]:px-4`,icon:`size-9`}},defaultVariants:{variant:`default`,size:`default`}}),Mu=e=>e.replace(/([a-z0-9])([A-Z])/g,`$1-$2`).toLowerCase(),Nu=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),Pu=e=>{let t=Nu(e);return t.charAt(0).toUpperCase()+t.slice(1)},Fu=(...e)=>e.filter((e,t,n)=>!!e&&e.trim()!==``&&n.indexOf(e)===t).join(` `).trim(),Iu=e=>e===``;
/**
* @license lucide-vue-next v0.544.0 - ISC
*
* This source code is licensed under the ISC license.
* See the LICENSE file in the root directory of this source tree.
*/
var Lu={xmlns:`http://www.w3.org/2000/svg`,width:24,height:24,viewBox:`0 0 24 24`,fill:`none`,stroke:`currentColor`,"stroke-width":2,"stroke-linecap":`round`,"stroke-linejoin":`round`};const Ru=({name:e,iconNode:t,absoluteStrokeWidth:n,"absolute-stroke-width":r,strokeWidth:i,"stroke-width":a,size:o=Lu.width,color:s=Lu.stroke,...c},{slots:l})=>g(`svg`,{...Lu,...c,width:o,height:o,stroke:s,"stroke-width":Iu(n)||Iu(r)||n===!0||r===!0?Number(i||a||Lu[`stroke-width`])*24/Number(o):i||a||Lu[`stroke-width`],class:Fu(`lucide`,c.class,...e?[`lucide-${Mu(Pu(e))}-icon`,`lucide-${Mu(e)}`]:[`lucide-icon`])},[...t.map(e=>g(...e)),...l.default?[l.default()]:[]]),zu=(e,t)=>(n,{slots:r,attrs:i})=>g(Ru,{...i,...n,iconNode:t,name:e},r),Bu=zu(`check`,[[`path`,{d:`M20 6 9 17l-5-5`,key:`1gmf2c`}]]),Vu=zu(`chevron-down`,[[`path`,{d:`m6 9 6 6 6-6`,key:`qrunsl`}]]),Hu=zu(`chevron-left`,[[`path`,{d:`m15 18-6-6 6-6`,key:`1wnfg3`}]]),Uu=zu(`chevron-right`,[[`path`,{d:`m9 18 6-6-6-6`,key:`mthhwq`}]]),Wu=zu(`chevron-up`,[[`path`,{d:`m18 15-6-6-6 6`,key:`153udz`}]]),Gu=zu(`circle-x`,[[`circle`,{cx:`12`,cy:`12`,r:`10`,key:`1mglay`}],[`path`,{d:`m15 9-6 6`,key:`1uzhvr`}],[`path`,{d:`m9 9 6 6`,key:`z0biqf`}]]),Ku=zu(`circle`,[[`circle`,{cx:`12`,cy:`12`,r:`10`,key:`1mglay`}]]),qu=zu(`grip-vertical`,[[`circle`,{cx:`9`,cy:`12`,r:`1`,key:`1vctgf`}],[`circle`,{cx:`9`,cy:`5`,r:`1`,key:`hp0tcf`}],[`circle`,{cx:`9`,cy:`19`,r:`1`,key:`fkjjf6`}],[`circle`,{cx:`15`,cy:`12`,r:`1`,key:`1tmaij`}],[`circle`,{cx:`15`,cy:`5`,r:`1`,key:`19l28e`}],[`circle`,{cx:`15`,cy:`19`,r:`1`,key:`f4zoj3`}]]),Ju=zu(`minus`,[[`path`,{d:`M5 12h14`,key:`1ays0h`}]]),Yu=zu(`plus`,[[`path`,{d:`M5 12h14`,key:`1ays0h`}],[`path`,{d:`M12 5v14`,key:`s699le`}]]),Xu=zu(`search`,[[`path`,{d:`m21 21-4.34-4.34`,key:`14j7rj`}],[`circle`,{cx:`11`,cy:`11`,r:`8`,key:`4ej97u`}]]),Zu=zu(`x`,[[`path`,{d:`M18 6 6 18`,key:`1bl5f8`}],[`path`,{d:`m6 6 12 12`,key:`d8bk6v`}]]);var Qu=p({__name:`Checkbox`,props:{defaultValue:{type:[Boolean,String]},modelValue:{type:[Boolean,String,null]},disabled:{type:Boolean},value:{},id:{},asChild:{type:Boolean},as:{},name:{},required:{type:Boolean},class:{}},emits:[`update:modelValue`],setup(e,{emit:t}){let n=e,r=t,i=H(n,`class`),a=X(i,r);return(e,t)=>(k(),s(I(gi),x({"data-slot":`checkbox`},I(a),{class:I(q)(`peer border-input data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50`,n.class)}),{default:B(()=>[f(I(_i),{"data-slot":`checkbox-indicator`,class:`flex items-center justify-center text-current transition-none`},{default:B(()=>[N(e.$slots,`default`,{},()=>[f(I(Bu),{class:`size-3.5`})])]),_:3})]),_:3},16,[`class`]))}}),$u=p({__name:`ContextMenu`,props:{pressOpenDelay:{},dir:{},modal:{type:Boolean}},emits:[`update:open`],setup(e,{emit:t}){let n=X(e,t);return(e,t)=>(k(),s(I(js),x({"data-slot":`context-menu`},I(n)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}});const ed={class:`pointer-events-none absolute left-2 flex size-3.5 items-center justify-center`};var td=p({__name:`ContextMenuCheckboxItem`,props:{modelValue:{type:[Boolean,String]},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{}},emits:[`select`,`update:modelValue`],setup(e,{emit:t}){let n=e,r=t,i=H(n,`class`),a=X(i,r);return(e,t)=>(k(),s(I(Os),x({"data-slot":`context-menu-checkbox-item`},I(a),{class:I(q)(`focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4`,n.class)}),{default:B(()=>[u(`span`,ed,[f(I(Fs),null,{default:B(()=>[f(I(Bu),{class:`size-4`})]),_:1})]),N(e.$slots,`default`)]),_:3},16,[`class`]))}}),nd=p({__name:`ContextMenuContent`,props:{forceMount:{type:Boolean},loop:{type:Boolean},sideFlip:{type:Boolean},alignOffset:{},alignFlip:{type:Boolean},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},class:{}},emits:[`escapeKeyDown`,`pointerDownOutside`,`focusOutside`,`interactOutside`,`closeAutoFocus`],setup(e,{emit:t}){let n=e,r=t,i=H(n,`class`),a=X(i,r);return(e,t)=>(k(),s(I(Ls),null,{default:B(()=>[f(I(Ms),x({"data-slot":`context-menu-content`},I(a),{class:I(q)(`bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--reka-context-menu-content-available-height) min-w-[8rem] overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md`,n.class)}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`class`])]),_:3}))}}),rd=p({__name:`ContextMenuGroup`,props:{asChild:{type:Boolean},as:{}},setup(e){let t=e;return(e,n)=>(k(),s(I(Ns),x({"data-slot":`context-menu-group`},t),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),id=p({__name:`ContextMenuItem`,props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean},variant:{default:`default`}},emits:[`select`],setup(e,{emit:t}){let n=e,r=t,i=H(n,`class`),a=X(i,r);return(t,r)=>(k(),s(I(Ps),x({"data-slot":`context-menu-item`,"data-inset":e.inset?``:void 0,"data-variant":e.variant},I(a),{class:I(q)(`focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4`,n.class)}),{default:B(()=>[N(t.$slots,`default`)]),_:3},16,[`data-inset`,`data-variant`,`class`]))}}),ad=p({__name:`ContextMenuLabel`,props:{asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},setup(e){let t=e,n=H(t,`class`);return(r,i)=>(k(),s(I(Is),x({"data-slot":`context-menu-label`,"data-inset":e.inset?``:void 0},I(n),{class:I(q)(`text-foreground px-2 py-1.5 text-sm font-medium data-[inset]:pl-8`,t.class)}),{default:B(()=>[N(r.$slots,`default`)]),_:3},16,[`data-inset`,`class`]))}}),od=p({__name:`ContextMenuRadioGroup`,props:{modelValue:{},asChild:{type:Boolean},as:{}},emits:[`update:modelValue`],setup(e,{emit:t}){let n=X(e,t);return(e,t)=>(k(),s(I(Rs),x({"data-slot":`context-menu-radio-group`},I(n)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}});const sd={class:`pointer-events-none absolute left-2 flex size-3.5 items-center justify-center`};var cd=p({__name:`ContextMenuRadioItem`,props:{value:{},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{}},emits:[`select`],setup(e,{emit:t}){let n=e,r=t,i=H(n,`class`),a=X(i,r);return(e,t)=>(k(),s(I(zs),x({"data-slot":`context-menu-radio-item`},I(a),{class:I(q)(`focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4`,n.class)}),{default:B(()=>[u(`span`,sd,[f(I(Fs),null,{default:B(()=>[f(I(Ku),{class:`size-2 fill-current`})]),_:1})]),N(e.$slots,`default`)]),_:3},16,[`class`]))}}),ld=p({__name:`ContextMenuSeparator`,props:{asChild:{type:Boolean},as:{},class:{}},setup(e){let t=e,n=H(t,`class`);return(e,r)=>(k(),s(I(Bs),x({"data-slot":`context-menu-separator`},I(n),{class:I(q)(`bg-border -mx-1 my-1 h-px`,t.class)}),null,16,[`class`]))}}),ud=p({__name:`ContextMenuShortcut`,props:{class:{}},setup(e){let t=e;return(e,n)=>(k(),l(`span`,{"data-slot":`context-menu-shortcut`,class:C(I(q)(`text-muted-foreground ml-auto text-xs tracking-widest`,t.class))},[N(e.$slots,`default`)],2))}}),dd=p({__name:`ContextMenuSub`,props:{defaultOpen:{type:Boolean},open:{type:Boolean}},emits:[`update:open`],setup(e,{emit:t}){let n=X(e,t);return(e,t)=>(k(),s(I(Vs),x({"data-slot":`context-menu-sub`},I(n)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),fd=p({__name:`ContextMenuSubContent`,props:{forceMount:{type:Boolean},loop:{type:Boolean},sideOffset:{},sideFlip:{type:Boolean},alignOffset:{},alignFlip:{type:Boolean},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},class:{}},emits:[`escapeKeyDown`,`pointerDownOutside`,`focusOutside`,`interactOutside`,`entryFocus`,`openAutoFocus`,`closeAutoFocus`],setup(e,{emit:t}){let n=e,r=t,i=H(n,`class`),a=X(i,r);return(e,t)=>(k(),s(I(Hs),x({"data-slot":`context-menu-sub-content`},I(a),{class:I(q)(`bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--reka-context-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg`,n.class)}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`class`]))}}),pd=p({__name:`ContextMenuSubTrigger`,props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},setup(e){let t=e,n=H(t,`class`),r=An(n);return(n,i)=>(k(),s(I(Us),x({"data-slot":`context-menu-sub-trigger`,"data-inset":e.inset?``:void 0},I(r),{class:I(q)(`focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4`,t.class)}),{default:B(()=>[N(n.$slots,`default`),f(I(Uu),{class:`ml-auto`})]),_:3},16,[`data-inset`,`class`]))}}),md=p({__name:`ContextMenuTrigger`,props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e){let t=An(e);return(e,n)=>(k(),s(I(Gs),x({"data-slot":`context-menu-trigger`},I(t)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),hd=p({__name:`Dialog`,props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:[`update:open`],setup(e,{emit:t}){let n=X(e,t);return(e,t)=>(k(),s(I(rr),x({"data-slot":`dialog`},I(n)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),gd=p({__name:`DialogClose`,props:{asChild:{type:Boolean},as:{}},setup(e){let t=e;return(e,n)=>(k(),s(I(ir),x({"data-slot":`dialog-close`},t),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),_d=p({__name:`DialogOverlay`,props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(e){let t=e,n=H(t,`class`);return(e,r)=>(k(),s(I(Wr),x({"data-slot":`dialog-overlay`},I(n),{class:I(q)(`data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80`,t.class)}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`class`]))}}),vd=p({__name:`DialogContent`,props:{forceMount:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:[`escapeKeyDown`,`pointerDownOutside`,`focusOutside`,`interactOutside`,`openAutoFocus`,`closeAutoFocus`],setup(e,{emit:t}){let n=e,r=t,i=H(n,`class`),a=X(i,r);return(e,t)=>(k(),s(I(Kr),null,{default:B(()=>[f(_d),f(I(Vr),x({"data-slot":`dialog-content`},I(a),{class:I(q)(`bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg`,n.class)}),{default:B(()=>[N(e.$slots,`default`),f(I(ir),{class:`ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4`},{default:B(()=>[f(I(Zu)),t[0]||=u(`span`,{class:`sr-only`},`Close`,-1)]),_:1})]),_:3},16,[`class`])]),_:3}))}}),yd=p({__name:`DialogDescription`,props:{asChild:{type:Boolean},as:{},class:{}},setup(e){let t=e,n=H(t,`class`),r=An(n);return(e,n)=>(k(),s(I(Hr),x({"data-slot":`dialog-description`},I(r),{class:I(q)(`text-muted-foreground text-sm`,t.class)}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`class`]))}}),bd=p({__name:`DialogFooter`,props:{class:{}},setup(e){let t=e;return(e,n)=>(k(),l(`div`,{"data-slot":`dialog-footer`,class:C(I(q)(`flex flex-col-reverse gap-2 sm:flex-row sm:justify-end`,t.class))},[N(e.$slots,`default`)],2))}}),xd=p({__name:`DialogHeader`,props:{class:{}},setup(e){let t=e;return(e,n)=>(k(),l(`div`,{"data-slot":`dialog-header`,class:C(I(q)(`flex flex-col gap-2 text-center sm:text-left`,t.class))},[N(e.$slots,`default`)],2))}}),Sd=p({__name:`DialogScrollContent`,props:{forceMount:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:[`escapeKeyDown`,`pointerDownOutside`,`focusOutside`,`interactOutside`,`openAutoFocus`,`closeAutoFocus`],setup(e,{emit:t}){let n=e,r=t,i=H(n,`class`),a=X(i,r);return(e,t)=>(k(),s(I(Kr),null,{default:B(()=>[f(I(Wr),{class:`fixed inset-0 z-50 grid place-items-center overflow-y-auto bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0`},{default:B(()=>[f(I(Vr),x({class:I(q)(`relative z-50 grid w-full max-w-lg my-8 gap-4 border border-border bg-background p-6 shadow-lg duration-200 sm:rounded-lg md:w-full`,n.class)},I(a),{onPointerDownOutside:t[0]||=e=>{let t=e.detail.originalEvent,n=t.target;(t.offsetX>n.clientWidth||t.offsetY>n.clientHeight)&&e.preventDefault()}}),{default:B(()=>[N(e.$slots,`default`),f(I(ir),{class:`absolute top-4 right-4 p-0.5 transition-colors rounded-md hover:bg-secondary`},{default:B(()=>[f(I(Zu),{class:`w-4 h-4`}),t[1]||=u(`span`,{class:`sr-only`},`Close`,-1)]),_:1})]),_:3},16,[`class`])]),_:3})]),_:3}))}}),Cd=p({__name:`DialogTitle`,props:{asChild:{type:Boolean},as:{},class:{}},setup(e){let t=e,n=H(t,`class`),r=An(n);return(e,n)=>(k(),s(I(qr),x({"data-slot":`dialog-title`},I(r),{class:I(q)(`text-lg leading-none font-semibold`,t.class)}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`class`]))}}),wd=p({__name:`DialogTrigger`,props:{asChild:{type:Boolean},as:{}},setup(e){let t=e;return(e,n)=>(k(),s(I(Jr),x({"data-slot":`dialog-trigger`},t),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),Td=p({__name:`Input`,props:{defaultValue:{},modelValue:{},class:{}},emits:[`update:modelValue`],setup(e,{emit:t}){let n=e,r=xe(n,`modelValue`,t,{passive:!0,defaultValue:n.defaultValue});return(e,t)=>ce((k(),l(`input`,{"onUpdate:modelValue":t[0]||=e=>v(r)?r.value=e:null,"data-slot":`input`,class:C(I(q)(`file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm`,`focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]`,`aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive`,n.class))},null,2)),[[oe,I(r)]])}}),Ed=p({__name:`Label`,props:{for:{},asChild:{type:Boolean},as:{},class:{}},setup(e){let t=e,n=H(t,`class`);return(e,r)=>(k(),s(I(Ks),x({"data-slot":`label`},I(n),{class:I(q)(`flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50`,t.class)}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`class`]))}}),Dd=p({__name:`NumberField`,props:{defaultValue:{},modelValue:{},min:{},max:{},step:{},stepSnapping:{type:Boolean},formatOptions:{},locale:{},disabled:{type:Boolean},readonly:{type:Boolean},disableWheelChange:{type:Boolean},invertWheelChange:{type:Boolean},id:{},asChild:{type:Boolean},as:{},name:{},required:{type:Boolean},class:{}},emits:[`update:modelValue`],setup(e,{emit:t}){let n=e,r=t,i=H(n,`class`),a=X(i,r);return(e,t)=>(k(),s(I(vc),x(I(a),{class:I(q)(`grid gap-1.5`,n.class)}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`class`]))}}),Od=p({__name:`NumberFieldContent`,props:{class:{}},setup(e){let t=e;return(e,n)=>(k(),l(`div`,{class:C(I(q)(`relative [&>[data-slot=input]]:has-[[data-slot=increment]]:pr-5 [&>[data-slot=input]]:has-[[data-slot=decrement]]:pl-5`,t.class))},[N(e.$slots,`default`)],2))}}),kd=p({__name:`NumberFieldDecrement`,props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(e){let t=e,n=H(t,`class`),r=An(n);return(e,n)=>(k(),s(I(yc),x({"data-slot":`decrement`},I(r),{class:I(q)(`absolute top-1/2 -translate-y-1/2 left-0 p-3 disabled:cursor-not-allowed disabled:opacity-20`,t.class)}),{default:B(()=>[N(e.$slots,`default`,{},()=>[f(I(Ju),{class:`h-4 w-4`})])]),_:3},16,[`class`]))}}),Ad=p({__name:`NumberFieldIncrement`,props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(e){let t=e,n=H(t,`class`),r=An(n);return(e,n)=>(k(),s(I(bc),x({"data-slot":`increment`},I(r),{class:I(q)(`absolute top-1/2 -translate-y-1/2 right-0 disabled:cursor-not-allowed disabled:opacity-20 p-3`,t.class)}),{default:B(()=>[N(e.$slots,`default`,{},()=>[f(I(Yu),{class:`h-4 w-4`})])]),_:3},16,[`class`]))}}),jd=p({__name:`NumberFieldInput`,props:{class:{}},setup(e){let t=e;return(e,n)=>(k(),s(I(xc),{"data-slot":`input`,class:C(I(q)(`flex h-9 w-full rounded-md border border-input bg-transparent py-1 text-sm text-center shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50`,t.class))},null,8,[`class`]))}}),Md=p({__name:`RadioGroup`,props:{modelValue:{},defaultValue:{},disabled:{type:Boolean},orientation:{},dir:{},loop:{type:Boolean},asChild:{type:Boolean},as:{},name:{},required:{type:Boolean},class:{}},emits:[`update:modelValue`],setup(e,{emit:t}){let n=e,r=t,i=H(n,`class`),a=X(i,r);return(e,t)=>(k(),s(I(Ec),x({"data-slot":`radio-group`,class:I(q)(`grid gap-3`,n.class)},I(a)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`class`]))}}),Nd=p({__name:`RadioGroupItem`,props:{id:{},value:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{},name:{},required:{type:Boolean},class:{}},setup(e){let t=e,n=H(t,`class`),r=An(n);return(e,n)=>(k(),s(I(kc),x({"data-slot":`radio-group-item`},I(r),{class:I(q)(`border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50`,t.class)}),{default:B(()=>[f(I(Ac),{"data-slot":`radio-group-indicator`,class:`relative flex items-center justify-center`},{default:B(()=>[f(I(Ku),{class:`fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2`})]),_:1})]),_:1},16,[`class`]))}});const Pd={key:0,class:`bg-border z-10 flex h-4 w-3 items-center justify-center rounded-xs border`};var Fd=p({__name:`ResizableHandle`,props:{id:{},hitAreaMargins:{},tabindex:{},disabled:{type:Boolean},nonce:{},asChild:{type:Boolean},as:{},class:{},withHandle:{type:Boolean}},emits:[`dragging`],setup(e,{emit:t}){let n=e,r=t,i=H(n,`class`,`withHandle`),a=X(i,r);return(e,t)=>(k(),s(I(ku),x({"data-slot":`resizable-handle`},I(a),{class:I(q)(`bg-border focus-visible:ring-ring relative flex w-px items-center justify-center after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:ring-1 focus-visible:ring-offset-1 focus-visible:outline-hidden data-[orientation=vertical]:h-px data-[orientation=vertical]:w-full data-[orientation=vertical]:after:left-0 data-[orientation=vertical]:after:h-1 data-[orientation=vertical]:after:w-full data-[orientation=vertical]:after:-translate-y-1/2 data-[orientation=vertical]:after:translate-x-0 [&[data-orientation=vertical]>div]:rotate-90`,n.class)}),{default:B(()=>[n.withHandle?(k(),l(`div`,Pd,[f(I(qu),{class:`size-2.5`})])):c(``,!0)]),_:1},16,[`class`]))}}),Id=p({__name:`ResizablePanel`,props:{collapsedSize:{},collapsible:{type:Boolean},defaultSize:{},id:{},maxSize:{},minSize:{},order:{},asChild:{type:Boolean},as:{}},emits:[`collapse`,`expand`,`resize`],setup(e,{emit:t}){let n=X(e,t);return(e,t)=>(k(),s(I(Du),x({"data-slot":`resizable-panel`},I(n)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),Ld=p({__name:`ResizablePanelGroup`,props:{id:{},autoSaveId:{},direction:{},keyboardResizeBy:{},storage:{},asChild:{type:Boolean},as:{},class:{}},emits:[`layout`],setup(e,{emit:t}){let n=e,r=t,i=H(n,`class`),a=X(i,r);return(e,t)=>(k(),s(I(Eu),x({"data-slot":`resizable-panel-group`},I(a),{class:I(q)(`flex h-full w-full data-[orientation=vertical]:flex-col`,n.class)}),{default:B(()=>[N(e.$slots,`default`)]),_:3},16,[`class`]))}}),Rd=p({__name:`Select`,props:{open:{type:Boolean},defaultOpen:{type:Boolean},defaultValue:{},modelValue:{},by:{type:[String,Function]},dir:{},multiple:{type:Boolean},autocomplete:{},disabled:{type:Boolean},name:{},required:{type:Boolean}},emits:[`update:modelValue`,`update:open`],setup(e,{emit:t}){let n=X(e,t);return(e,t)=>(k(),s(I(Bc),x({"data-slot":`select`},I(n)),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),zd=p({inheritAttrs:!1,__name:`SelectContent`,props:{forceMount:{type:Boolean},position:{default:`popper`},bodyLock:{type:Boolean},side:{},sideOffset:{},sideFlip:{type:Boolean},align:{},alignOffset:{},alignFlip:{type:Boolean},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},class:{}},emits:[`closeAutoFocus`,`escapeKeyDown`,`pointerDownOutside`],setup(e,{emit:t}){let n=e,r=t,i=H(n,`class`),a=X(i,r);return(t,r)=>(k(),s(I(cl),null,{default:B(()=>[f(I(Zc),x({"data-slot":`select-content`},{...I(a),...t.$attrs},{class:I(q)(`bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--reka-select-content-available-height) min-w-[8rem] overflow-x-hidden overflow-y-auto rounded-md border shadow-md`,e.position===`popper`&&`data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1`,n.class)}),{default:B(()=>[f(I(Kd)),f(I(hl),{class:C(I(q)(`p-1`,e.position===`popper`&&`h-[var(--reka-select-trigger-height)] w-full min-w-[var(--reka-select-trigger-width)] scroll-my-1`))},{default:B(()=>[N(t.$slots,`default`)]),_:3},8,[`class`]),f(I(Gd))]),_:3},16,[`class`])]),_:3}))}}),Bd=p({__name:`SelectGroup`,props:{asChild:{type:Boolean},as:{}},setup(e){let t=e;return(e,n)=>(k(),s(I(el),x({"data-slot":`select-group`},t),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}});const Vd={class:`absolute right-2 flex size-3.5 items-center justify-center`};var Hd=p({__name:`SelectItem`,props:{value:{},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{}},setup(e){let t=e,n=H(t,`class`),r=An(n);return(e,n)=>(k(),s(I(il),x({"data-slot":`select-item`},I(r),{class:I(q)(`focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2`,t.class)}),{default:B(()=>[u(`span`,Vd,[f(I(al),null,{default:B(()=>[f(I(Bu),{class:`size-4`})]),_:1})]),f(I(ol),null,{default:B(()=>[N(e.$slots,`default`)]),_:3})]),_:3},16,[`class`]))}}),Ud=p({__name:`SelectItemText`,props:{asChild:{type:Boolean},as:{}},setup(e){let t=e;return(e,n)=>(k(),s(I(ol),x({"data-slot":`select-item-text`},t),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),Wd=p({__name:`SelectLabel`,props:{for:{},asChild:{type:Boolean},as:{},class:{}},setup(e){let t=e;return(e,n)=>(k(),s(I(sl),{"data-slot":`select-label`,class:C(I(q)(`px-2 py-1.5 text-sm font-medium`,t.class))},{default:B(()=>[N(e.$slots,`default`)]),_:3},8,[`class`]))}}),Gd=p({__name:`SelectScrollDownButton`,props:{asChild:{type:Boolean},as:{},class:{}},setup(e){let t=e,n=H(t,`class`),r=An(n);return(e,n)=>(k(),s(I(ul),x({"data-slot":`select-scroll-down-button`},I(r),{class:I(q)(`flex cursor-default items-center justify-center py-1`,t.class)}),{default:B(()=>[N(e.$slots,`default`,{},()=>[f(I(Vu),{class:`size-4`})])]),_:3},16,[`class`]))}}),Kd=p({__name:`SelectScrollUpButton`,props:{asChild:{type:Boolean},as:{},class:{}},setup(e){let t=e,n=H(t,`class`),r=An(n);return(e,n)=>(k(),s(I(dl),x({"data-slot":`select-scroll-up-button`},I(r),{class:I(q)(`flex cursor-default items-center justify-center py-1`,t.class)}),{default:B(()=>[N(e.$slots,`default`,{},()=>[f(I(Wu),{class:`size-4`})])]),_:3},16,[`class`]))}}),qd=p({__name:`SelectSeparator`,props:{asChild:{type:Boolean},as:{},class:{}},setup(e){let t=e,n=H(t,`class`);return(e,r)=>(k(),s(I(fl),x({"data-slot":`select-separator`},I(n),{class:I(q)(`bg-border pointer-events-none -mx-1 my-1 h-px`,t.class)}),null,16,[`class`]))}}),Jd=p({__name:`SelectTrigger`,props:{disabled:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},class:{},size:{default:`default`}},setup(e){let t=e,n=H(t,`class`,`size`),r=An(n);return(n,i)=>(k(),s(I(pl),x({"data-slot":`select-trigger`,"data-size":e.size},I(r),{class:I(q)(`border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4`,t.class)}),{default:B(()=>[N(n.$slots,`default`),f(I(tl),{"as-child":``},{default:B(()=>[f(I(Vu),{class:`size-4 opacity-50`})]),_:1})]),_:3},16,[`data-size`,`class`]))}}),Yd=p({__name:`SelectValue`,props:{placeholder:{},asChild:{type:Boolean},as:{}},setup(e){let t=e;return(e,n)=>(k(),s(I(ml),x({"data-slot":`select-value`},t),{default:B(()=>[N(e.$slots,`default`)]),_:3},16))}}),Xd=p({__name:`Textarea`,props:{class:{},defaultValue:{},modelValue:{}},emits:[`update:modelValue`],setup(e,{emit:t}){let n=e,r=xe(n,`modelValue`,t,{passive:!0,defaultValue:n.defaultValue});return(e,t)=>ce((k(),l(`textarea`,{"onUpdate:modelValue":t[0]||=e=>v(r)?r.value=e:null,"data-slot":`textarea`,class:C(I(q)(`border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm`,n.class))},null,2)),[[oe,I(r)]])}}),Zd=p({__name:`ContentWrapper`,props:{class:{}},setup(e){let t=e,n=L(),r=o(()=>{let{class:e,...t}=n;return t});return(e,n)=>(k(),l(`div`,x({class:I(q)(`w-full grow`,t.class)},r.value),[N(e.$slots,`default`)],16))}}),Qd=p({__name:`ContextMenuWrapper`,props:{id:{}},setup(e){let t=e,r=Me(),i=o(()=>(r.state[t.id],Ae(Oe).get(t.id))),a=je();return(e,t)=>(k(),s(I($u),null,{default:B(()=>[f(I(md),null,{default:B(()=>[N(e.$slots,`default`)]),_:3}),f(I(nd),{class:`w-52`},{default:B(()=>[(k(!0),l(n,null,M(i.value,(e,t)=>(k(),l(n,{key:t},[(k(!0),l(n,null,M(e,e=>(k(),l(n,{key:e.name},[e.children?(k(),s(I(dd),{key:0},{default:B(()=>[f(I(pd),null,{default:B(()=>[d(P(I(a)(e.name)),1)]),_:2},1024),f(I(fd),{class:`w-44`},{default:B(()=>[(k(!0),l(n,null,M(e.children,e=>(k(),s(I(id),{key:e.name},{default:B(()=>[d(P(I(a)(e.name))+` `,1),e.shortcut?(k(),s(I(ud),{key:0},{default:B(()=>[d(P(e.shortcut.show()),1)]),_:2},1024)):c(``,!0)]),_:2},1024))),128))]),_:2},1024)]),_:2},1024)):(k(),s(I(id),{key:1,inset:``},{default:B(()=>[d(P(I(a)(e.name))+` `,1),e.shortcut?(k(),s(I(ud),{key:0},{default:B(()=>[d(P(e.shortcut.display()),1)]),_:2},1024)):c(``,!0)]),_:2},1024))],64))),128)),t===i.value.length-1?c(``,!0):(k(),s(I(ld),{key:0}))],64))),128))]),_:1})]),_:3}))}}),$d=p({__name:`DraggableArea`,props:{class:{type:String,default:``}},setup(e){let t=e,n=async e=>{e.target.hasAttribute(`data-drag`)&&e.buttons===1&&Ne().startDragging()},r=async()=>{await Ne().maximize()};return(e,i)=>(k(),l(`div`,{"data-drag":`true`,class:C(I(q)(t.class)),onMousedown:n,onDblclick:r},[N(e.$slots,`default`)],34))}}),ef=p({__name:`IconButton`,props:{icon:{}},emits:[`click`],setup(e){return(t,n)=>(k(),s(te(e.icon),{class:`size-4 cursor-pointer text-icon hover:scale-105 active:scale-100 transition-colors`,onClick:n[0]||=e=>t.$emit(`click`)}))}});const tf={class:`w-full relative`};var nf=p({__name:`Search`,props:{modelValue:{default:``},showSearch:{type:Boolean,default:!0},allowClear:{type:Boolean,default:!0},className:{default:``}},emits:[`update:modelValue`,`clear`,`change`],setup(e,{expose:t,emit:n}){let r=n,i=j(),a=e=>{let t=e.target.value;r(`update:modelValue`,t),r(`change`,t)},o=e=>{e.preventDefault(),e.stopPropagation(),r(`update:modelValue`,``),r(`change`,``),r(`clear`),S(()=>{i.value?.focus()})};return t({focus:()=>i.value?.focus(),blur:()=>i.value?.blur(),inputRef:i}),(t,n)=>(k(),l(`div`,tf,[e.showSearch?(k(),s(I(Xu),{key:0,class:`absolute left-2 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground`})):c(``,!0),f(I(Td),x({ref_key:`inputRef`,ref:i,class:I(q)(e.showSearch?`pl-7`:``,e.allowClear&&e.modelValue?`pr-10`:``,e.className),"model-value":e.modelValue,onInput:a},t.$attrs),null,16,[`class`,`model-value`]),e.allowClear&&e.modelValue?(k(),s(I(Gu),{key:1,class:`absolute right-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground hover:text-primary cursor-pointer transition-colors`,onClick:o})):c(``,!0)]))}}),rf=p({__name:`Select`,props:{modelValue:{default:``},placeholder:{default:``},options:{}},emits:[`update:modelValue`],setup(e,{emit:t}){let r=t,i=j(!1),a=je(),o=e=>{i.value=e},c=e=>{r(`update:modelValue`,e)};return(t,r)=>(k(),s(I(Rd),{"model-value":e.modelValue,"onUpdate:modelValue":c,open:i.value,"onUpdate:open":o},{default:B(()=>[f(I(Jd),{class:C(I(q)(i.value&&`border-primary`))},{default:B(()=>[f(I(Yd),{placeholder:e.placeholder},null,8,[`placeholder`])]),_:1},8,[`class`]),f(I(zd),null,{default:B(()=>[(k(!0),l(n,null,M(e.options,e=>(k(),s(I(Hd),{key:e.value,value:e.value},{default:B(()=>[d(P(I(a)(e.name)),1)]),_:2},1032,[`value`]))),128))]),_:1})]),_:1},8,[`model-value`,`open`]))}});const af=[`tabindex`];var sf=p({__name:`ShortcutInput`,props:{disabled:{type:Boolean,default:!1},modelValue:{},class:{}},emits:[`update:modelValue`,`blur`],setup(e,{expose:t,emit:n}){let r=L(),i=je(),a=o(()=>{let{class:e,...t}=r;return t}),s=e,c=n,d=j(null),f=j(null),p=j(s.modelValue?ke.from(s.modelValue).show():``);R(()=>s.modelValue,e=>{p.value=e?ke.from(e).show():``});let m=e=>{s.disabled||(f.value=new ke(e.altKey,e.ctrlKey,e.shiftKey,e.metaKey,e.key,e.code),p.value=f.value.show(),f.value.isValid&&c(`update:modelValue`,f.value.toType()),e.preventDefault())},h=e=>{f.value?.isValid||(p.value=s.modelValue?ke.from(s.modelValue).show():``),c(`blur`,e)};return t({focus:()=>{d.value?.focus()}}),(t,n)=>(k(),l(`div`,x(a.value,{"data-slot":`shortcut-input`,tabindex:e.disabled?void 0:-1,class:[`file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50`,!e.disabled&&`focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]`,!e.disabled&&`focus:border-ring focus:ring-ring/50 focus:ring-[3px]`,`items-center`,e.disabled&&`opacity-50 cursor-not-allowed`,`aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive`,s.class],onKeydown:m,onBlur:h,ref_key:`elementRef`,ref:d}),[u(`div`,{class:C([`whitespace-nowrap text-sm px-2 select-none`,!p.value&&`text-muted-foreground`])},P(p.value||I(i)(`enter_composed_key`)),3)],16,af))}}),cf=p({__name:`TabList`,props:{modelValue:{},direction:{},class:{}},emits:[`update:modelValue`],setup(e,{emit:t}){let n=e,r=t,i=j(null),a=j({width:`0px`,transform:`translateX(0px)`}),s=j({width:`0px`,height:`0px`,transform:`translateY(0px)`}),c=o(()=>n.direction===`vertical`?s.value:a.value),d=j(!0),f=ne(null),p=_e(i),m=o(()=>n.modelValue);A(`tabs`,{activeTab:m,setActiveTab:e=>{r(`update:modelValue`,e)},direction:o(()=>n.direction)}),R([p,m],([e,t])=>{e?(h(),d.value&&!f.value&&(f.value=setTimeout(()=>{d.value=!1},150))):(d.value=!0,clearTimeout(f.value),f.value=null)},{flush:`post`});function h(){let e=i.value?.querySelector(`[data-tab-item].active`);if(e){let t=e.clientWidth,r=e.clientHeight,i=e.offsetLeft,o=e.offsetTop;n.direction===`vertical`?s.value={width:`${t}px`,height:`${r}px`,transform:`translateY(${o}px)`}:a.value={width:`${t}px`,transform:`translateX(${i}px)`}}}return(t,r)=>(k(),l(`ul`,{ref_key:`elRef`,ref:i,"data-tabs":``,class:C(I(q)(`relative flex list-none`,e.direction===`vertical`&&`flex-col`,n.class))},[u(`div`,{style:T(c.value),class:C(I(q)(`absolute left-0 top-0 bg-primary rounded-md z-0`,e.direction===`vertical`?``:`h-full`,!d.value&&`transition-all`))},null,6),N(t.$slots,`default`)],2))}});const lf={class:`whitespace-nowrap`};var uf=p({__name:`Tab`,props:{name:{},title:{},scope:{},icon:{type:[Function,Object]},class:{}},setup(e){let t=e,n=_(`tabs`),r=o(()=>n?.activeTab.value===t.name),i=je(t.scope);return(a,o)=>(k(),l(`li`,{"data-tab-item":``,class:C(I(q)(`relative z-1 transition-colors cursor-default`,`flex items-center justify-center gap-1 h-8 px-2 text-sm rounded-md text-secondary-foreground border border-transparent`,r.value&&`active text-primary-foreground`,t.class)),onClick:o[0]||=t=>I(n)?.setActiveTab(e.name)},[e.icon?(k(),s(te(e.icon),{key:0,class:C([`size-5 md:size-4 text-icon`,r.value&&`text-primary-foreground`])},null,8,[`class`])):c(``,!0),u(`div`,lf,P(I(i)(e.title)),1)],2))}});const df={class:`hidden md:block`};var ff=p({__name:`TitleItem`,props:{icon:{},title:{},active:{type:Boolean,default:!1},class:{}},setup(e){let t=e;return(n,r)=>(k(),l(`div`,{class:C(I(q)(`flex items-center gap-1 cursor-pointer h-8 px-2 text-sm rounded-md text-secondary-foreground border border-transparent`,!e.active&&`hover:bg-accent dark:hover:bg-accent/40`,e.active&&`bg-primary text-primary-foreground`,t.class))},[(k(),s(te(e.icon),{class:C([`size-5 md:size-4 text-icon`,e.active&&`text-primary-foreground`])},null,8,[`class`])),u(`div`,df,P(e.title),1)],2))}}),pf=p({__name:`TitleContainer`,props:{backTitle:{default:void 0},class:{default:``}},setup(e){let t=e,n=Pe(),r=()=>{n.back()};return(n,i)=>(k(),s(I($d),{class:C(I(q)(`relative w-full h-[4rem] flex bg-title pl-4 items-center select-none border-b`,e.backTitle?`pl-12`:``,t.class))},{default:B(()=>[N(n.$slots,`default`),e.backTitle?(k(),l(`div`,{key:0,class:`absolute left-4 top-1/2 translate-y-[-50%] flex gap-1 cursor-pointer`,onClick:r},[f(I(Hu),{class:`size-6`}),u(`div`,null,P(e.backTitle),1)])):c(``,!0)]),_:3},8,[`class`]))}});export{Au as Button,Qu as Checkbox,Zd as ContentWrapper,$u as ContextMenu,td as ContextMenuCheckboxItem,nd as ContextMenuContent,rd as ContextMenuGroup,id as ContextMenuItem,ad as ContextMenuLabel,od as ContextMenuRadioGroup,cd as ContextMenuRadioItem,ld as ContextMenuSeparator,ud as ContextMenuShortcut,dd as ContextMenuSub,fd as ContextMenuSubContent,pd as ContextMenuSubTrigger,md as ContextMenuTrigger,Qd as ContextMenuWrapper,hd as Dialog,gd as DialogClose,vd as DialogContent,yd as DialogDescription,bd as DialogFooter,xd as DialogHeader,_d as DialogOverlay,Sd as DialogScrollContent,Cd as DialogTitle,wd as DialogTrigger,$d as DraggableArea,ef as IconButton,Td as Input,Ed as Label,Dd as NumberField,Od as NumberFieldContent,kd as NumberFieldDecrement,Ad as NumberFieldIncrement,jd as NumberFieldInput,Md as RadioGroup,Nd as RadioGroupItem,Fd as ResizableHandle,Id as ResizablePanel,Ld as ResizablePanelGroup,nf as Search,Rd as Select,zd as SelectContent,Bd as SelectGroup,Hd as SelectItem,Ud as SelectItemText,Wd as SelectLabel,Gd as SelectScrollDownButton,Kd as SelectScrollUpButton,qd as SelectSeparator,Jd as SelectTrigger,Yd as SelectValue,sf as ShortcutInput,rf as SirenSelect,uf as Tab,cf as TabList,Xd as Textarea,pf as TitleContainer,ff as TitleItem,ju as buttonVariants,q as cn};
//# sourceMappingURL=index.js.map
import { defineComponent, computed, createElementBlock, openBlock, normalizeClass, unref, createBlock, createCommentVNode, createElementVNode, resolveDynamicComponent, toDisplayString, Fragment, renderList, ref, createVNode, resolveComponent, withCtx, KeepAlive } from "vue";
import { cn, DraggableArea, Search, ResizablePanelGroup, ResizablePanel, ResizableHandle } from "@siren/ui";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "@siren/core/vue";
import { usePluginStore } from "@siren/core/store";
import { c as createLucideIcon } from "./createLucideIcon-D_qmVIjI.js";
/**
 * @license lucide-vue-next v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Blocks = createLucideIcon("blocks", [
  [
    "path",
    {
      d: "M10 22V7a1 1 0 0 0-1-1H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-5a1 1 0 0 0-1-1H2",
      key: "1ah6g2"
    }
  ],
  ["rect", { x: "14", y: "2", width: "8", height: "8", rx: "1", key: "88lufb" }]
]);
/**
 * @license lucide-vue-next v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Settings = createLucideIcon("settings", [
  [
    "path",
    {
      d: "M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",
      key: "1i5ecw"
    }
  ],
  ["circle", { cx: "12", cy: "12", r: "3", key: "1v7zrd" }]
]);
const _hoisted_1$3 = ["innerHTML"];
const _hoisted_2$1 = ["src"];
const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "SideItem",
  props: {
    className: { default: "" },
    img: {},
    icon: {},
    active: { type: Boolean, default: false },
    title: {},
    onClick: {}
  },
  setup(__props) {
    const props = __props;
    const svgImg = computed(() => props.img && decodeBase64SVG(props.img) || null);
    const decodeBase64SVG = (dataURI) => {
      if (!dataURI.startsWith("data:image/svg+xml;base64,")) {
        return null;
      }
      const base64String = dataURI.replace("data:image/svg+xml;base64,", "");
      try {
        const decodedString = atob(base64String);
        return decodedString;
      } catch (error) {
        console.error("解码 base64 失败:", error);
      }
      return null;
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(unref(cn)(
          "flex items-center gap-[8px] py-[4px] px-[16px] cursor-pointer rounded-r-sm border-l-4 border-transparent",
          !__props.active && "hover:bg-accent",
          __props.active && "border-l-4 border-primary bg-active",
          __props.className
        )),
        onClick: _cache[0] || (_cache[0] = //@ts-ignore
        (...args) => __props.onClick && __props.onClick(...args))
      }, [
        svgImg.value ? (openBlock(), createElementBlock("div", {
          key: 0,
          innerHTML: svgImg.value,
          class: "side-item-icon size-4 text-icon"
        }, null, 8, _hoisted_1$3)) : __props.img ? (openBlock(), createElementBlock("img", {
          key: 1,
          src: __props.img,
          class: "size-4"
        }, null, 8, _hoisted_2$1)) : __props.icon ? (openBlock(), createBlock(resolveDynamicComponent(__props.icon), {
          key: 2,
          class: "size-4 text-icon"
        })) : createCommentVNode("", true),
        createElementVNode("div", null, toDisplayString(__props.title), 1)
      ], 2);
    };
  }
});
const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "SidePluginItem",
  props: {
    plugin: {},
    onClick: {}
  },
  emits: ["click"],
  setup(__props) {
    const props = __props;
    const route = useRoute();
    const isActive = computed(() => route.path === `/main/feature/${props.plugin.id}`);
    const t = useI18n(props.plugin.id);
    return (_ctx, _cache) => {
      return openBlock(), createBlock(_sfc_main$4, {
        active: isActive.value,
        img: __props.plugin.icon,
        title: unref(t)(__props.plugin.display_name),
        onClick: _cache[0] || (_cache[0] = ($event) => _ctx.$emit("click"))
      }, null, 8, ["active", "img", "title"]);
    };
  }
});
const _hoisted_1$2 = { class: "relative" };
const _hoisted_2 = { class: "text-sm px-[12px] mt-[8px] leading-[18px] text-muted-foreground" };
const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "FeatureList",
  props: {
    category: {}
  },
  setup(__props) {
    const props = __props;
    const plugin = usePluginStore();
    const plugins = computed(() => plugin.allPlugins.filter((plugin2) => plugin2.category === props.category));
    const router = useRouter();
    const t = useI18n();
    const toFeature = (id) => {
      router.push(`/main/feature/${id}`);
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1$2, [
        createElementVNode("div", _hoisted_2, toDisplayString(unref(t)(`${__props.category}_category_title`)), 1),
        createElementVNode("div", null, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(plugins.value, (plugin2) => {
            return openBlock(), createBlock(_sfc_main$3, {
              key: plugin2.id,
              plugin: plugin2,
              onClick: ($event) => toFeature(plugin2.id)
            }, null, 8, ["plugin", "onClick"]);
          }), 128))
        ])
      ]);
    };
  }
});
const _hoisted_1$1 = { class: "relative flex flex-col justify-between h-screen px-[8px] bg-side cursor-default select-none" };
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "SideNavigation",
  setup(__props) {
    const searchStr = ref("");
    const route = useRoute();
    const router = useRouter();
    const t = useI18n();
    const toSettings = () => {
      router.push("/main/settings");
    };
    const toAll = () => {
      router.push("/main");
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1$1, [
        createElementVNode("div", null, [
          createVNode(unref(DraggableArea), { class: "h-14" }),
          createVNode(unref(Search), {
            "show-search": true,
            "allow-clear": true,
            class: "mb-4",
            "model-value": searchStr.value,
            "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => searchStr.value = $event),
            placeholder: unref(t)("search")
          }, null, 8, ["model-value", "placeholder"]),
          createVNode(_sfc_main$4, {
            active: unref(route).path === "/main",
            class: "mb-4",
            icon: unref(Blocks),
            title: unref(t)("all_features"),
            onClick: toAll
          }, null, 8, ["active", "icon", "title"]),
          createVNode(_sfc_main$2, { category: "video" }),
          createVNode(_sfc_main$2, { category: "audio" }),
          createVNode(_sfc_main$2, { category: "image" }),
          createVNode(_sfc_main$2, { category: "text" }),
          createVNode(_sfc_main$2, { category: "other" })
        ]),
        createElementVNode("div", null, [
          createVNode(_sfc_main$4, {
            active: unref(route).path.startsWith("/main/settings"),
            icon: unref(Settings),
            title: unref(t)("settings"),
            onClick: toSettings
          }, null, 8, ["active", "icon", "title"]),
          _cache[1] || (_cache[1] = createElementVNode("div", { class: "h-5" }, null, -1))
        ])
      ]);
    };
  }
});
const _hoisted_1 = { className: "flex flex-col grow h-full" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "MainLayout",
  setup(__props) {
    return (_ctx, _cache) => {
      const _component_router_view = resolveComponent("router-view");
      return openBlock(), createBlock(unref(ResizablePanelGroup), {
        direction: "horizontal",
        class: "rounded-lg border"
      }, {
        default: withCtx(() => [
          createVNode(unref(ResizablePanel), {
            defaultSize: 10,
            class: "max-w-[22rem] min-w-[13rem]"
          }, {
            default: withCtx(() => [
              createVNode(_sfc_main$1)
            ]),
            _: 1
          }),
          createVNode(unref(ResizableHandle)),
          createVNode(unref(ResizablePanel), { class: "bg-content" }, {
            default: withCtx(() => [
              createElementVNode("div", _hoisted_1, [
                createVNode(_component_router_view, null, {
                  default: withCtx(({ Component }) => [
                    (openBlock(), createBlock(KeepAlive, null, [
                      (openBlock(), createBlock(resolveDynamicComponent(Component)))
                    ], 1024))
                  ]),
                  _: 1
                })
              ])
            ]),
            _: 1
          })
        ]),
        _: 1
      });
    };
  }
});
export {
  _sfc_main as default
};

import { defineComponent, ref, createElementBlock, openBlock, createVNode, createElementVNode, unref } from "vue";
import { useI18n } from "@siren/core/vue";
import { Search } from "@siren/ui";
import { getCurrentWebviewWindow } from "@siren/core/tauri";
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "OmniLayout",
  setup(__props) {
    const searchStr = ref("");
    const t = useI18n();
    const handleKeydown = (e) => {
      console.log("key down ", e);
      if (e.key === "Escape") {
        const window = getCurrentWebviewWindow();
        window.close();
      }
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        tabindex: "-1",
        class: "p-4 size-full",
        onKeydown: handleKeydown
      }, [
        createVNode(unref(Search), {
          "show-search": true,
          "allow-clear": true,
          class: "mb-4",
          "model-value": searchStr.value,
          "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => searchStr.value = $event),
          placeholder: unref(t)("search")
        }, null, 8, ["model-value", "placeholder"]),
        _cache[1] || (_cache[1] = createElementVNode("div", null, "OmniLayout", -1))
      ], 32);
    };
  }
});
export {
  _sfc_main as default
};

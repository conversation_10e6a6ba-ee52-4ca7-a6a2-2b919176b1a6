import { defineComponent, inject, createElement<PERSON><PERSON>, openBlock, createElementVNode, renderSlot, toDisplayString, unref, computed, createCommentVNode, createVNode, normalizeClass, createBlock, withCtx, ref, Fragment, renderList, h, resolveComponent, mergeProps, resolveDynamicComponent, provide, createTextVNode } from "vue";
import { useSettingsStore } from "@siren/core/store";
import { cn, IconButton, Checkbox, ShortcutInput, RadioGroup, RadioGroupItem, SirenSelect, NumberField, NumberFieldContent, NumberFieldDecrement, NumberFieldInput, NumberFieldIncrement, Button } from "@siren/ui";
import { useRoute } from "vue-router";
import { useI18n } from "@siren/core/vue";
import { c as createLucideIcon } from "./createLucideIcon-D_qmVIjI.js";
import { KeyBinding } from "@siren/core";
import { invokeMain, unregisterGlobalShortcut, registerGlobalShortcut } from "@siren/core/api";
/**
 * @license lucide-vue-next v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const RotateCcw = createLucideIcon("rotate-ccw", [
  ["path", { d: "M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8", key: "1357e3" }],
  ["path", { d: "M3 3v5h5", key: "1xhq8a" }]
]);
const _hoisted_1$7 = { class: "relative flex group w-fit" };
const _hoisted_2$2 = { class: "flex-[12rem] grow-0 shrink-0 align-middle text-end w-fit" };
const _hoisted_3$2 = { class: "inline-flex h-9 text-end items-center" };
const _hoisted_4$2 = { class: "flex flex-col gap-2 px-6" };
const _sfc_main$d = /* @__PURE__ */ defineComponent({
  __name: "SettingWrapper",
  props: {
    label: {}
  },
  setup(__props) {
    const scope = inject("scope");
    const t = useI18n(scope == null ? void 0 : scope.value);
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1$7, [
        createElementVNode("div", _hoisted_2$2, [
          createElementVNode("div", _hoisted_3$2, toDisplayString(unref(t)(__props.label || "")), 1)
        ]),
        createElementVNode("div", _hoisted_4$2, [
          renderSlot(_ctx.$slots, "default")
        ]),
        renderSlot(_ctx.$slots, "extra")
      ]);
    };
  }
});
function useSetting(props) {
  const settings = useSettingsStore();
  const value = computed(() => {
    const result = props.setting.value === null ? props.setting.default_value : props.setting.value;
    return result;
  });
  const handleChange = (value2) => {
    settings.doUpdateSetting({
      id: props.setting.id,
      value: value2
    });
  };
  const scope = inject("scope");
  const t = useI18n(scope == null ? void 0 : scope.value);
  return {
    t,
    value,
    handleChange
  };
}
const _hoisted_1$6 = {
  key: 0,
  class: "text-sm text-muted-foreground whitespace-nowrap leading-[1]"
};
const _sfc_main$c = /* @__PURE__ */ defineComponent({
  __name: "SettingDescription",
  props: {
    desc: { default: void 0 }
  },
  setup(__props) {
    const t = useI18n();
    return (_ctx, _cache) => {
      return __props.desc ? (openBlock(), createElementBlock("div", _hoisted_1$6, toDisplayString(unref(t)(__props.desc)), 1)) : createCommentVNode("", true);
    };
  }
});
const _hoisted_1$5 = { class: "relative flex group w-fit" };
const _hoisted_2$1 = { class: "flex-[12rem] grow-0 shrink-0 align-middle text-end w-fit" };
const _hoisted_3$1 = { class: "inline-flex h-9 text-end items-center" };
const _hoisted_4$1 = { class: "flex flex-col gap-2 px-6" };
const _hoisted_5 = { class: "px-4" };
const _sfc_main$b = /* @__PURE__ */ defineComponent({
  __name: "SettingItem",
  props: {
    setting: {}
  },
  setup(__props) {
    const props = __props;
    const scope = inject("scope");
    const t = useI18n(scope == null ? void 0 : scope.value);
    const settings = useSettingsStore();
    const handleReset = () => {
      settings.doUpdateSetting({
        id: props.setting.id,
        value: null,
        recursive: true
      });
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1$5, [
        createElementVNode("div", _hoisted_2$1, [
          createElementVNode("div", _hoisted_3$1, toDisplayString(unref(t)(__props.setting.name)), 1)
        ]),
        createElementVNode("div", _hoisted_4$1, [
          renderSlot(_ctx.$slots, "default"),
          createVNode(_sfc_main$c, {
            desc: __props.setting.description
          }, null, 8, ["desc"])
        ]),
        createElementVNode("div", _hoisted_5, [
          createElementVNode("div", {
            class: normalizeClass(unref(cn)(
              "h-9 flex items-center opacity-0 group-hover:opacity-100 transition-opacity"
            ))
          }, [
            createVNode(unref(IconButton), {
              icon: unref(RotateCcw),
              onClick: handleReset
            }, null, 8, ["icon"])
          ], 2)
        ])
      ]);
    };
  }
});
const _hoisted_1$4 = { class: "h-9 flex items-center" };
const _sfc_main$a = /* @__PURE__ */ defineComponent({
  __name: "CheckboxSetting",
  props: {
    setting: {}
  },
  setup(__props) {
    const props = __props;
    const {
      value,
      handleChange
    } = useSetting(props);
    return (_ctx, _cache) => {
      return openBlock(), createBlock(_sfc_main$b, { setting: __props.setting }, {
        default: withCtx(() => [
          createElementVNode("div", _hoisted_1$4, [
            createVNode(unref(Checkbox), {
              "model-value": unref(value),
              "onUpdate:modelValue": unref(handleChange)
            }, null, 8, ["model-value", "onUpdate:modelValue"])
          ])
        ]),
        _: 1
      }, 8, ["setting"]);
    };
  }
});
const _hoisted_1$3 = { className: "flex flex-col gap-2" };
const _sfc_main$9 = /* @__PURE__ */ defineComponent({
  __name: "KeyboardSetting",
  props: {
    setting: {}
  },
  setup(__props) {
    const props = __props;
    const changed = ref(false);
    const settings = useSettingsStore();
    const handleChange = (value) => {
      changed.value = true;
      settings.doUpdateSetting({
        id: props.setting.id,
        value
      });
    };
    const handleFocus = () => {
      const keyBinding = KeyBinding.from(props.setting.value || props.setting.default_value);
      changed.value = false;
      invokeMain(unregisterGlobalShortcut({
        keyBinding: keyBinding.toType()
      }));
    };
    const handleBlur = () => {
      const keyBinding = KeyBinding.from(props.setting.value || props.setting.default_value);
      if (!changed.value) {
        invokeMain(registerGlobalShortcut({
          keyBinding: keyBinding.toType()
        }));
      }
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1$3, [
        createVNode(unref(ShortcutInput), {
          disabled: __props.setting.disabled,
          modelValue: __props.setting.value || __props.setting.default_value,
          "onUpdate:modelValue": handleChange,
          onFocus: handleFocus,
          onBlur: handleBlur
        }, null, 8, ["disabled", "modelValue"]),
        createVNode(_sfc_main$c, {
          desc: __props.setting.description
        }, null, 8, ["desc"])
      ]);
    };
  }
});
const _hoisted_1$2 = { class: "flex gap-3" };
const _hoisted_2 = { class: "h-9 flex items-center" };
const _hoisted_3 = { class: "" };
const _hoisted_4 = ["for"];
const _sfc_main$8 = /* @__PURE__ */ defineComponent({
  __name: "CheckboxSetting",
  props: {
    setting: {}
  },
  setup(__props) {
    const props = __props;
    const t = useI18n();
    const {
      value,
      handleChange
    } = useSetting(props);
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1$2, [
        createElementVNode("div", _hoisted_2, [
          createVNode(unref(Checkbox), {
            id: __props.setting.id,
            "model-value": unref(value),
            "onUpdate:modelValue": unref(handleChange)
          }, null, 8, ["id", "model-value", "onUpdate:modelValue"])
        ]),
        createElementVNode("div", _hoisted_3, [
          createElementVNode("label", {
            class: "leading-9 text-sm",
            for: __props.setting.id
          }, toDisplayString(unref(t)(__props.setting.name)), 9, _hoisted_4),
          createVNode(_sfc_main$c, {
            desc: __props.setting.description
          }, null, 8, ["desc"])
        ])
      ]);
    };
  }
});
const _hoisted_1$1 = { class: "flex flex-col gap-2" };
const _sfc_main$7 = /* @__PURE__ */ defineComponent({
  __name: "GroupSetting",
  props: {
    setting: {}
  },
  setup(__props) {
    const props = __props;
    const settings = useSettingsStore();
    const ids = computed(
      () => settings.settingsArr.filter((s) => s.parent_id === props.setting.id).map((s) => s.id)
    );
    const Any = (props2) => {
      var _a;
      const settings2 = useSettingsStore();
      const setting = computed(() => settings2.settings[props2.id]);
      switch ((_a = setting.value) == null ? void 0 : _a.type) {
        case "keyboard":
          return h(_sfc_main$9, { setting: setting.value });
        case "checkbox":
          return h(_sfc_main$8, { setting: setting.value });
      }
      return null;
    };
    return (_ctx, _cache) => {
      return openBlock(), createBlock(_sfc_main$b, { setting: __props.setting }, {
        default: withCtx(() => [
          createElementVNode("div", _hoisted_1$1, [
            (openBlock(true), createElementBlock(Fragment, null, renderList(ids.value, (id) => {
              return openBlock(), createBlock(Any, {
                key: id,
                id
              }, null, 8, ["id"]);
            }), 128))
          ])
        ]),
        _: 1
      }, 8, ["setting"]);
    };
  }
});
const _sfc_main$6 = /* @__PURE__ */ defineComponent({
  __name: "InputSetting",
  props: {
    setting: {}
  },
  setup(__props) {
    const t = (key) => key;
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", null, toDisplayString(t(__props.setting.name)), 1);
    };
  }
});
const _sfc_main$5 = /* @__PURE__ */ defineComponent({
  __name: "KeyboardSetting",
  props: {
    setting: {}
  },
  setup(__props) {
    const props = __props;
    const settings = useSettingsStore();
    const changed = ref(false);
    const handleChange = (value) => {
      changed.value = true;
      settings.doUpdateSetting({
        id: props.setting.id,
        value
      });
    };
    const handleFocus = () => {
      const keyBinding = KeyBinding.from(props.setting.value || props.setting.default_value);
      changed.value = false;
      invokeMain(unregisterGlobalShortcut({
        keyBinding: keyBinding.toType()
      }));
    };
    const handleBlur = () => {
      const keyBinding = KeyBinding.from(props.setting.value || props.setting.default_value);
      if (!changed.value) {
        invokeMain(registerGlobalShortcut({
          keyBinding: keyBinding.toType()
        }));
      }
    };
    return (_ctx, _cache) => {
      const _component_ShortcutInput = resolveComponent("ShortcutInput");
      return openBlock(), createBlock(_sfc_main$b, { setting: __props.setting }, {
        default: withCtx(() => [
          createVNode(_component_ShortcutInput, {
            value: __props.setting.value || __props.setting.default_value,
            "onUpdate:value": handleChange,
            onFocus: handleFocus,
            onBlur: handleBlur
          }, null, 8, ["value"])
        ]),
        _: 1
      }, 8, ["setting"]);
    };
  }
});
const _hoisted_1 = ["for"];
const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "RadioSetting",
  props: {
    setting: {}
  },
  setup(__props) {
    const props = __props;
    const {
      value,
      handleChange,
      t
    } = useSetting(props);
    return (_ctx, _cache) => {
      return openBlock(), createBlock(_sfc_main$b, { setting: __props.setting }, {
        default: withCtx(() => [
          createVNode(unref(RadioGroup), {
            "model-value": unref(value),
            "onUpdate:modelValue": unref(handleChange)
          }, {
            default: withCtx(() => [
              (openBlock(true), createElementBlock(Fragment, null, renderList(__props.setting.options, (option) => {
                return openBlock(), createElementBlock("div", {
                  key: option.value,
                  class: "flex items-center gap-3 h-9 not-first:mt-[-16px]"
                }, [
                  createVNode(unref(RadioGroupItem), {
                    value: option.value,
                    id: option.value
                  }, null, 8, ["value", "id"]),
                  createElementVNode("label", {
                    class: "whitespace-nowrap text-sm",
                    for: option.value
                  }, toDisplayString(unref(t)(option.name)), 9, _hoisted_1)
                ]);
              }), 128))
            ]),
            _: 1
          }, 8, ["model-value", "onUpdate:modelValue"])
        ]),
        _: 1
      }, 8, ["setting"]);
    };
  }
});
const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "SelectSetting",
  props: {
    setting: {}
  },
  setup(__props) {
    const props = __props;
    const {
      value,
      handleChange
    } = useSetting(props);
    return (_ctx, _cache) => {
      return openBlock(), createBlock(_sfc_main$b, { setting: __props.setting }, {
        default: withCtx(() => [
          createVNode(unref(SirenSelect), {
            "model-value": unref(value),
            options: __props.setting.options,
            "onUpdate:modelValue": unref(handleChange)
          }, null, 8, ["model-value", "options", "onUpdate:modelValue"])
        ]),
        _: 1
      }, 8, ["setting"]);
    };
  }
});
const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "CountSetting",
  props: {
    setting: {}
  },
  setup(__props) {
    const props = __props;
    const {
      value,
      handleChange
    } = useSetting(props);
    const restProps = computed(() => {
      const rest = {};
      if (typeof props.setting.min === "number") {
        rest.min = props.setting.min;
      }
      if (typeof props.setting.max === "number") {
        rest.max = props.setting.max;
      }
      return rest;
    });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(_sfc_main$b, { setting: __props.setting }, {
        default: withCtx(() => [
          createVNode(unref(NumberField), mergeProps({
            "model-value": unref(value),
            "onUpdate:modelValue": unref(handleChange)
          }, restProps.value), {
            default: withCtx(() => [
              createVNode(unref(NumberFieldContent), null, {
                default: withCtx(() => [
                  createVNode(unref(NumberFieldDecrement)),
                  createVNode(unref(NumberFieldInput), { class: "w-26" }),
                  createVNode(unref(NumberFieldIncrement))
                ]),
                _: 1
              })
            ]),
            _: 1
          }, 16, ["model-value", "onUpdate:modelValue"])
        ]),
        _: 1
      }, 8, ["setting"]);
    };
  }
});
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "index",
  props: {
    id: {}
  },
  setup(__props) {
    const props = __props;
    const settings = useSettingsStore();
    const setting = computed(() => settings.settings[props.id]);
    const settingComponent = computed(() => {
      if (!setting.value) return null;
      switch (setting.value.type) {
        case "group":
          return _sfc_main$7;
        case "input":
          return _sfc_main$6;
        case "checkbox":
          return _sfc_main$a;
        case "keyboard":
          return _sfc_main$5;
        case "select":
          return _sfc_main$3;
        case "radio":
          return _sfc_main$4;
        case "count":
          return _sfc_main$2;
        default:
          return null;
      }
    });
    return (_ctx, _cache) => {
      return setting.value ? (openBlock(), createBlock(resolveDynamicComponent(settingComponent.value), {
        key: 0,
        setting: setting.value
      }, null, 8, ["setting"])) : createCommentVNode("", true);
    };
  }
});
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "SingleColumnContent",
  setup(__props) {
    const settings = useSettingsStore();
    const t = useI18n();
    const route = useRoute();
    const id = computed(() => route.params.sub || route.params.root);
    const isSub = computed(() => !!route.params.sub);
    const scope = computed(() => {
      if (route.params.sub) {
        const sub = route.params.sub;
        return sub.split(".").pop();
      }
      return void 0;
    });
    provide("scope", scope);
    const ids = computed(
      () => settings.settingsArr.filter((s) => s.parent_id === id.value).map((s) => s.id)
    );
    const handleReset = () => {
      settings.doUpdateSetting({
        id: id.value,
        value: null,
        recursive: true
      });
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(unref(cn)(
          !isSub.value && "w-3/4 m-auto py-16 flex flex-col gap-6",
          isSub.value && "pl-6 m-auto py-16 flex flex-col gap-6"
        ))
      }, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(ids.value, (sectionId) => {
          return openBlock(), createBlock(_sfc_main$1, {
            key: sectionId,
            id: sectionId
          }, null, 8, ["id"]);
        }), 128)),
        createVNode(_sfc_main$d, null, {
          default: withCtx(() => [
            createVNode(unref(Button), { onClick: handleReset }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(t)("reset")), 1)
              ]),
              _: 1
            })
          ]),
          _: 1
        })
      ], 2);
    };
  }
});
export {
  _sfc_main as _
};

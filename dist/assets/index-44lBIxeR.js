const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/MainLayout-CbE5f_hD.js","assets/createLucideIcon-D_qmVIjI.js","assets/MainLayout-C-CW5okw.css","assets/index-CEQt7rOJ.js","assets/index-BjnkqnB4.js","assets/SingleColumnContent.vue_vue_type_script_setup_true_lang-CXhtzLjV.js","assets/SingleColumnContent-DP7hmxfd.js"])))=>i.map(i=>d[i]);
import { computed, watchEffect, defineComponent, ref, resolveComponent, createBlock, createCommentVNode, openBlock, createApp } from "vue";
import { createPinia } from "pinia";
import { createRouter, createWebHistory } from "vue-router";
import { useSettingsStore, useAppStore, usePluginStore } from "@siren/core/store";
import { useMediaQuery } from "@vueuse/core";
import { getManager, RenderManager, PluginManager } from "@siren/core";
import { invokeMain, getLangMap, getPlugins } from "@siren/core/api";
import { getAppName, getAppVersion } from "@siren/core/tauri";
(function polyfill() {
  const relList = document.createElement("link").relList;
  if (relList && relList.supports && relList.supports("modulepreload")) {
    return;
  }
  for (const link of document.querySelectorAll('link[rel="modulepreload"]')) {
    processPreload(link);
  }
  new MutationObserver((mutations) => {
    for (const mutation of mutations) {
      if (mutation.type !== "childList") {
        continue;
      }
      for (const node of mutation.addedNodes) {
        if (node.tagName === "LINK" && node.rel === "modulepreload")
          processPreload(node);
      }
    }
  }).observe(document, { childList: true, subtree: true });
  function getFetchOpts(link) {
    const fetchOpts = {};
    if (link.integrity) fetchOpts.integrity = link.integrity;
    if (link.referrerPolicy) fetchOpts.referrerPolicy = link.referrerPolicy;
    if (link.crossOrigin === "use-credentials")
      fetchOpts.credentials = "include";
    else if (link.crossOrigin === "anonymous") fetchOpts.credentials = "omit";
    else fetchOpts.credentials = "same-origin";
    return fetchOpts;
  }
  function processPreload(link) {
    if (link.ep)
      return;
    link.ep = true;
    const fetchOpts = getFetchOpts(link);
    fetch(link.href, fetchOpts);
  }
})();
const scriptRel = "modulepreload";
const assetsURL = function(dep) {
  return "/" + dep;
};
const seen = {};
const __vitePreload = function preload(baseModule, deps, importerUrl) {
  let promise = Promise.resolve();
  if (deps && deps.length > 0) {
    let allSettled2 = function(promises) {
      return Promise.all(
        promises.map(
          (p) => Promise.resolve(p).then(
            (value) => ({ status: "fulfilled", value }),
            (reason) => ({ status: "rejected", reason })
          )
        )
      );
    };
    document.getElementsByTagName("link");
    const cspNonceMeta = document.querySelector(
      "meta[property=csp-nonce]"
    );
    const cspNonce = (cspNonceMeta == null ? void 0 : cspNonceMeta.nonce) || (cspNonceMeta == null ? void 0 : cspNonceMeta.getAttribute("nonce"));
    promise = allSettled2(
      deps.map((dep) => {
        dep = assetsURL(dep);
        if (dep in seen) return;
        seen[dep] = true;
        const isCss = dep.endsWith(".css");
        const cssSelector = isCss ? '[rel="stylesheet"]' : "";
        if (document.querySelector(`link[href="${dep}"]${cssSelector}`)) {
          return;
        }
        const link = document.createElement("link");
        link.rel = isCss ? "stylesheet" : scriptRel;
        if (!isCss) {
          link.as = "script";
        }
        link.crossOrigin = "";
        link.href = dep;
        if (cspNonce) {
          link.setAttribute("nonce", cspNonce);
        }
        document.head.appendChild(link);
        if (isCss) {
          return new Promise((res, rej) => {
            link.addEventListener("load", res);
            link.addEventListener(
              "error",
              () => rej(new Error(`Unable to preload CSS for ${dep}`))
            );
          });
        }
      })
    );
  }
  function handlePreloadError(err) {
    const e = new Event("vite:preloadError", {
      cancelable: true
    });
    e.payload = err;
    window.dispatchEvent(e);
    if (!e.defaultPrevented) {
      throw err;
    }
  }
  return promise.then((res) => {
    for (const item of res || []) {
      if (item.status !== "rejected") continue;
      handlePreloadError(item.reason);
    }
    return baseModule().catch(handlePreloadError);
  });
};
const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: "/",
      redirect: "/main"
    },
    {
      path: "/main",
      component: () => __vitePreload(() => import("./MainLayout-CbE5f_hD.js"), true ? __vite__mapDeps([0,1,2]) : void 0),
      children: [
        {
          path: "",
          component: () => __vitePreload(() => import("./index-CN6wI51-.js"), true ? [] : void 0)
        },
        {
          name: "feature",
          path: "feature/:id",
          component: () => __vitePreload(() => import("./index-CEQt7rOJ.js"), true ? __vite__mapDeps([3,1]) : void 0)
        },
        {
          path: "settings",
          component: () => __vitePreload(() => import("./index-BKqoVe3N.js"), true ? [] : void 0),
          children: [
            {
              path: ":root",
              component: () => __vitePreload(() => import("./index-BjnkqnB4.js"), true ? __vite__mapDeps([4,5,1]) : void 0),
              children: [
                {
                  path: ":sub",
                  component: () => __vitePreload(() => import("./SingleColumnContent-DP7hmxfd.js"), true ? __vite__mapDeps([6,5,1]) : void 0)
                }
              ]
            }
          ]
        }
      ]
    },
    {
      path: "/omni",
      component: () => __vitePreload(() => import("./OmniLayout-BlL2Tgwm.js"), true ? [] : void 0)
    }
  ]
});
function useSettings() {
  const settings = useSettingsStore();
  settings.fetchSettings();
}
function useTheme() {
  const settings = useSettingsStore();
  const prefersDark = useMediaQuery("(prefers-color-scheme: dark)");
  const theme = computed(
    () => settings.theme === "system" ? prefersDark.value ? "dark" : "light" : settings.theme
  );
  watchEffect(() => {
    const root = document.documentElement;
    root.classList.remove("light", "dark");
    root.classList.add(theme.value);
  });
  return {
    theme
  };
}
async function initialize() {
  const app2 = useAppStore();
  const plugin = usePluginStore();
  const [appName, appVersion] = await Promise.all([
    getAppName(),
    getAppVersion()
  ]);
  app2.setAppName(appName);
  app2.setAppVersion(appVersion);
  getManager(RenderManager);
  window.__LANG_MAP__ = await invokeMain(getLangMap());
  const plugins = await invokeMain(getPlugins());
  plugin.registerAll(plugins);
  await getManager(PluginManager).registerAll(plugins);
}
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "App",
  setup(__props) {
    const isReady = ref(false);
    initialize().then(() => {
      isReady.value = true;
    });
    useSettings();
    useTheme();
    return (_ctx, _cache) => {
      const _component_RouterView = resolveComponent("RouterView");
      return isReady.value ? (openBlock(), createBlock(_component_RouterView, { key: 0 })) : createCommentVNode("", true);
    };
  }
});
{
  document.addEventListener("contextmenu", (e) => {
    e.preventDefault();
  });
}
const pinia = createPinia();
const app = createApp(_sfc_main);
app.use(pinia);
app.use(router);
app.mount("#app");

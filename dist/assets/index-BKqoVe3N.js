import { defineComponent, computed, watch, onActivated, resolveComponent, createElementBlock, openBlock, Fragment, createVNode, unref, withCtx, createElementVNode, renderList, createBlock } from "vue";
import { useSettingsStore } from "@siren/core/store";
import { useI18n } from "@siren/core/vue";
import { TitleContainer, TabList, Tab, ContentWrapper } from "@siren/ui";
import { useRouter } from "vue-router";
const _hoisted_1 = { class: "absolute h-1/2 left-1/2 top-1/2 translate-[-50%] flex items-center" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  setup(__props) {
    const settings = useSettingsStore();
    const rootSettings = computed(() => settings.rootSettingIds.map((id) => settings.settings[id]));
    const t = useI18n();
    const router = useRouter();
    watch(() => settings.currentSettingRootId, (value) => {
      router.push(`/main/settings/${value}`);
    }, { immediate: true });
    onActivated(() => {
      router.push(`/main/settings/${settings.currentSettingRootId}`);
    });
    return (_ctx, _cache) => {
      const _component_router_view = resolveComponent("router-view");
      return openBlock(), createElementBlock(Fragment, null, [
        createVNode(unref(TitleContainer), {
          "back-title": unref(t)("settings")
        }, {
          default: withCtx(() => [
            createElementVNode("div", _hoisted_1, [
              createVNode(unref(TabList), {
                "model-value": unref(settings).currentSettingRootId,
                "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => unref(settings).setCurrentSettingRootId($event))
              }, {
                default: withCtx(() => [
                  (openBlock(true), createElementBlock(Fragment, null, renderList(rootSettings.value, (setting) => {
                    return openBlock(), createBlock(unref(Tab), {
                      key: setting.id,
                      name: setting.id,
                      title: unref(t)(setting.name)
                    }, null, 8, ["name", "title"]);
                  }), 128))
                ]),
                _: 1
              }, 8, ["model-value"])
            ])
          ]),
          _: 1
        }, 8, ["back-title"]),
        createVNode(unref(ContentWrapper), null, {
          default: withCtx(() => [
            createVNode(_component_router_view)
          ]),
          _: 1
        })
      ], 64);
    };
  }
});
export {
  _sfc_main as default
};

import { defineComponent, computed, ref, watch, resolveComponent, createElement<PERSON><PERSON>, openBlock, createVNode, createElementVNode, unref, withCtx, Fragment, renderList, createBlock, resolveDynamicComponent } from "vue";
import { useSettingsStore } from "@siren/core/store";
import { TabList, Tab } from "@siren/ui";
import { useRoute, useRouter } from "vue-router";
import { _ as _sfc_main$2 } from "./SingleColumnContent.vue_vue_type_script_setup_true_lang-CXhtzLjV.js";
import "@siren/core/vue";
import "./createLucideIcon-D_qmVIjI.js";
import "@siren/core";
import "@siren/core/api";
const _hoisted_1 = { class: "flex w-3/4 h-full m-auto" };
const _hoisted_2 = { class: "grow" };
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "TwoColumnsContent",
  setup(__props) {
    const route = useRoute();
    const router = useRouter();
    const id = computed(() => route.params.root);
    const settings = useSettingsStore();
    const subSettings = computed(
      () => settings.settingsArr.filter((s) => s.parent_id === id.value).filter((s) => s.id)
    );
    const getScope = (id2) => id2.split(".").pop();
    const currentColumn = ref(subSettings.value[0].id);
    watch([id, currentColumn], ([idValue, columnValue]) => {
      if (route.path.startsWith(`/main/settings/${idValue}`)) {
        router.push(`/main/settings/${idValue}/${columnValue}`);
      }
    }, { immediate: true });
    return (_ctx, _cache) => {
      const _component_router_view = resolveComponent("router-view");
      return openBlock(), createElementBlock("div", _hoisted_1, [
        createVNode(unref(TabList), {
          "model-value": currentColumn.value,
          "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => currentColumn.value = $event),
          direction: "vertical",
          class: "grow-0 shrink-0 gap-2 justify-start py-16"
        }, {
          default: withCtx(() => [
            (openBlock(true), createElementBlock(Fragment, null, renderList(subSettings.value, (setting) => {
              return openBlock(), createBlock(unref(Tab), {
                key: setting.id,
                name: setting.id,
                title: setting.name,
                scope: getScope(setting.id),
                class: "text-center"
              }, null, 8, ["name", "title", "scope"]);
            }), 128))
          ]),
          _: 1
        }, 8, ["model-value"]),
        createElementVNode("div", _hoisted_2, [
          createVNode(_component_router_view)
        ])
      ]);
    };
  }
});
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  setup(__props) {
    const route = useRoute();
    const settings = useSettingsStore();
    const isSubCategory = computed(
      () => settings.settingsArr.filter((s) => s.parent_id === route.params.root).every((s) => s.type === "category")
    );
    return (_ctx, _cache) => {
      return openBlock(), createBlock(resolveDynamicComponent(isSubCategory.value ? _sfc_main$1 : _sfc_main$2));
    };
  }
});
export {
  _sfc_main as default
};

/*! tailwindcss v4.1.13 | MIT License | https://tailwindcss.com */
@layer properties {
@supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
*, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-pan-x: initial;
      --tw-pan-y: initial;
      --tw-pinch-zoom: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-x-reverse: 0;
      --tw-border-style: solid;
      --tw-divide-y-reverse: 0;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-content: "";
      --tw-animation-delay: 0s;
      --tw-animation-direction: normal;
      --tw-animation-duration: initial;
      --tw-animation-fill-mode: none;
      --tw-animation-iteration-count: 1;
      --tw-enter-blur: 0;
      --tw-enter-opacity: 1;
      --tw-enter-rotate: 0;
      --tw-enter-scale: 1;
      --tw-enter-translate-x: 0;
      --tw-enter-translate-y: 0;
      --tw-exit-blur: 0;
      --tw-exit-opacity: 1;
      --tw-exit-rotate: 0;
      --tw-exit-scale: 1;
      --tw-exit-translate-x: 0;
      --tw-exit-translate-y: 0;
}
}
}
@layer theme {
:root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-lg: 32rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --tracking-widest: .1em;
    --radius-xs: .125rem;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
}
}
@layer base {
*, :after, :before, ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
}
::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
}
html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
}
hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
}
abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
}
h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
}
a {
    color: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
}
b, strong {
    font-weight: bolder;
}
code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
}
small {
    font-size: 80%;
}
sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
}
sub {
    bottom: -.25em;
}
sup {
    top: -.5em;
}
table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
}
:-moz-focusring {
    outline: auto;
}
progress {
    vertical-align: baseline;
}
summary {
    display: list-item;
}
ol, ul, menu {
    list-style: none;
}
img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
}
img, video {
    max-width: 100%;
    height: auto;
}
button, input, select, optgroup, textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
}
::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
}
:where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
}
:where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
}
::file-selector-button {
    margin-inline-end: 4px;
}
::placeholder {
    opacity: 1;
}
@supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
::placeholder {
      color: currentColor;
}
@supports (color: color-mix(in lab, red, red)) {
::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
}
}
}
textarea {
    resize: vertical;
}
::-webkit-search-decoration {
    -webkit-appearance: none;
}
::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
}
::-webkit-datetime-edit {
    display: inline-flex;
}
::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
}
::-webkit-datetime-edit {
    padding-block: 0;
}
::-webkit-datetime-edit-year-field {
    padding-block: 0;
}
::-webkit-datetime-edit-month-field {
    padding-block: 0;
}
::-webkit-datetime-edit-day-field {
    padding-block: 0;
}
::-webkit-datetime-edit-hour-field {
    padding-block: 0;
}
::-webkit-datetime-edit-minute-field {
    padding-block: 0;
}
::-webkit-datetime-edit-second-field {
    padding-block: 0;
}
::-webkit-datetime-edit-millisecond-field {
    padding-block: 0;
}
::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
}
::-webkit-calendar-picker-indicator {
    line-height: 1;
}
:-moz-ui-invalid {
    box-shadow: none;
}
button, input:where([type="button"], [type="reset"], [type="submit"]) {
    appearance: button;
}
::file-selector-button {
    appearance: button;
}
::-webkit-inner-spin-button {
    height: auto;
}
::-webkit-outer-spin-button {
    height: auto;
}
[hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
}
* {
    border-color: var(--border);
    outline-color: var(--ring);
}
@supports (color: color-mix(in lab, red, red)) {
* {
      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
}
}
body {
    background-color: var(--background);
    color: var(--foreground);
}
}
@layer components;
@layer utilities {
.pointer-events-none {
    pointer-events: none;
}
.collapse {
    visibility: collapse;
}
.invisible {
    visibility: hidden;
}
.visible {
    visibility: visible;
}
.sr-only {
    clip-path: inset(50%);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
}
.not-sr-only {
    clip-path: none;
    white-space: normal;
    width: auto;
    height: auto;
    margin: 0;
    padding: 0;
    position: static;
    overflow: visible;
}
.absolute {
    position: absolute;
}
.fixed {
    position: fixed;
}
.relative {
    position: relative;
}
.static {
    position: static;
}
.sticky {
    position: sticky;
}
.inset-0 {
    inset: calc(var(--spacing) * 0);
}
.top-0 {
    top: calc(var(--spacing) * 0);
}
.top-1\/2 {
    top: 50%;
}
.top-4 {
    top: calc(var(--spacing) * 4);
}
.top-\[50\%\] {
    top: 50%;
}
.right-0 {
    right: calc(var(--spacing) * 0);
}
.right-2 {
    right: calc(var(--spacing) * 2);
}
.right-3 {
    right: calc(var(--spacing) * 3);
}
.right-4 {
    right: calc(var(--spacing) * 4);
}
.left-0 {
    left: calc(var(--spacing) * 0);
}
.left-1\/2 {
    left: 50%;
}
.left-2 {
    left: calc(var(--spacing) * 2);
}
.left-4 {
    left: calc(var(--spacing) * 4);
}
.left-\[50\%\] {
    left: 50%;
}
.isolate {
    isolation: isolate;
}
.isolation-auto {
    isolation: auto;
}
.z-0 {
    z-index: 0;
}
.z-1 {
    z-index: 1;
}
.z-10 {
    z-index: 10;
}
.z-50 {
    z-index: 50;
}
.container {
    width: 100%;
}
@media (min-width: 40rem) {
.container {
      max-width: 40rem;
}
}
@media (min-width: 48rem) {
.container {
      max-width: 48rem;
}
}
@media (min-width: 64rem) {
.container {
      max-width: 64rem;
}
}
@media (min-width: 80rem) {
.container {
      max-width: 80rem;
}
}
@media (min-width: 96rem) {
.container {
      max-width: 96rem;
}
}
.m-0 {
    margin: calc(var(--spacing) * 0);
}
.m-auto {
    margin: auto;
}
.-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
}
.my-1 {
    margin-block: calc(var(--spacing) * 1);
}
.my-8 {
    margin-block: calc(var(--spacing) * 8);
}
.mt-\[8px\] {
    margin-top: 8px;
}
.mr-4 {
    margin-right: calc(var(--spacing) * 4);
}
.mr-8 {
    margin-right: calc(var(--spacing) * 8);
}
.mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
}
.mb-\[1px\] {
    margin-bottom: 1px;
}
.ml-auto {
    margin-left: auto;
}
.block {
    display: block;
}
.contents {
    display: contents;
}
.flex {
    display: flex;
}
.flow-root {
    display: flow-root;
}
.grid {
    display: grid;
}
.hidden {
    display: none;
}
.inline {
    display: inline;
}
.inline-block {
    display: inline-block;
}
.inline-flex {
    display: inline-flex;
}
.inline-grid {
    display: inline-grid;
}
.inline-table {
    display: inline-table;
}
.list-item {
    display: list-item;
}
.table {
    display: table;
}
.table-caption {
    display: table-caption;
}
.table-cell {
    display: table-cell;
}
.table-column {
    display: table-column;
}
.table-column-group {
    display: table-column-group;
}
.table-footer-group {
    display: table-footer-group;
}
.table-header-group {
    display: table-header-group;
}
.table-row {
    display: table-row;
}
.table-row-group {
    display: table-row-group;
}
.field-sizing-content {
    field-sizing: content;
}
.aspect-square {
    aspect-ratio: 1;
}
.size-2 {
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
}
.size-2\.5 {
    width: calc(var(--spacing) * 2.5);
    height: calc(var(--spacing) * 2.5);
}
.size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
}
.size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
}
.size-5 {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
}
.size-6 {
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
}
.size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
}
.size-10 {
    width: calc(var(--spacing) * 10);
    height: calc(var(--spacing) * 10);
}
.size-full {
    width: 100%;
    height: 100%;
}
.h-1\/2 {
    height: 50%;
}
.h-4 {
    height: calc(var(--spacing) * 4);
}
.h-5 {
    height: calc(var(--spacing) * 5);
}
.h-8 {
    height: calc(var(--spacing) * 8);
}
.h-9 {
    height: calc(var(--spacing) * 9);
}
.h-10 {
    height: calc(var(--spacing) * 10);
}
.h-14 {
    height: calc(var(--spacing) * 14);
}
.h-\[4rem\] {
    height: 4rem;
}
.h-\[15rem\] {
    height: 15rem;
}
.h-\[40px\] {
    height: 40px;
}
.h-\[48px\] {
    height: 48px;
}
.h-\[var\(--reka-select-trigger-height\)\] {
    height: var(--reka-select-trigger-height);
}
.h-full {
    height: 100%;
}
.h-px {
    height: 1px;
}
.h-screen {
    height: 100vh;
}
.max-h-\(--reka-context-menu-content-available-height\) {
    max-height: var(--reka-context-menu-content-available-height);
}
.max-h-\(--reka-select-content-available-height\) {
    max-height: var(--reka-select-content-available-height);
}
.min-h-16 {
    min-height: calc(var(--spacing) * 16);
}
.w-3 {
    width: calc(var(--spacing) * 3);
}
.w-3\/4 {
    width: 75%;
}
.w-4 {
    width: calc(var(--spacing) * 4);
}
.w-26 {
    width: calc(var(--spacing) * 26);
}
.w-44 {
    width: calc(var(--spacing) * 44);
}
.w-52 {
    width: calc(var(--spacing) * 52);
}
.w-\[10rem\] {
    width: 10rem;
}
.w-\[15rem\] {
    width: 15rem;
}
.w-fit {
    width: fit-content;
}
.w-full {
    width: 100%;
}
.w-px {
    width: 1px;
}
.w-screen {
    width: 100vw;
}
.max-w-\[22rem\] {
    max-width: 22rem;
}
.max-w-\[calc\(100\%-2rem\)\] {
    max-width: calc(100% - 2rem);
}
.max-w-lg {
    max-width: var(--container-lg);
}
.min-w-0 {
    min-width: calc(var(--spacing) * 0);
}
.min-w-\[8rem\] {
    min-width: 8rem;
}
.min-w-\[13rem\] {
    min-width: 13rem;
}
.min-w-\[var\(--reka-select-trigger-width\)\] {
    min-width: var(--reka-select-trigger-width);
}
.flex-\[12rem\] {
    flex: 12rem;
}
.shrink {
    flex-shrink: 1;
}
.shrink-0 {
    flex-shrink: 0;
}
.grow {
    flex-grow: 1;
}
.grow-0 {
    flex-grow: 0;
}
.border-collapse {
    border-collapse: collapse;
}
.origin-\(--reka-context-menu-content-transform-origin\) {
    transform-origin: var(--reka-context-menu-content-transform-origin);
}
.translate-\[-50\%\] {
    --tw-translate-x: -50%;
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
}
.-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
}
.translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
}
.-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
}
.translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
}
.translate-none {
    translate: none;
}
.scale-3d {
    scale: var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z);
}
.rotate-270 {
    rotate: 270deg;
}
.transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
}
.transform\! {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, ) !important;
}
.cursor-default {
    cursor: default;
}
.cursor-not-allowed {
    cursor: not-allowed;
}
.cursor-pointer {
    cursor: pointer;
}
.touch-pinch-zoom {
    --tw-pinch-zoom: pinch-zoom;
    touch-action: var(--tw-pan-x, ) var(--tw-pan-y, ) var(--tw-pinch-zoom, );
}
.resize {
    resize: both;
}
.resize-none {
    resize: none;
}
.scroll-my-1 {
    scroll-margin-block: calc(var(--spacing) * 1);
}
.list-none {
    list-style-type: none;
}
.flex-col {
    flex-direction: column;
}
.flex-col-reverse {
    flex-direction: column-reverse;
}
.flex-wrap {
    flex-wrap: wrap;
}
.place-items-center {
    place-items: center;
}
.items-center {
    align-items: center;
}
.justify-between {
    justify-content: space-between;
}
.justify-center {
    justify-content: center;
}
.justify-start {
    justify-content: flex-start;
}
.gap-1 {
    gap: calc(var(--spacing) * 1);
}
.gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
}
.gap-2 {
    gap: calc(var(--spacing) * 2);
}
.gap-3 {
    gap: calc(var(--spacing) * 3);
}
.gap-4 {
    gap: calc(var(--spacing) * 4);
}
.gap-6 {
    gap: calc(var(--spacing) * 6);
}
.gap-\[2px\] {
    gap: 2px;
}
.gap-\[8px\] {
    gap: 8px;
}
:where(.space-y-reverse > :not(:last-child)) {
    --tw-space-y-reverse: 1;
}
:where(.space-x-reverse > :not(:last-child)) {
    --tw-space-x-reverse: 1;
}
:where(.divide-x > :not(:last-child)) {
    --tw-divide-x-reverse: 0;
    border-inline-style: var(--tw-border-style);
    border-inline-start-width: calc(1px * var(--tw-divide-x-reverse));
    border-inline-end-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
}
:where(.divide-y > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
}
:where(.divide-y-reverse > :not(:last-child)) {
    --tw-divide-y-reverse: 1;
}
.self-center {
    align-self: center;
}
.truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
.overflow-hidden {
    overflow: hidden;
}
.overflow-x-hidden {
    overflow-x: hidden;
}
.overflow-y-auto {
    overflow-y: auto;
}
.rounded-\[4px\] {
    border-radius: 4px;
}
.rounded-full {
    border-radius: 3.40282e38px;
}
.rounded-lg {
    border-radius: var(--radius);
}
.rounded-md {
    border-radius: calc(var(--radius)  - 2px);
}
.rounded-sm {
    border-radius: calc(var(--radius)  - 4px);
}
.rounded-xs {
    border-radius: var(--radius-xs);
}
.rounded-s {
    border-start-start-radius: .25rem;
    border-end-start-radius: .25rem;
}
.rounded-ss {
    border-start-start-radius: .25rem;
}
.rounded-e {
    border-start-end-radius: .25rem;
    border-end-end-radius: .25rem;
}
.rounded-se {
    border-start-end-radius: .25rem;
}
.rounded-ee {
    border-end-end-radius: .25rem;
}
.rounded-es {
    border-end-start-radius: .25rem;
}
.rounded-t {
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem;
}
.rounded-l {
    border-top-left-radius: .25rem;
    border-bottom-left-radius: .25rem;
}
.rounded-tl {
    border-top-left-radius: .25rem;
}
.rounded-r {
    border-top-right-radius: .25rem;
    border-bottom-right-radius: .25rem;
}
.rounded-r-sm {
    border-top-right-radius: calc(var(--radius)  - 4px);
    border-bottom-right-radius: calc(var(--radius)  - 4px);
}
.rounded-tr {
    border-top-right-radius: .25rem;
}
.rounded-b {
    border-bottom-right-radius: .25rem;
    border-bottom-left-radius: .25rem;
}
.rounded-br {
    border-bottom-right-radius: .25rem;
}
.rounded-bl {
    border-bottom-left-radius: .25rem;
}
.border {
    border-style: var(--tw-border-style);
    border-width: 1px;
}
.border-x {
    border-inline-style: var(--tw-border-style);
    border-inline-width: 1px;
}
.border-y {
    border-block-style: var(--tw-border-style);
    border-block-width: 1px;
}
.border-s {
    border-inline-start-style: var(--tw-border-style);
    border-inline-start-width: 1px;
}
.border-e {
    border-inline-end-style: var(--tw-border-style);
    border-inline-end-width: 1px;
}
.border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
}
.border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
}
.border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
}
.border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
}
.border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
}
.border-border {
    border-color: var(--border);
}
.border-input {
    border-color: var(--input);
}
.border-primary {
    border-color: var(--primary);
}
.border-transparent {
    border-color: #0000;
}
.bg-\[\#808080\] {
    background-color: gray;
}
.bg-active {
    background-color: var(--active);
}
.bg-background {
    background-color: var(--background);
}
.bg-black {
    background-color: var(--color-black);
}
.bg-black\/80 {
    background-color: #000c;
}
@supports (color: color-mix(in lab, red, red)) {
.bg-black\/80 {
      background-color: color-mix(in oklab, var(--color-black) 80%, transparent);
}
}
.bg-border {
    background-color: var(--border);
}
.bg-content {
    background-color: var(--content);
}
.bg-destructive {
    background-color: var(--destructive);
}
.bg-popover {
    background-color: var(--popover);
}
.bg-primary {
    background-color: var(--primary);
}
.bg-secondary {
    background-color: var(--secondary);
}
.bg-side {
    background-color: var(--side);
}
.bg-title {
    background-color: var(--title);
}
.bg-transparent {
    background-color: #0000;
}
.bg-white {
    background-color: var(--color-white);
}
.bg-repeat {
    background-repeat: repeat;
}
.mask-no-clip {
    -webkit-mask-clip: no-clip;
    mask-clip: no-clip;
}
.mask-repeat {
    -webkit-mask-repeat: repeat;
    mask-repeat: repeat;
}
.fill-current {
    fill: currentColor;
}
.fill-none {
    fill: none;
}
.fill-primary {
    fill: var(--primary);
}
.stroke-border {
    stroke: var(--border);
}
.stroke-primary {
    stroke: var(--primary);
}
.stroke-4 {
    stroke-width: 4px;
}
.p-0 {
    padding: calc(var(--spacing) * 0);
}
.p-0\.5 {
    padding: calc(var(--spacing) * .5);
}
.p-1 {
    padding: calc(var(--spacing) * 1);
}
.p-3 {
    padding: calc(var(--spacing) * 3);
}
.p-4 {
    padding: calc(var(--spacing) * 4);
}
.p-6 {
    padding: calc(var(--spacing) * 6);
}
.px-2 {
    padding-inline: calc(var(--spacing) * 2);
}
.px-3 {
    padding-inline: calc(var(--spacing) * 3);
}
.px-4 {
    padding-inline: calc(var(--spacing) * 4);
}
.px-6 {
    padding-inline: calc(var(--spacing) * 6);
}
.px-\[8px\] {
    padding-inline: 8px;
}
.px-\[12px\] {
    padding-inline: 12px;
}
.px-\[16px\] {
    padding-inline: 16px;
}
.py-1 {
    padding-block: calc(var(--spacing) * 1);
}
.py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
}
.py-2 {
    padding-block: calc(var(--spacing) * 2);
}
.py-16 {
    padding-block: calc(var(--spacing) * 16);
}
.py-\[4px\] {
    padding-block: 4px;
}
.pt-8 {
    padding-top: calc(var(--spacing) * 8);
}
.pr-2 {
    padding-right: calc(var(--spacing) * 2);
}
.pr-4 {
    padding-right: calc(var(--spacing) * 4);
}
.pr-6 {
    padding-right: calc(var(--spacing) * 6);
}
.pr-8 {
    padding-right: calc(var(--spacing) * 8);
}
.pr-10 {
    padding-right: calc(var(--spacing) * 10);
}
.pl-2 {
    padding-left: calc(var(--spacing) * 2);
}
.pl-4 {
    padding-left: calc(var(--spacing) * 4);
}
.pl-6 {
    padding-left: calc(var(--spacing) * 6);
}
.pl-7 {
    padding-left: calc(var(--spacing) * 7);
}
.pl-8 {
    padding-left: calc(var(--spacing) * 8);
}
.pl-12 {
    padding-left: calc(var(--spacing) * 12);
}
.text-center {
    text-align: center;
}
.text-end {
    text-align: end;
}
.align-middle {
    vertical-align: middle;
}
.font-mono {
    font-family: var(--font-mono);
}
.text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
}
.text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
}
.text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
}
.text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
}
.text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
}
.leading-9 {
    --tw-leading: calc(var(--spacing) * 9);
    line-height: calc(var(--spacing) * 9);
}
.leading-\[1\] {
    --tw-leading: 1;
    line-height: 1;
}
.leading-\[18px\] {
    --tw-leading: 18px;
    line-height: 18px;
}
.leading-none {
    --tw-leading: 1;
    line-height: 1;
}
.font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
}
.font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
}
.tracking-widest {
    --tw-tracking: var(--tracking-widest);
    letter-spacing: var(--tracking-widest);
}
.text-wrap {
    text-wrap: wrap;
}
.text-clip {
    text-overflow: clip;
}
.text-ellipsis {
    text-overflow: ellipsis;
}
.whitespace-nowrap {
    white-space: nowrap;
}
.text-current {
    color: currentColor;
}
.text-foreground {
    color: var(--foreground);
}
.text-icon {
    color: var(--icon);
}
.text-muted-foreground {
    color: var(--muted-foreground);
}
.text-popover-foreground {
    color: var(--popover-foreground);
}
.text-primary {
    color: var(--primary);
}
.text-primary-foreground {
    color: var(--primary-foreground);
}
.text-secondary-foreground {
    color: var(--secondary-foreground);
}
.text-white {
    color: var(--color-white);
}
.capitalize {
    text-transform: capitalize;
}
.lowercase {
    text-transform: lowercase;
}
.normal-case {
    text-transform: none;
}
.uppercase {
    text-transform: uppercase;
}
.italic {
    font-style: italic;
}
.not-italic {
    font-style: normal;
}
.diagonal-fractions {
    --tw-numeric-fraction: diagonal-fractions;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
}
.lining-nums {
    --tw-numeric-figure: lining-nums;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
}
.oldstyle-nums {
    --tw-numeric-figure: oldstyle-nums;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
}
.ordinal {
    --tw-ordinal: ordinal;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
}
.proportional-nums {
    --tw-numeric-spacing: proportional-nums;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
}
.slashed-zero {
    --tw-slashed-zero: slashed-zero;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
}
.stacked-fractions {
    --tw-numeric-fraction: stacked-fractions;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
}
.tabular-nums {
    --tw-numeric-spacing: tabular-nums;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
}
.normal-nums {
    font-variant-numeric: normal;
}
.line-through {
    text-decoration-line: line-through;
}
.no-underline {
    text-decoration-line: none;
}
.overline {
    text-decoration-line: overline;
}
.underline {
    text-decoration-line: underline;
}
.underline-offset-4 {
    text-underline-offset: 4px;
}
.antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.subpixel-antialiased {
    -webkit-font-smoothing: auto;
    -moz-osx-font-smoothing: auto;
}
.opacity-0 {
    opacity: 0;
}
.opacity-50 {
    opacity: .5;
}
.opacity-70 {
    opacity: .7;
}
.shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.inset-ring {
    --tw-inset-ring-shadow: inset 0 0 0 1px var(--tw-inset-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.ring-offset-background {
    --tw-ring-offset-color: var(--background);
}
.outline-hidden {
    --tw-outline-style: none;
    outline-style: none;
}
@media (forced-colors: active) {
.outline-hidden {
      outline-offset: 2px;
      outline: 2px solid #0000;
}
}
.outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
}
.blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
}
.drop-shadow {
    --tw-drop-shadow-size: drop-shadow(0 1px 2px var(--tw-drop-shadow-color, #0000001a)) drop-shadow(0 1px 1px var(--tw-drop-shadow-color, #0000000f));
    --tw-drop-shadow: drop-shadow(0 1px 2px #0000001a) drop-shadow(0 1px 1px #0000000f);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
}
.filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
}
.filter\! {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, ) !important;
}
.backdrop-blur {
    --tw-backdrop-blur: blur(8px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
}
.backdrop-grayscale {
    --tw-backdrop-grayscale: grayscale(100%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
}
.backdrop-invert {
    --tw-backdrop-invert: invert(100%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
}
.backdrop-sepia {
    --tw-backdrop-sepia: sepia(100%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
}
.backdrop-filter {
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
}
.transition-\[color\,box-shadow\] {
    transition-property: color, box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.transition-none {
    transition-property: none;
}
.duration-50 {
    --tw-duration: 50ms;
    transition-duration: 50ms;
}
.duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
}
.outline-none {
    --tw-outline-style: none;
    outline-style: none;
}
.select-none {
    -webkit-user-select: none;
    user-select: none;
}
:where(.divide-x-reverse > :not(:last-child)) {
    --tw-divide-x-reverse: 1;
}
.paused {
    animation-play-state: paused;
}
.ring-inset {
    --tw-ring-inset: inset;
}
.running {
    animation-play-state: running;
}
.zoom-in {
    --tw-enter-scale: 0;
}
.zoom-out {
    --tw-exit-scale: 0;
}
.not-first\:mt-\[-16px\]:not(:first-child) {
    margin-top: -16px;
}
@media (hover: hover) {
.group-hover\:opacity-100:is(:where(.group):hover *) {
      opacity: 1;
}
}
.group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled="true"] *) {
    pointer-events: none;
}
.group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled="true"] *) {
    opacity: .5;
}
.peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
    cursor: not-allowed;
}
.peer-disabled\:opacity-50:is(:where(.peer):disabled ~ *) {
    opacity: .5;
}
.selection\:bg-primary ::selection {
    background-color: var(--primary);
}
.selection\:bg-primary::selection {
    background-color: var(--primary);
}
.selection\:text-primary-foreground ::selection {
    color: var(--primary-foreground);
}
.selection\:text-primary-foreground::selection {
    color: var(--primary-foreground);
}
.file\:inline-flex::file-selector-button {
    display: inline-flex;
}
.file\:h-7::file-selector-button {
    height: calc(var(--spacing) * 7);
}
.file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
}
.file\:bg-transparent::file-selector-button {
    background-color: #0000;
}
.file\:text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
}
.file\:font-medium::file-selector-button {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
}
.file\:text-foreground::file-selector-button {
    color: var(--foreground);
}
.placeholder\:text-muted-foreground::placeholder {
    color: var(--muted-foreground);
}
.after\:absolute:after {
    content: var(--tw-content);
    position: absolute;
}
.after\:inset-y-0:after {
    content: var(--tw-content);
    inset-block: calc(var(--spacing) * 0);
}
.after\:left-1\/2:after {
    content: var(--tw-content);
    left: 50%;
}
.after\:w-1:after {
    content: var(--tw-content);
    width: calc(var(--spacing) * 1);
}
.after\:-translate-x-1\/2:after {
    content: var(--tw-content);
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
}
@media (hover: hover) {
.hover\:scale-105:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
}
.hover\:bg-accent:hover {
      background-color: var(--accent);
}
.hover\:bg-secondary:hover {
      background-color: var(--secondary);
}
.hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
}
.hover\:text-primary:hover {
      color: var(--primary);
}
.hover\:opacity-100:hover {
      opacity: 1;
}
}
.focus\:border-ring:focus {
    border-color: var(--ring);
}
.focus\:bg-accent:focus {
    background-color: var(--accent);
}
.focus\:text-accent-foreground:focus {
    color: var(--accent-foreground);
}
.focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.focus\:ring-\[3px\]:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.focus\:ring-ring:focus, .focus\:ring-ring\/50:focus {
    --tw-ring-color: var(--ring);
}
@supports (color: color-mix(in lab, red, red)) {
.focus\:ring-ring\/50:focus {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
}
}
.focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
}
.focus\:outline-hidden:focus {
    --tw-outline-style: none;
    outline-style: none;
}
@media (forced-colors: active) {
.focus\:outline-hidden:focus {
      outline-offset: 2px;
      outline: 2px solid #0000;
}
}
.focus-visible\:border-ring:focus-visible {
    border-color: var(--ring);
}
.focus-visible\:ring-1:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.focus-visible\:ring-\[3px\]:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.focus-visible\:ring-ring:focus-visible, .focus-visible\:ring-ring\/50:focus-visible {
    --tw-ring-color: var(--ring);
}
@supports (color: color-mix(in lab, red, red)) {
.focus-visible\:ring-ring\/50:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
}
}
.focus-visible\:ring-offset-1:focus-visible {
    --tw-ring-offset-width: 1px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
}
.focus-visible\:outline-hidden:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
}
@media (forced-colors: active) {
.focus-visible\:outline-hidden:focus-visible {
      outline-offset: 2px;
      outline: 2px solid #0000;
}
}
.focus-visible\:outline-none:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
}
.active\:scale-100:active {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
}
.disabled\:pointer-events-none:disabled {
    pointer-events: none;
}
.disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
}
.disabled\:opacity-20:disabled {
    opacity: .2;
}
.disabled\:opacity-50:disabled {
    opacity: .5;
}
.has-\[\>svg\]\:px-2\.5:has( > svg) {
    padding-inline: calc(var(--spacing) * 2.5);
}
.has-\[\>svg\]\:px-3:has( > svg) {
    padding-inline: calc(var(--spacing) * 3);
}
.has-\[\>svg\]\:px-4:has( > svg) {
    padding-inline: calc(var(--spacing) * 4);
}
.aria-invalid\:border-destructive[aria-invalid="true"] {
    border-color: var(--destructive);
}
.aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
}
@supports (color: color-mix(in lab, red, red)) {
.aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
}
}
.data-\[disabled\]\:pointer-events-none[data-disabled] {
    pointer-events: none;
}
.data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: .5;
}
.data-\[disabled\=true\]\:cursor-not-allowed[data-disabled="true"] {
    cursor: not-allowed;
}
.data-\[disabled\=true\]\:opacity-50[data-disabled="true"] {
    opacity: .5;
}
.data-\[inset\]\:pl-8[data-inset] {
    padding-left: calc(var(--spacing) * 8);
}
.data-\[orientation\=vertical\]\:h-px[data-orientation="vertical"] {
    height: 1px;
}
.data-\[orientation\=vertical\]\:w-full[data-orientation="vertical"] {
    width: 100%;
}
.data-\[orientation\=vertical\]\:flex-col[data-orientation="vertical"] {
    flex-direction: column;
}
.data-\[orientation\=vertical\]\:after\:left-0[data-orientation="vertical"]:after {
    content: var(--tw-content);
    left: calc(var(--spacing) * 0);
}
.data-\[orientation\=vertical\]\:after\:h-1[data-orientation="vertical"]:after {
    content: var(--tw-content);
    height: calc(var(--spacing) * 1);
}
.data-\[orientation\=vertical\]\:after\:w-full[data-orientation="vertical"]:after {
    content: var(--tw-content);
    width: 100%;
}
.data-\[orientation\=vertical\]\:after\:translate-x-0[data-orientation="vertical"]:after {
    content: var(--tw-content);
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
}
.data-\[orientation\=vertical\]\:after\:-translate-y-1\/2[data-orientation="vertical"]:after {
    content: var(--tw-content);
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
}
.data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
    color: var(--muted-foreground);
}
.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
    --tw-translate-y: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
}
.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
    --tw-enter-translate-y: calc(2 * var(--spacing) * -1);
}
.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
    --tw-translate-x: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
}
.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
    --tw-enter-translate-x: calc(2 * var(--spacing));
}
.data-\[side\=right\]\:translate-x-1[data-side="right"] {
    --tw-translate-x: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
}
.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
    --tw-enter-translate-x: calc(2 * var(--spacing) * -1);
}
.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
}
.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
    --tw-enter-translate-y: calc(2 * var(--spacing));
}
.data-\[size\=default\]\:h-9[data-size="default"] {
    height: calc(var(--spacing) * 9);
}
.data-\[size\=sm\]\:h-8[data-size="sm"] {
    height: calc(var(--spacing) * 8);
}
:is(.\*\:data-\[slot\=select-value\]\:line-clamp-1 > *)[data-slot="select-value"] {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
}
:is(.\*\:data-\[slot\=select-value\]\:flex > *)[data-slot="select-value"] {
    display: flex;
}
:is(.\*\:data-\[slot\=select-value\]\:items-center > *)[data-slot="select-value"] {
    align-items: center;
}
:is(.\*\:data-\[slot\=select-value\]\:gap-2 > *)[data-slot="select-value"] {
    gap: calc(var(--spacing) * 2);
}
.data-\[state\=checked\]\:border-primary[data-state="checked"] {
    border-color: var(--primary);
}
.data-\[state\=checked\]\:bg-primary[data-state="checked"] {
    background-color: var(--primary);
}
.data-\[state\=checked\]\:bg-primary-foreground[data-state="checked"] {
    background-color: var(--primary-foreground);
}
.data-\[state\=checked\]\:text-primary[data-state="checked"] {
    color: var(--primary);
}
.data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
    color: var(--primary-foreground);
}
.data-\[state\=closed\]\:animate-out[data-state="closed"] {
    animation: exit var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
}
.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
    --tw-exit-opacity: 0;
}
.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
    --tw-exit-scale: .95;
}
.data-\[state\=open\]\:animate-in[data-state="open"] {
    animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
}
.data-\[state\=open\]\:bg-accent[data-state="open"] {
    background-color: var(--accent);
}
.data-\[state\=open\]\:text-accent-foreground[data-state="open"] {
    color: var(--accent-foreground);
}
.data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
    color: var(--muted-foreground);
}
.data-\[state\=open\]\:fade-in-0[data-state="open"] {
    --tw-enter-opacity: 0;
}
.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
    --tw-enter-scale: .95;
}
.data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
    background-color: var(--destructive);
}
@supports (color: color-mix(in lab, red, red)) {
.data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
      background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
}
}
@media (min-width: 40rem) {
.sm\:max-w-lg {
      max-width: var(--container-lg);
}
.sm\:flex-row {
      flex-direction: row;
}
.sm\:justify-end {
      justify-content: flex-end;
}
.sm\:rounded-lg {
      border-radius: var(--radius);
}
.sm\:text-left {
      text-align: left;
}
}
@media (min-width: 48rem) {
.md\:block {
      display: block;
}
.md\:size-4 {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
}
.md\:w-full {
      width: 100%;
}
.md\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
}
}
.dark\:border-input:is(.dark *) {
    border-color: var(--input);
}
.dark\:bg-destructive\/60:is(.dark *) {
    background-color: var(--destructive);
}
@supports (color: color-mix(in lab, red, red)) {
.dark\:bg-destructive\/60:is(.dark *) {
      background-color: color-mix(in oklab, var(--destructive) 60%, transparent);
}
}
.dark\:bg-input\/30:is(.dark *) {
    background-color: var(--input);
}
@supports (color: color-mix(in lab, red, red)) {
.dark\:bg-input\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
}
}
@media (hover: hover) {
.dark\:hover\:bg-accent\/40:is(.dark *):hover {
      background-color: var(--accent);
}
@supports (color: color-mix(in lab, red, red)) {
.dark\:hover\:bg-accent\/40:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--accent) 40%, transparent);
}
}
.dark\:hover\:bg-input\/50:is(.dark *):hover {
      background-color: var(--input);
}
@supports (color: color-mix(in lab, red, red)) {
.dark\:hover\:bg-input\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--input) 50%, transparent);
}
}
}
.dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
}
@supports (color: color-mix(in lab, red, red)) {
.dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
}
}
.dark\:data-\[state\=checked\]\:bg-primary-foreground:is(.dark *)[data-state="checked"] {
    background-color: var(--primary-foreground);
}
.dark\:data-\[state\=checked\]\:text-primary:is(.dark *)[data-state="checked"] {
    color: var(--primary);
}
.dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/40:is(.dark *)[data-variant="destructive"]:focus {
    background-color: var(--destructive);
}
@supports (color: color-mix(in lab, red, red)) {
.dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/40:is(.dark *)[data-variant="destructive"]:focus {
      background-color: color-mix(in oklab, var(--destructive) 40%, transparent);
}
}
.\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
}
.\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
}
.\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
}
.\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*="text-"]) {
    color: var(--muted-foreground);
}
:is(.\*\:\[span\]\:last\:flex > *):is(span):last-child {
    display: flex;
}
:is(.\*\:\[span\]\:last\:items-center > *):is(span):last-child {
    align-items: center;
}
:is(.\*\:\[span\]\:last\:gap-2 > *):is(span):last-child {
    gap: calc(var(--spacing) * 2);
}
@media (hover: hover) {
.\[\&\:not\(\[data-disabled\=true\]\)\]\:hover\:bg-accent:not([data-disabled="true"]):hover {
      background-color: var(--accent);
}
.\[\&\:not\(\[data-disabled\=true\]\)\]\:hover\:bg-destructive\/90:not([data-disabled="true"]):hover {
      background-color: var(--destructive);
}
@supports (color: color-mix(in lab, red, red)) {
.\[\&\:not\(\[data-disabled\=true\]\)\]\:hover\:bg-destructive\/90:not([data-disabled="true"]):hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
}
}
.\[\&\:not\(\[data-disabled\=true\]\)\]\:hover\:bg-primary\/90:not([data-disabled="true"]):hover {
      background-color: var(--primary);
}
@supports (color: color-mix(in lab, red, red)) {
.\[\&\:not\(\[data-disabled\=true\]\)\]\:hover\:bg-primary\/90:not([data-disabled="true"]):hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
}
}
.\[\&\:not\(\[data-disabled\=true\]\)\]\:hover\:bg-secondary\/80:not([data-disabled="true"]):hover {
      background-color: var(--secondary);
}
@supports (color: color-mix(in lab, red, red)) {
.\[\&\:not\(\[data-disabled\=true\]\)\]\:hover\:bg-secondary\/80:not([data-disabled="true"]):hover {
        background-color: color-mix(in oklab, var(--secondary) 80%, transparent);
}
}
.\[\&\:not\(\[data-disabled\=true\]\)\]\:hover\:text-accent-foreground:not([data-disabled="true"]):hover {
      color: var(--accent-foreground);
}
.\[\&\:not\(\[data-disabled\=true\]\)\]\:hover\:underline:not([data-disabled="true"]):hover {
      text-decoration-line: underline;
}
}
.\[\&\:not\(\[data-disabled\=true\]\)\]\:focus-visible\:ring-destructive\/20:not([data-disabled="true"]):focus-visible {
    --tw-ring-color: var(--destructive);
}
@supports (color: color-mix(in lab, red, red)) {
.\[\&\:not\(\[data-disabled\=true\]\)\]\:focus-visible\:ring-destructive\/20:not([data-disabled="true"]):focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
}
}
@media (hover: hover) {
.dark\:\[\&\:not\(\[data-disabled\=true\]\)\]\:hover\:bg-accent\/50:is(.dark *):not([data-disabled="true"]):hover {
      background-color: var(--accent);
}
@supports (color: color-mix(in lab, red, red)) {
.dark\:\[\&\:not\(\[data-disabled\=true\]\)\]\:hover\:bg-accent\/50:is(.dark *):not([data-disabled="true"]):hover {
        background-color: color-mix(in oklab, var(--accent) 50%, transparent);
}
}
.dark\:\[\&\:not\(\[data-disabled\=true\]\)\]\:hover\:bg-input\/50:is(.dark *):not([data-disabled="true"]):hover {
      background-color: var(--input);
}
@supports (color: color-mix(in lab, red, red)) {
.dark\:\[\&\:not\(\[data-disabled\=true\]\)\]\:hover\:bg-input\/50:is(.dark *):not([data-disabled="true"]):hover {
        background-color: color-mix(in oklab, var(--input) 50%, transparent);
}
}
}
.dark\:\[\&\:not\(\[data-disabled\=true\]\)\]\:focus-visible\:ring-destructive\/40:is(.dark *):not([data-disabled="true"]):focus-visible {
    --tw-ring-color: var(--destructive);
}
@supports (color: color-mix(in lab, red, red)) {
.dark\:\[\&\:not\(\[data-disabled\=true\]\)\]\:focus-visible\:ring-destructive\/40:is(.dark *):not([data-disabled="true"]):focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
}
}
.\[\&\>\[data-slot\=input\]\]\:has-\[\[data-slot\=decrement\]\]\:pl-5 > [data-slot="input"]:has([data-slot="decrement"]) {
    padding-left: calc(var(--spacing) * 5);
}
.\[\&\>\[data-slot\=input\]\]\:has-\[\[data-slot\=increment\]\]\:pr-5 > [data-slot="input"]:has([data-slot="increment"]) {
    padding-right: calc(var(--spacing) * 5);
}
.\[\&\[data-orientation\=vertical\]\>div\]\:rotate-90[data-orientation="vertical"] > div {
    rotate: 90deg;
}
}
@property --tw-animation-delay {
  syntax: "*";
  inherits: false;
  initial-value: 0s;
}
@property --tw-animation-direction {
  syntax: "*";
  inherits: false;
  initial-value: normal;
}
@property --tw-animation-duration {
  syntax: "*";
  inherits: false
}
@property --tw-animation-fill-mode {
  syntax: "*";
  inherits: false;
  initial-value: none;
}
@property --tw-animation-iteration-count {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-enter-blur {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-enter-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-enter-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-enter-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-enter-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-enter-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-exit-blur {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-exit-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-exit-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-exit-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-exit-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-exit-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
:root {
  --radius: .625rem;
  --background: oklch(100% 0 0);
  --foreground: oklch(14.5% 0 0);
  --card: oklch(100% 0 0);
  --card-foreground: oklch(14.5% 0 0);
  --popover: oklch(100% 0 0);
  --popover-foreground: oklch(14.5% 0 0);
  --primary: #ca3033;
  --primary-foreground: oklch(100% 0 0);
  --secondary: oklch(97% 0 0);
  --secondary-foreground: oklch(20.5% 0 0);
  --muted: oklch(97% 0 0);
  --muted-foreground: oklch(55.6% 0 0);
  --accent: oklch(97% 0 0);
  --accent-foreground: oklch(20.5% 0 0);
  --destructive: oklch(57.7% .245 27.325);
  --border: oklch(92.2% 0 0);
  --input: oklch(92.2% 0 0);
  --ring: oklch(62.92% .1929 21.19 / .6619);
  --chart-1: oklch(64.6% .222 41.116);
  --chart-2: oklch(60% .118 184.704);
  --chart-3: oklch(39.8% .07 227.392);
  --chart-4: oklch(82.8% .189 84.429);
  --chart-5: oklch(76.9% .188 70.08);
  --sidebar: oklch(98.5% 0 0);
  --sidebar-foreground: oklch(14.5% 0 0);
  --sidebar-primary: oklch(20.5% 0 0);
  --sidebar-primary-foreground: oklch(98.5% 0 0);
  --sidebar-accent: oklch(97% 0 0);
  --sidebar-accent-foreground: oklch(20.5% 0 0);
  --sidebar-border: oklch(92.2% 0 0);
  --sidebar-ring: oklch(70.8% 0 0);
  --active: oklch(92% 0 0);
  --side: oklch(100% 0 0);
  --title: oklch(100% 0 0);
  --content: oklch(100% 0 0);
  --icon: oklch(62.92% .1929 21.19);
  font-size: 14px;
}
.dark {
  --background: oklch(22.5% 0 0);
  --foreground: oklch(98.5% 0 0);
  --card: oklch(20.5% 0 0);
  --card-foreground: oklch(98.5% 0 0);
  --popover: #2c2c2c;
  --popover-foreground: oklch(98.5% 0 0);
  --primary: #ca3033;
  --primary-foreground: oklch(100% 0 0);
  --secondary: oklch(26.9% 0 0);
  --secondary-foreground: oklch(98.5% 0 0);
  --muted: oklch(26.9% 0 0);
  --muted-foreground: oklch(70.8% 0 0);
  --accent: oklch(36.9% 0 0);
  --accent-foreground: oklch(98.5% 0 0);
  --destructive: oklch(70.4% .191 22.216);
  --border: oklch(100% 0 0 / .1);
  --input: oklch(100% 0 0 / .15);
  --ring: oklch(62.92% .1929 21.19 / .6619);
  --chart-1: oklch(48.8% .243 264.376);
  --chart-2: oklch(69.6% .17 162.48);
  --chart-3: oklch(76.9% .188 70.08);
  --chart-4: oklch(62.7% .265 303.9);
  --chart-5: oklch(64.5% .246 16.439);
  --sidebar: oklch(20.5% 0 0);
  --sidebar-foreground: oklch(98.5% 0 0);
  --sidebar-primary: oklch(48.8% .243 264.376);
  --sidebar-primary-foreground: oklch(98.5% 0 0);
  --sidebar-accent: oklch(26.9% 0 0);
  --sidebar-accent-foreground: oklch(98.5% 0 0);
  --sidebar-border: oklch(100% 0 0 / .1);
  --sidebar-ring: oklch(55.6% 0 0);
  --active: oklch(39% 0 0);
  --side: oklch(29.52% 0 0);
  --title: oklch(23.21% 0 0);
  --content: oklch(26.5% 0 0);
  --icon: oklch(70.9% .185055 16.866);
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false
}
@property --tw-pan-x {
  syntax: "*";
  inherits: false
}
@property --tw-pan-y {
  syntax: "*";
  inherits: false
}
@property --tw-pinch-zoom {
  syntax: "*";
  inherits: false
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-leading {
  syntax: "*";
  inherits: false
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false
}
@property --tw-tracking {
  syntax: "*";
  inherits: false
}
@property --tw-ordinal {
  syntax: "*";
  inherits: false
}
@property --tw-slashed-zero {
  syntax: "*";
  inherits: false
}
@property --tw-numeric-figure {
  syntax: "*";
  inherits: false
}
@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false
}
@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false
}
@property --tw-brightness {
  syntax: "*";
  inherits: false
}
@property --tw-contrast {
  syntax: "*";
  inherits: false
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}
@property --tw-invert {
  syntax: "*";
  inherits: false
}
@property --tw-opacity {
  syntax: "*";
  inherits: false
}
@property --tw-saturate {
  syntax: "*";
  inherits: false
}
@property --tw-sepia {
  syntax: "*";
  inherits: false
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}
@property --tw-duration {
  syntax: "*";
  inherits: false
}
@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}
@keyframes enter {
from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
    filter: blur(var(--tw-enter-blur, 0));
}
}
@keyframes exit {
to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
    filter: blur(var(--tw-exit-blur, 0));
}
}

import { defineComponent, ref, createElementBlock, openBlock, normalizeClass, unref, createVNode, createElementVNode, withCtx, createTextVNode, toDisplayString, computed, createBlock, createCommentVNode, shallowRef, provide, watch, KeepAlive, Suspense, resolveDynamicComponent, Fragment } from "vue";
import { getManager, RenderManager, RenderEvent, ContextMenuManager } from "@siren/core";
import { useTaskStore, useContextMenuStore } from "@siren/core/store";
import { useI18n, pluginContextSb } from "@siren/core/vue";
import { useRoute } from "vue-router";
import { cn, Checkbox, Button, ContextMenuWrapper, ContentWrapper, Dialog, DialogTrigger, TitleItem, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose, TitleContainer, TabList, Tab } from "@siren/ui";
import { c as createLucideIcon } from "./createLucideIcon-D_qmVIjI.js";
/**
 * @license lucide-vue-next v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const CircleCheck = createLucideIcon("circle-check", [
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
  ["path", { d: "m9 12 2 2 4-4", key: "dzmm74" }]
]);
/**
 * @license lucide-vue-next v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const CircleDot = createLucideIcon("circle-dot", [
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
  ["circle", { cx: "12", cy: "12", r: "1", key: "41hilf" }]
]);
/**
 * @license lucide-vue-next v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const CircleEllipsis = createLucideIcon("circle-ellipsis", [
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
  ["path", { d: "M17 12h.01", key: "1m0b6t" }],
  ["path", { d: "M12 12h.01", key: "1mp3jc" }],
  ["path", { d: "M7 12h.01", key: "eqddd0" }]
]);
/**
 * @license lucide-vue-next v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const CirclePlus = createLucideIcon("circle-plus", [
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
  ["path", { d: "M8 12h8", key: "1wcyev" }],
  ["path", { d: "M12 8v8", key: "napkw2" }]
]);
/**
 * @license lucide-vue-next v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Funnel = createLucideIcon("funnel", [
  [
    "path",
    {
      d: "M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",
      key: "sc7q7i"
    }
  ]
]);
/**
 * @license lucide-vue-next v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const ListChecks = createLucideIcon("list-checks", [
  ["path", { d: "M13 5h8", key: "a7qcls" }],
  ["path", { d: "M13 12h8", key: "h98zly" }],
  ["path", { d: "M13 19h8", key: "c3s6r1" }],
  ["path", { d: "m3 17 2 2 4-4", key: "1jhpwq" }],
  ["path", { d: "m3 7 2 2 4-4", key: "1obspn" }]
]);
/**
 * @license lucide-vue-next v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Play = createLucideIcon("play", [
  [
    "path",
    {
      d: "M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",
      key: "10ikf1"
    }
  ]
]);
const _hoisted_1$2 = { class: "absolute left-1/2 top-1/2 translate-[-50%]" };
const _sfc_main$5 = /* @__PURE__ */ defineComponent({
  __name: "TaskCheckTitle",
  setup(__props) {
    const checked = ref(false);
    const t = useI18n();
    const toggleCheckState = () => {
      checked.value = !checked.value;
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(unref(cn)(
          "relative flex justify-between items-center h-[40px] pl-4 pr-6 mb-[1px]"
        ))
      }, [
        createVNode(unref(Checkbox), {
          class: normalizeClass(unref(cn)(
            "data-[state=checked]:bg-primary-foreground data-[state=checked]:text-primary",
            "dark:data-[state=checked]:bg-primary-foreground dark:data-[state=checked]:text-primary"
          )),
          "model-value": checked.value,
          "onUpdate:modelValue": toggleCheckState
        }, null, 8, ["class", "model-value"]),
        createElementVNode("div", _hoisted_1$2, [
          createVNode(unref(Button), {
            size: "sm",
            variant: "destructive"
          }, {
            default: withCtx(() => [
              createTextVNode(toDisplayString(unref(t)("delete")), 1)
            ]),
            _: 1
          })
        ])
      ], 2);
    };
  }
});
const _hoisted_1$1 = { class: "flex items-center gap-4" };
const _hoisted_2$1 = { key: 0 };
const _hoisted_3$1 = {
  key: 1,
  class: "flex items-center gap-4"
};
const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "TaskRecord",
  props: {
    id: {},
    editable: { type: Boolean }
  },
  setup(__props) {
    const task = useTaskStore();
    const inEdit = computed(() => task.inEdit);
    const checked = ref(false);
    const t = useI18n();
    const toggleCheckState = (e) => {
      e.preventDefault();
      e.stopPropagation();
      checked.value = !checked.value;
    };
    const handleContextMenu = () => {
      checked.value = true;
    };
    const handleDelete = (e) => {
      e.preventDefault();
      e.stopPropagation();
    };
    const setChecked = (value) => {
      checked.value = value;
    };
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ContextMenuWrapper), { id: "processing-task-item" }, {
        default: withCtx(() => [
          createElementVNode("div", {
            class: normalizeClass(unref(cn)(
              "relative flex justify-between items-center h-[48px] border-b pl-4 pr-6",
              !checked.value && "hover:bg-accent dark:hover:bg-accent/40",
              checked.value && "bg-primary text-primary-foreground"
            )),
            onClick: _cache[0] || (_cache[0] = () => setChecked(true)),
            onContextmenu: handleContextMenu
          }, [
            createElementVNode("div", _hoisted_1$1, [
              createVNode(unref(Checkbox), {
                class: normalizeClass([
                  "data-[state=checked]:bg-primary-foreground data-[state=checked]:text-primary",
                  "dark:data-[state=checked]:bg-primary-foreground dark:data-[state=checked]:text-primary",
                  "hidden",
                  inEdit.value && "block"
                ]),
                checked: checked.value,
                onClick: toggleCheckState
              }, null, 8, ["class", "checked"]),
              _cache[1] || (_cache[1] = createElementVNode("div", null, "《365天：今时之欲》全集在线观看 - 电影 - 努努影院(2)", -1))
            ]),
            inEdit.value ? (openBlock(), createElementBlock("div", _hoisted_2$1, [
              createVNode(unref(Button), {
                size: "sm",
                variant: "ghost",
                onClick: handleDelete
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString(unref(t)("delete")), 1)
                ]),
                _: 1
              })
            ])) : (openBlock(), createElementBlock("div", _hoisted_3$1, [
              createElementVNode("div", null, toDisplayString(unref(t)("task_in_progress")), 1),
              createVNode(unref(Play), { class: "size-4 cursor-pointer" })
            ]))
          ], 34)
        ]),
        _: 1
      });
    };
  }
});
const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "index",
  setup(__props) {
    const task = useTaskStore();
    const inEdit = computed(() => task.inEdit);
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ContentWrapper), null, {
        default: withCtx(() => [
          inEdit.value ? (openBlock(), createBlock(_sfc_main$5, { key: 0 })) : createCommentVNode("", true),
          createVNode(_sfc_main$4, {
            id: "",
            editable: true
          }),
          createVNode(_sfc_main$4, {
            id: "",
            editable: true
          })
        ]),
        _: 1
      });
    };
  }
});
const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "TaskAdd",
  setup(__props) {
    const t = useI18n();
    const open = ref(false);
    const setOpen = (value) => {
      open.value = value;
    };
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(Dialog), {
        open: open.value,
        "onUpdate:open": setOpen
      }, {
        default: withCtx(() => [
          createVNode(unref(DialogTrigger), { asChild: "" }, {
            default: withCtx(() => [
              createVNode(unref(TitleItem), {
                class: "mr-8",
                icon: unref(CirclePlus),
                title: unref(t)("task_add"),
                onClick: _cache[0] || (_cache[0] = ($event) => setOpen(true))
              }, null, 8, ["icon", "title"])
            ]),
            _: 1
          }),
          createVNode(unref(DialogContent), null, {
            default: withCtx(() => [
              createVNode(unref(DialogHeader), null, {
                default: withCtx(() => [
                  createVNode(unref(DialogTitle), null, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(unref(t)("task_add")), 1)
                    ]),
                    _: 1
                  }),
                  createVNode(unref(DialogDescription), null, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(unref(t)("task_add_desc")), 1)
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              }),
              _cache[2] || (_cache[2] = createElementVNode("div", null, "something..............", -1)),
              createVNode(unref(DialogFooter), null, {
                default: withCtx(() => [
                  createVNode(unref(DialogClose), { asChild: "" }, {
                    default: withCtx(() => [
                      createVNode(unref(Button), { variant: "outline" }, {
                        default: withCtx(() => [
                          createTextVNode(toDisplayString(unref(t)("cancel")), 1)
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  }),
                  createVNode(unref(Button), {
                    type: "submit",
                    onClick: _cache[1] || (_cache[1] = ($event) => setOpen(false))
                  }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(unref(t)("task_start")), 1)
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              })
            ]),
            _: 1
          })
        ]),
        _: 1
      }, 8, ["open"]);
    };
  }
});
const _hoisted_1 = { class: "flex h-full items-center" };
const _hoisted_2 = { class: "h-full flex items-center pr-4 gap-[2px]" };
const _hoisted_3 = { class: "text-muted-foreground text-xs hidden md:block mr-4" };
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "index",
  setup(__props) {
    const currentTab = ref("pending");
    const t = useI18n();
    const task = useTaskStore();
    const inEdit = computed(() => task.inEdit);
    const inDetail = computed(() => task.inDetail);
    const inFilter = computed(() => task.inFilter);
    const toggleInEdit = () => {
      task.setInEdit(!inEdit.value);
    };
    const toggleInDetail = () => {
      task.setInDetail(!inDetail.value);
    };
    const toggleInFilter = () => {
      task.setInFilter(!inFilter.value);
    };
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(TitleContainer), { class: "justify-between" }, {
        default: withCtx(() => [
          createElementVNode("div", _hoisted_1, [
            createVNode(_sfc_main$2),
            createVNode(unref(TabList), {
              "model-value": currentTab.value,
              "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => currentTab.value = $event)
            }, {
              default: withCtx(() => [
                createVNode(unref(Tab), {
                  name: "pending",
                  icon: unref(CircleDot),
                  title: unref(t)("task_in_progress")
                }, null, 8, ["icon", "title"]),
                createVNode(unref(Tab), {
                  name: "completed",
                  icon: unref(CircleCheck),
                  title: unref(t)("task_completed")
                }, null, 8, ["icon", "title"])
              ]),
              _: 1
            }, 8, ["model-value"])
          ]),
          createElementVNode("div", _hoisted_2, [
            createElementVNode("div", _hoisted_3, toDisplayString(unref(t)("task_total", { count: 100 })), 1),
            createVNode(unref(TitleItem), {
              active: inEdit.value,
              icon: unref(ListChecks),
              title: unref(t)("edit"),
              onClick: toggleInEdit
            }, null, 8, ["active", "icon", "title"]),
            createVNode(unref(TitleItem), {
              active: inFilter.value,
              icon: unref(Funnel),
              title: unref(t)("filter"),
              onClick: toggleInFilter
            }, null, 8, ["active", "icon", "title"]),
            createVNode(unref(TitleItem), {
              active: inDetail.value,
              icon: unref(CircleEllipsis),
              title: unref(t)("detail"),
              onClick: toggleInDetail
            }, null, 8, ["active", "icon", "title"])
          ])
        ]),
        _: 1
      });
    };
  }
});
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  setup(__props) {
    const route = useRoute();
    const renderComponent = shallowRef(null);
    const contextmenu = useContextMenuStore();
    provide(pluginContextSb, {
      id: computed(() => route.params.id),
      basePath: computed(() => route.path + "/")
    });
    watch(() => route.params.id, async (id) => {
      try {
        renderComponent.value = null;
        const result = await getManager(RenderManager).trigger(RenderEvent.Main, id, null);
        if (result) {
          renderComponent.value = result;
        } else {
          renderComponent.value = null;
        }
        const changedKeys = await getManager(ContextMenuManager).init();
        contextmenu.makeChange(changedKeys);
      } catch (err) {
        console.error(err);
      }
    }, { immediate: true });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(KeepAlive, null, [
        renderComponent.value ? (openBlock(), createBlock(Suspense, { key: 0 }, {
          fallback: withCtx(() => [..._cache[0] || (_cache[0] = [
            createTextVNode(" Page Error ", -1)
          ])]),
          default: withCtx(() => [
            (openBlock(), createBlock(resolveDynamicComponent(renderComponent.value)))
          ]),
          _: 1
        })) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
          createVNode(_sfc_main$1),
          createVNode(_sfc_main$3)
        ], 64))
      ], 1024);
    };
  }
});
export {
  _sfc_main as default
};

<!DOCTYPE html><html lang="en"><head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/svg+xml" href="/vite.svg">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tauri + Vue + Typescript App</title>
    <script type="importmap">{
  "imports": {
    "vue-router": "/vue-router/dist/vue-router.mjs",
    "@siren/ui": "/@siren/ui/dist/index.js",
    "@vueuse/shared": "/@vueuse/shared/index.mjs",
    "@vueuse/core": "/@vueuse/core/index.mjs",
    "@siren/core/api": "/@siren/core/dist/api/index.js",
    "@siren/core": "/@siren/core/dist/index.js",
    "@siren/core/tauri": "/@siren/core/dist/tauri/index.js",
    "@vue/devtools-shared": "/@vue/devtools-shared/dist/index.js",
    "@siren/core/store": "/@siren/core/dist/store/index.js",
    "@siren/core/vue": "/@siren/core/dist/vue/index.js",
    "perfect-debounce": "/perfect-debounce/dist/index.mjs",
    "@vue/shared": "/@vue/shared/dist/shared.esm-bundler.js",
    "@vue/reactivity": "/@vue/reactivity/dist/reactivity.esm-bundler.js",
    "@vue/runtime-core": "/@vue/runtime-core/dist/runtime-core.esm-bundler.js",
    "@vue/runtime-dom": "/@vue/runtime-dom/dist/runtime-dom.esm-bundler.js",
    "vue": "/vue/dist/vue.runtime.esm-bundler.js",
    "hookable": "/hookable/dist/index.mjs",
    "birpc": "/birpc/dist/index.mjs",
    "@vue/devtools-kit": "/@vue/devtools-kit/dist/index.js",
    "@vue/devtools-api": "/@vue/devtools-api/dist/index.js",
    "pinia": "/pinia/dist/pinia.mjs"
  }
}</script>
<script type="module" crossorigin="" src="/assets/index-44lBIxeR.js"></script>
    <link rel="stylesheet" crossorigin="" href="/assets/index-CDaHZQVs.css">
  </head>

  <body class="m-0 p-0 w-screen h-screen overflow-hidden">
    <div id="app" class="m-0 p-0 w-screen h-screen overflow-hidden"></div>
  

</body></html>
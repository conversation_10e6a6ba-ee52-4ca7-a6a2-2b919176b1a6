use std::{path::PathBuf, time::Instant};
use test_rust::high_performance_scanner::{HighPerformanceScanner, ScanConfig};

fn main() -> anyhow::Result<()> {
    println!("🏁 High-Performance Scanner Benchmark");
    println!("=====================================");

    let test_path = PathBuf::from(".");
    
    // Benchmark different configurations
    benchmark_thread_scaling(&test_path)?;
    benchmark_batch_sizes(&test_path)?;
    benchmark_depth_limits(&test_path)?;
    
    Ok(())
}

fn benchmark_thread_scaling(path: &PathBuf) -> anyhow::Result<()> {
    println!("\n📊 Thread Scaling Benchmark");
    println!("---------------------------");
    
    let thread_counts = vec![1, 2, 4, 8, 16];
    let mut results = Vec::new();
    
    for thread_count in thread_counts {
        let config = ScanConfig {
            max_threads: thread_count,
            min_file_size_threshold: 0,
            max_depth: None,
            follow_symlinks: false,
            batch_size: 1000,
            show_progress: false,
        };
        
        let scanner = HighPerformanceScanner::with_config(config);
        
        // Warm up
        let _ = scanner.quick_scan(path)?;
        
        // Actual benchmark
        let start = Instant::now();
        let stats = scanner.quick_scan(path)?;
        let duration = start.elapsed();
        
        results.push((thread_count, duration, stats.files_per_second));
        
        println!("Threads: {:2} | Time: {:8.2}ms | Speed: {:8.0} files/sec", 
            thread_count, 
            duration.as_millis(),
            stats.files_per_second
        );
    }
    
    // Find best performance
    let best = results.iter().max_by(|a, b| a.2.partial_cmp(&b.2).unwrap()).unwrap();
    println!("🏆 Best: {} threads at {:.0} files/sec", best.0, best.2);
    
    Ok(())
}

fn benchmark_batch_sizes(path: &PathBuf) -> anyhow::Result<()> {
    println!("\n📦 Batch Size Benchmark");
    println!("-----------------------");
    
    let batch_sizes = vec![100, 500, 1000, 2000, 5000];
    let mut results = Vec::new();
    
    for batch_size in batch_sizes {
        let config = ScanConfig {
            max_threads: num_cpus::get(),
            min_file_size_threshold: 0,
            max_depth: None,
            follow_symlinks: false,
            batch_size,
            show_progress: false,
        };
        
        let scanner = HighPerformanceScanner::with_config(config);
        
        let start = Instant::now();
        let stats = scanner.quick_scan(path)?;
        let duration = start.elapsed();
        
        results.push((batch_size, duration, stats.files_per_second));
        
        println!("Batch: {:4} | Time: {:8.2}ms | Speed: {:8.0} files/sec", 
            batch_size, 
            duration.as_millis(),
            stats.files_per_second
        );
    }
    
    let best = results.iter().max_by(|a, b| a.2.partial_cmp(&b.2).unwrap()).unwrap();
    println!("🏆 Best: {} batch size at {:.0} files/sec", best.0, best.2);
    
    Ok(())
}

fn benchmark_depth_limits(path: &PathBuf) -> anyhow::Result<()> {
    println!("\n🌳 Depth Limit Benchmark");
    println!("------------------------");
    
    let depth_limits = vec![None, Some(1), Some(2), Some(3), Some(5)];
    
    for depth_limit in depth_limits {
        let config = ScanConfig {
            max_threads: num_cpus::get(),
            min_file_size_threshold: 0,
            max_depth: depth_limit,
            follow_symlinks: false,
            batch_size: 1000,
            show_progress: false,
        };
        
        let scanner = HighPerformanceScanner::with_config(config);
        
        let start = Instant::now();
        let stats = scanner.quick_scan(path)?;
        let duration = start.elapsed();
        
        let depth_str = match depth_limit {
            None => "∞".to_string(),
            Some(d) => d.to_string(),
        };
        
        println!("Depth: {:>2} | Time: {:8.2}ms | Files: {:6} | Dirs: {:4} | Speed: {:8.0} files/sec", 
            depth_str,
            duration.as_millis(),
            stats.total_files,
            stats.total_directories,
            stats.files_per_second
        );
    }
    
    Ok(())
}

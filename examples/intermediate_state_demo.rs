use std::{path::PathBuf, sync::{Arc, Mutex}, time::Instant};
use test_rust::high_performance_scanner::{
    HighPerformanceScanner, ScanConfig, IntermediateConfig, IntermediateState
};

fn main() -> anyhow::Result<()> {
    println!("🔄 Intermediate State Scanner Demo");
    println!("==================================");

    let test_path = PathBuf::from(".");
    
    // Demo 1: Basic intermediate state collection
    println!("\n📊 Demo 1: Basic Intermediate State Collection");
    println!("----------------------------------------------");
    demo_basic_intermediate(&test_path)?;

    // Demo 2: Real-time progress tracking
    println!("\n⏱️  Demo 2: Real-time Progress Tracking");
    println!("--------------------------------------");
    demo_progress_tracking(&test_path)?;

    // Demo 3: Partial tree collection
    println!("\n🌳 Demo 3: Partial Tree Collection");
    println!("----------------------------------");
    demo_partial_tree(&test_path)?;

    Ok(())
}

fn demo_basic_intermediate(path: &PathBuf) -> anyhow::Result<()> {
    let scanner = HighPerformanceScanner::new();
    
    // Shared state to collect intermediate updates
    let updates = Arc::new(Mutex::new(Vec::new()));
    let updates_clone = Arc::clone(&updates);
    
    // Add callback to collect intermediate states
    scanner.add_state_callback(move |state: &IntermediateState| {
        let mut updates = updates_clone.lock().unwrap();
        updates.push((
            state.stats.total_files,
            state.stats.total_directories,
            state.stats.total_size,
            state.scan_progress,
            state.current_path.clone(),
        ));
    });

    let config = IntermediateConfig {
        callback_interval_ms: 200, // Update every 200ms
        collect_partial_tree: false, // Don't collect tree for this demo
        max_partial_entries: 0,
        estimate_progress: true,
    };

    let start = Instant::now();
    let result = scanner.scan_with_intermediate(path, config)?;
    let duration = start.elapsed();

    println!("✅ Scan completed in {:?}", duration);
    println!("📁 Final: {} directories, {} files, {}", 
        result.directory_count, result.file_count, result.format_size());

    // Show intermediate updates
    let updates = updates.lock().unwrap();
    println!("\n📈 Intermediate Updates ({} total):", updates.len());
    for (i, (files, dirs, size, progress, current_path)) in updates.iter().enumerate() {
        if i % 3 == 0 || i == updates.len() - 1 { // Show every 3rd update + last
            println!("  Update {}: Files: {}, Dirs: {}, Size: {:.2} MB, Progress: {:.1}%, Path: {:?}",
                i + 1, files, dirs, *size as f64 / (1024.0 * 1024.0), progress * 100.0,
                current_path.file_name().unwrap_or_default()
            );
        }
    }

    Ok(())
}

fn demo_progress_tracking(path: &PathBuf) -> anyhow::Result<()> {
    let scanner = HighPerformanceScanner::new();
    
    // Progress tracking callback
    scanner.add_state_callback(|state: &IntermediateState| {
        let progress_bar = create_progress_bar(state.scan_progress, 30);
        let speed = state.stats.files_per_second;
        
        print!("\r🔄 Progress: {} {:.1}% | Files: {} | Speed: {:.0} files/s | Current: {:?}",
            progress_bar,
            state.scan_progress * 100.0,
            state.stats.total_files,
            speed,
            state.current_path.file_name().unwrap_or_default()
        );
        
        use std::io::{self, Write};
        io::stdout().flush().unwrap();
    });

    let config = IntermediateConfig {
        callback_interval_ms: 100, // Fast updates for smooth progress
        collect_partial_tree: false,
        max_partial_entries: 0,
        estimate_progress: true,
    };

    let start = Instant::now();
    let result = scanner.scan_with_intermediate(path, config)?;
    let duration = start.elapsed();

    println!("\n✅ Scan completed in {:?}", duration);
    println!("📊 Final result: {} files, {} directories, {}", 
        result.file_count, result.directory_count, result.format_size());

    Ok(())
}

fn demo_partial_tree(path: &PathBuf) -> anyhow::Result<()> {
    let scanner = HighPerformanceScanner::new();
    
    // Collect partial tree data
    let partial_trees = Arc::new(Mutex::new(Vec::new()));
    let partial_trees_clone = Arc::clone(&partial_trees);
    
    scanner.add_state_callback(move |state: &IntermediateState| {
        if !state.partial_tree.is_empty() {
            let mut trees = partial_trees_clone.lock().unwrap();
            trees.push((
                state.stats.total_files,
                state.partial_tree.len(),
                state.completed_paths.len(),
            ));
        }
    });

    let config = IntermediateConfig {
        callback_interval_ms: 300,
        collect_partial_tree: true,
        max_partial_entries: 100, // Limit to avoid memory issues
        estimate_progress: true,
    };

    let start = Instant::now();
    let result = scanner.scan_with_intermediate(path, config)?;
    let duration = start.elapsed();

    println!("✅ Scan completed in {:?}", duration);
    
    // Show partial tree collection progress
    let trees = partial_trees.lock().unwrap();
    println!("\n🌳 Partial Tree Collection Progress:");
    for (i, (files, tree_entries, completed)) in trees.iter().enumerate() {
        if i % 2 == 0 || i == trees.len() - 1 { // Show every 2nd update + last
            println!("  Update {}: Files processed: {}, Tree entries: {}, Completed paths: {}",
                i + 1, files, tree_entries, completed);
        }
    }

    // Show final intermediate state
    if let Some(final_state) = scanner.get_intermediate_state() {
        println!("\n📋 Final Intermediate State:");
        println!("  Current path: {:?}", final_state.current_path);
        println!("  Partial tree entries: {}", final_state.partial_tree.len());
        println!("  Completed paths: {}", final_state.completed_paths.len());
        println!("  Progress: {:.1}%", final_state.scan_progress * 100.0);
        
        // Show some entries from partial tree
        println!("\n🔍 Sample entries from partial tree:");
        for (i, (path, entry)) in final_state.partial_tree.iter().enumerate() {
            if i >= 5 { break; } // Show first 5 entries
            println!("    {}: {} ({})", 
                i + 1, 
                path.file_name().unwrap_or_default().to_string_lossy(),
                entry.format_size()
            );
        }
    }

    Ok(())
}

fn create_progress_bar(progress: f64, width: usize) -> String {
    let filled = (progress * width as f64) as usize;
    let empty = width - filled;
    
    format!("[{}{}]", 
        "█".repeat(filled),
        "░".repeat(empty)
    )
}

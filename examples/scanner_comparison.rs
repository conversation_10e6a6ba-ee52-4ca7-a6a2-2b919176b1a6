use std::path::PathBuf;
use test_rust::{
    scanner::ParallelScanner,
    optimized_scanner::OptimizedScanner,
    performance_test::PerformanceTest,
};

fn main() -> anyhow::Result<()> {
    println!("🔍 文件扫描器性能对比示例");
    println!("{}", "=".repeat(50));

    // 获取当前目录作为测试目录
    let test_dir = std::env::current_dir()?;
    println!("测试目录: {:?}", test_dir);

    // 运行性能对比
    PerformanceTest::run_comparison(&test_dir);

    // 演示优化版本的使用
    println!("\n🚀 优化版本详细演示");
    println!("{}", "-".repeat(50));

    let scanner = OptimizedScanner::new(500); // 批量大小 500
    let result = scanner.scan(&test_dir)?;

    println!("📁 扫描结果:");
    println!("   路径: {:?}", result.get_path());
    println!("   大小: {} bytes", result.get_size());
    println!("   是否为目录: {}", result.is_directory());
    println!("   子项数量: {}", result.get_children().len());

    // 显示前几个最大的子项
    if result.is_directory() {
        println!("\n📊 前 5 个最大的子项:");
        let mut children: Vec<_> = result.get_children().iter().collect();
        children.sort_by(|a, b| b.get_size().cmp(&a.get_size()));
        
        for (i, child) in children.iter().take(5).enumerate() {
            let size_mb = child.get_size() as f64 / 1024.0 / 1024.0;
            let name = child.get_path()
                .file_name()
                .map(|n| n.to_string_lossy())
                .unwrap_or_else(|| "Unknown".into());
            
            println!("   {}. {} - {:.2} MB", i + 1, name, size_mb);
        }
    }

    let (files, dirs) = scanner.get_stats();
    println!("\n📈 扫描统计:");
    println!("   文件数: {}", files);
    println!("   目录数: {}", dirs);
    println!("   总计: {}", files + dirs);

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_scanner_comparison() {
        let current_dir = std::env::current_dir().unwrap();
        PerformanceTest::run_comparison(&current_dir);
    }

    #[test]
    fn test_optimized_scanner_usage() {
        let current_dir = std::env::current_dir().unwrap();
        let scanner = OptimizedScanner::default();
        
        let result = scanner.scan(&current_dir).unwrap();
        assert!(result.get_size() > 0);
        
        if result.is_directory() {
            // 目录应该有子项（至少有 Cargo.toml 和 src）
            assert!(result.get_children().len() > 0);
        }
    }

    #[test]
    fn test_batch_size_impact() {
        let current_dir = std::env::current_dir().unwrap();
        
        // 测试不同批量大小的影响
        for batch_size in [100, 500, 1000] {
            let scanner = OptimizedScanner::new(batch_size);
            let start = std::time::Instant::now();
            let _result = scanner.scan(&current_dir).unwrap();
            let duration = start.elapsed();
            
            println!("批量大小 {}: {:?}", batch_size, duration);
        }
    }
}

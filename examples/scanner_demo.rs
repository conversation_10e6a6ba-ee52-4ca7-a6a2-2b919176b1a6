use std::{path::PathBuf, time::Instant};
use test_rust::high_performance_scanner::{HighPerformanceScanner, ScanConfig};

fn main() -> anyhow::Result<()> {
    println!("🚀 High-Performance Directory Scanner Demo");
    println!("==========================================");

    // Test with current directory
    let test_path = PathBuf::from(".");
    
    // Basic scan
    println!("\n📊 Basic Scan Example");
    println!("---------------------");
    basic_scan_example(&test_path)?;

    // Custom configuration
    println!("\n⚙️  Custom Configuration Example");
    println!("--------------------------------");
    custom_config_example(&test_path)?;

    // Quick scan
    println!("\n⚡ Quick Scan Example");
    println!("--------------------");
    quick_scan_example(&test_path)?;

    Ok(())
}

fn basic_scan_example(path: &PathBuf) -> anyhow::Result<()> {
    let scanner = HighPerformanceScanner::new();
    
    let start = Instant::now();
    let result = scanner.scan_and_process(path)?;
    let duration = start.elapsed();
    
    let stats = scanner.get_stats();
    
    println!("✅ Scan completed in {:?}", duration);
    println!("📁 Total directories: {}", stats.total_directories);
    println!("📄 Total files: {}", stats.total_files);
    println!("💾 Total size: {}", result.format_size());
    println!("🚀 Speed: {:.0} files/sec, {:.2} MB/sec", 
        stats.files_per_second, 
        stats.bytes_per_second / (1024.0 * 1024.0)
    );
    
    println!("\n🔝 Top 5 largest entries:");
    for (i, entry) in result.get_top_entries(5).iter().enumerate() {
        println!("  {}. {} - {}", i + 1, entry.name, entry.format_size());
    }
    
    Ok(())
}

fn custom_config_example(path: &PathBuf) -> anyhow::Result<()> {
    let config = ScanConfig {
        max_threads: 4,
        min_file_size_threshold: 1024, // 1KB threshold
        max_depth: Some(2), // Limit depth to 2 levels
        follow_symlinks: false,
        batch_size: 100,
        show_progress: false, // Set to true to see progress
    };
    
    let scanner = HighPerformanceScanner::with_config(config);
    
    let start = Instant::now();
    let result = scanner.scan_and_process(path)?;
    let duration = start.elapsed();
    
    let stats = scanner.get_stats();
    
    println!("✅ Custom scan completed in {:?}", duration);
    println!("📊 Configuration: 4 threads, 1KB threshold, max depth 2");
    println!("📁 Directories: {}, 📄 Files: {}, 💾 Size: {}", 
        stats.total_directories, stats.total_files, result.format_size());
    
    println!("\n🌳 Directory tree (limited to 2 levels):");
    result.print_tree(2);
    
    Ok(())
}

fn quick_scan_example(path: &PathBuf) -> anyhow::Result<()> {
    let scanner = HighPerformanceScanner::new();
    
    let start = Instant::now();
    let stats = scanner.quick_scan(path)?;
    let duration = start.elapsed();
    
    println!("⚡ Quick scan completed in {:?}", duration);
    println!("📊 Statistics only (no tree structure built):");
    println!("   📁 Directories: {}", stats.total_directories);
    println!("   📄 Files: {}", stats.total_files);
    println!("   💾 Total size: {:.2} MB", stats.total_size as f64 / (1024.0 * 1024.0));
    println!("   🚀 Speed: {:.0} files/sec", stats.files_per_second);
    
    Ok(())
}

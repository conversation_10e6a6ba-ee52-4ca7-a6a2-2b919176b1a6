
// use proc_macro::TokenStream;
// use quote::quote;
// use syn::{parse_macro_input, Data, DeriveInput, ItemFn};

// #[proc_macro_attribute]
// pub fn add_timestamp(_attr: TokenStream, item: TokenStream) -> TokenStream {
//     eprintln!("item is: {item:?}");
//     eprintln!("attr is: {_attr:?}");

//     let input = syn::parse::<DeriveInput>(item).unwrap();    


//     // let input = parse_macro_input!(item as DeriveInput);
//     let name = &input.ident;

//     eprint!("name is");
    
//     let expanded = quote! {
//         #input
        
//         impl #name {
//             pub fn get_timestamp() -> u64 {
//                 use std::time::{SystemTime, UNIX_EPOCH};
//                 SystemTime::now()
//                     .duration_since(UNIX_EPOCH)
//                     .expect("Time went backwards")
//                     .as_secs()
//             }
//         }
//     };
    
//     // TokenStream::from(expanded)
//     expanded.into()
// }



// #[proc_macro_attribute]
// pub fn singleton_mutex(_args: TokenStream, input: TokenStream) -> TokenStream {
//     let input = parse_macro_input!(input as DeriveInput);
//     let name = &input.ident;
    
//     let expanded = quote! {
//         #input
        
//         impl #name {
//             pub fn instance() -> std::sync::MutexGuard<'static, #name> {
//                 static INSTANCE: std::sync::OnceLock<std::sync::Mutex<#name>> =
//                     std::sync::OnceLock::new();
//                 INSTANCE.get_or_init(|| std::sync::Mutex::new(#name::default())).lock().unwrap()
//             }
//         }
//     };
    
//     expanded.into()
// }

// #[proc_macro_attribute]
// pub fn show_streams(_attr: TokenStream, item: TokenStream) -> TokenStream {
//     let input = parse_macro_input!(item as ItemFn);
//     println!("{:#?}", input);

//     let vis = &input.vis;
//     let sig = &input.sig;
//     let block = &input.block.stmts[1];
//     let asyncness = &input.sig.asyncness;
//     if let Some(x) = &input.sig.asyncness {
//         let y = x.to_owned();
//     }

//     println!("{:#?}", asyncness);


//     let a = quote! {
//         #vis #sig #block
//     }.to_string();

//     println!("{a}");

//     // match &input.data {
//     //     Data::Struct(s) => {
//     //         let name = &input.ident;
//     //         let fields = &s.fields;
//     //         for field in fields {
//     //             let a = field.ident.as_ref().unwrap();

//     //             let rr = quote! {
//     //                 #a xx
//     //             }.to_string();
//     //             println!("{rr}");
//     //         }

//     //     }   
//     //     _ => ()
//     // };

//     // let mut a = quote![];
//     // a.extend(quote! {});

//     quote! {
//         macro_rules! test_macro {
//             () => {
//                 fn testx() {
//                     println!("This is a test macro!");
//                 }
//             };
//         }
//         test_macro!();


//     }.into()
// }
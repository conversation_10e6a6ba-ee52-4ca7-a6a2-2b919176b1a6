
use proc_macro::TokenStream;
use quote::quote;
use syn::{parse_macro_input, DeriveInput};

#[proc_macro_attribute]
pub fn add_timestamp(_attr: TokenStream, item: TokenStream) -> TokenStream {
    eprintln!("item is: {item:?}");
    eprintln!("attr is: {_attr:?}");

    let input = syn::parse::<DeriveInput>(item).unwrap();    


    // let input = parse_macro_input!(item as DeriveInput);
    let name = &input.ident;

    eprint!("name is");
    
    let expanded = quote! {
        #input
        
        impl #name {
            pub fn get_timestamp() -> u64 {
                use std::time::{SystemTime, UNIX_EPOCH};
                SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .expect("Time went backwards")
                    .as_secs()
            }
        }
    };
    
    // TokenStream::from(expanded)
    expanded.into()
}



#[proc_macro_attribute]
pub fn singleton_mutex(_args: TokenStream, input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);
    let name = &input.ident;
    
    let expanded = quote! {
        #input
        
        impl #name {
            pub fn instance() -> std::sync::MutexGuard<'static, #name> {
                static INSTANCE: std::sync::OnceLock<std::sync::Mutex<#name>> =
                    std::sync::OnceLock::new();
                INSTANCE.get_or_init(|| std::sync::Mutex::new(#name::default())).lock().unwrap()
            }
        }
    };
    
    expanded.into()
}

trait Emitter {
    fn emit<T: 'static, U>(&self, event: T, data: U);
    fn on<T: 'static, U, F>(&mut self, event: T, callback: F)
    where
        F: FnMut(U) + 'static;
}

use syn::{Data, DataStruct, Fields, FieldsNamed};
use syn::punctuated::Punctuated;
use syn::token::Comma;

#[proc_macro_derive(Emitter)]
pub fn derive_emitter(input: TokenStream) -> TokenStream {
    let mut input = parse_macro_input!(input as DeriveInput);
    let name = &input.ident;
    
    let (impl_generics, ty_generics, where_clause) = input.generics.split_for_impl();
    
    // 修改结构体，添加 listeners 字段
    if let Data::Struct(DataStruct { fields: Fields::Named(FieldsNamed { named: fields, .. }), .. }) = &mut input.data {
        let mut new_fields = fields.clone();
        
        // 检查是否已经有 listeners 字段
        let has_listeners = new_fields.iter().any(|field| {
            field.ident.as_ref().map(|ident| ident == "listeners").unwrap_or(false)
        });
        
        if !has_listeners {
            // 添加 listeners 字段
            new_fields.push(syn::parse_quote! {
                listeners: std::collections::HashMap<std::any::TypeId, Box<dyn std::any::Any>>
            });
            
            // 更新结构体的字段
            if let Data::Struct(DataStruct { fields: Fields::Named(FieldsNamed { named, .. }), .. }) = &mut input.data {
                *named = new_fields;
            }
        }
    }
    
    let expanded = quote! {
        impl #impl_generics Emitter for #name #ty_generics #where_clause {
            fn emit<T: 'static, U>(&self, event: T, data: U) {
                use std::any::{TypeId, Any};
                
                let type_id = std::any::TypeId::of::<T>();
                if let Some(callbacks) = self.listeners.get(&type_id) {
                    if let Some(callbacks) = callbacks.downcast_ref::<Vec<Box<dyn FnMut(U)>>>() {
                        for callback in callbacks {
                            let mut callback_clone = callback.clone();
                            callback_clone(data);
                        }
                    }
                }
            }
            
            fn on<T: 'static, U, F>(&mut self, event: T, callback: F)
            where
                F: FnMut(U) + 'static,
            {
                use std::any::{TypeId, Any};
                
                let type_id = std::any::TypeId::of::<T>();
                let callback = Box::new(callback) as Box<dyn FnMut(U)>;
                
                let callbacks_vec = self.listeners
                    .entry(type_id)
                    .or_insert_with(|| {
                        Box::new(Vec::<Box<dyn FnMut(U)>>::new()) as Box<dyn Any>
                    })
                    .downcast_mut::<Vec<Box<dyn FnMut(U)>>>()
                    .unwrap();
                
                callbacks_vec.push(callback);
            }
        }
        
        impl #impl_generics Default for #name #ty_generics #where_clause {
            fn default() -> Self {
                Self {
                    listeners: std::collections::HashMap::new(),
                }
            }
        }
    };

    expanded.into()
}
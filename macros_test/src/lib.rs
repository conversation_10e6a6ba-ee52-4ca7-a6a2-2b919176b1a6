// use syn::parse::Parse;
// use syn::{parse_macro_input, Expr, ExprLit, Lit, LitStr, Meta, Token};
// use syn::punctuated::Punctuated;
// use syn::Attribute;
use quote::quote;
// use syn::{parse_macro_input, punctuated::Punctuated, LitStr, Token};

use proc_macro::TokenStream;
use syn::{parse_macro_input, DeriveInput, Ident};

#[proc_macro_attribute]
pub fn siren_plugin(attrs: TokenStream, input: TokenStream) -> TokenStream {
  let input = parse_macro_input!(input as DeriveInput);
  let ident = &input.ident;

  // let get_ident = Ident::new(&format!("get_{}", ident), ident.span());

  let r = quote! {
    #input
    #[unsafe(no_mangle)]
    pub extern "C" fn _create_plugin() -> *mut PluginBox {
        Box::into_raw(Box::new(PluginBox {
            ptr: Box::into_raw(Box::new(#ident)),
        }))
    }

    pub static LIB_VERSION: &str = env!("CARGO_PKG_VERSION");

    #[unsafe(no_mangle)]
    pub extern "C" fn _get_version_ptr() -> *const u8 {
        LIB_VERSION.as_ptr()
    }

    #[unsafe(no_mangle)]
    pub extern "C" fn _get_version_len() -> usize {
        LIB_VERSION.as_bytes().len()
    }

  };
  // input
  r.into()
}

    // #[no_mangle]
    // pub extern "C" fn _get_version_ptr() -> *const u8 {
    //     LIB_VERSION.as_ptr()
    // }


    // #[no_mangle]
    // pub extern "C" fn _get_version_len() -> usize {
    //     LIB_VERSION.as_bytes().len()
    // }
[{"id": "general", "name": "settings_general", "type": "category", "children": [{"id": "language", "name": "settings_general_language", "description": "settings_general_language_desc", "placeholder": "settings_general_language_placeholder", "type": "select", "default_value": "system", "client_side": true, "options": []}, {"id": "theme", "name": "settings_general_theme", "description": "settings_general_theme_desc", "type": "radio", "default_value": "system", "client_side": true, "options": [{"name": "settings_general_theme_system", "value": "system"}, {"name": "settings_general_theme_light", "value": "light"}, {"name": "settings_general_theme_dark", "value": "dark"}]}, {"id": "omnibar", "name": "settings_general_omnibar", "type": "group", "children": [{"id": "enable", "name": "settings_general_omnibar_enable", "type": "checkbox", "default_value": true}, {"id": "shortcut", "name": "settings_general_omnibar_shortcut", "description": "settings_general_omnibar_shortcut_desc", "type": "keyboard", "default_value": "Meta+Shift+Enter"}]}]}, {"id": "plugin", "name": "settings_plugins", "type": "category", "children": []}]
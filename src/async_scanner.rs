use std::{
    collections::{HashMap, HashSet},
    fs,
    path::PathBuf,
    sync::{Arc, OnceLock},
};

use futures::future::try_join_all;
use serde::Serialize;
use tokio::sync::Mutex;

#[derive(Debug, Serialize)]
pub struct DiskStat {
    name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    size: Option<u64>,
    #[serde(skip_serializing_if = "Vec::is_empty")]
    children: Vec<DiskStat>,
}

pub enum ScanFileInfo {
    File(u64),
    Directory,
}

#[derive(Debug)]
pub struct AsyncScanner {
    pub size_map: HashMap<PathBuf, u64>,
    pub path_map: HashMap<PathBuf, HashSet<PathBuf>>,
}

// Ensure ParallelScanner is Send + Sync
unsafe impl Send for AsyncScanner {}
unsafe impl Sync for AsyncScanner {}

impl AsyncScanner {
    pub fn instance() -> &'static Arc<Mutex<Self>> {
        static INSTANCE: OnceLock<Arc<Mutex<AsyncScanner>>> = OnceLock::new();

        INSTANCE.get_or_init(|| {
            Arc::new(Mutex::new(Self {
                size_map: HashMap::new(),
                path_map: HashMap::new(),
            }))
        })
    }

    pub fn new() -> Self {
        Self {
                size_map: HashMap::new(),
                path_map: HashMap::new(),
        }
    }

    pub fn scan_directory(
        scanner: Arc<Mutex<Self>>,
        path: PathBuf,
    ) -> std::pin::Pin<Box<dyn std::future::Future<Output = anyhow::Result<()>> + Send>> {
        Box::pin(async move {
            let mut path_set = HashSet::new();
            let mut size_map = HashMap::new();
            let mut handles = Vec::new();

            // Read directory entries
            let entries = match fs::read_dir(&path) {
                Ok(entries) => entries,
                Err(err) => {
                    println!("Failed to read directory {:?}: {:?}", path, err);
                    return Err(err.into());
                }
            };

            // Process entries sequentially to collect information
            for entry in entries {
                match entry {
                    Ok(entry) => match entry.file_type() {
                        Ok(file_type) => {
                            let cur_path = entry.path();

                            if file_type.is_file() || file_type.is_dir() {
                                path_set.insert(cur_path.clone());
                            }

                            if file_type.is_file() {
                                if let Ok(metadata) = entry.metadata() {
                                    let size = metadata.len();
                                    size_map.insert(cur_path, size);
                                }
                            } else if file_type.is_dir() {
                                let scanner = scanner.clone();
                                let handle = tokio::spawn(async move {
                                    Self::scan_directory(scanner, cur_path.clone()).await;
                                });

                                handles.push(handle);
                            }
                        }
                        Err(err) => {
                            println!("file type error: {:?}", err);
                        }
                    },
                    Err(err) => {
                        println!("entry error: {:?}", err);
                    }
                }
            }

            // Update the shared state
            {
                let mut scanner_guard = scanner.lock().await;
                scanner_guard.path_map.insert(path.clone(), path_set);
                scanner_guard.size_map.extend(size_map);
            }


            try_join_all(handles).await;


            Ok(())
        })
    }
}

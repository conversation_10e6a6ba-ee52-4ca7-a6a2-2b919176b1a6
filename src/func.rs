// pub fn greet(s: String) {
//     println!("Hello, {}!", s);
// }

// #[macro_export]
// macro_rules! __cmd__greet {
//     ($path:path, $ident:ident) => {
//       {
//         let a = stringify!($path);
//         let b = stringify!($ident);
        
//         true
//       }
//       // {
//       //   println!("Hello, !");
//       //   true
//       // }
//     };
// }
// pub use __cmd__greet;
// // pub use __cmd__greet;


// #[macro_export]
// macro_rules! __cmd__test_xxx {
//     ($path:path, $ident:ident) => {
//       {
//         let a = stringify!($path);
//         let b = stringify!($ident);
        
//         true
//       }
//       // {
//       //   println!("Hello, !");
//       //   true
//       // }
//     };
// }
// pub use __cmd__test_xxx;

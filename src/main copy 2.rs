use ab_glyph::PxScale;
use image::{ImageBuffer, Rgba};
use rusttype::{Font, Scale};
use imageproc::drawing::{draw_text_mut, Canvas};
use imageproc::rect::Rect;

/// 生成一张图片，包含 12:45 的文字
fn main() {
    // 加载字体（需要你放一个字体文件在项目目录，比如 "DejaVuSans.ttf"）
    let font_data = std::fs::read("DejaVuSans.ttf").expect("无法读取字体文件");
    let font = Font::try_from_vec(font_data).expect("加载字体失败");

    // 要绘制的文字
    let text = "12:45";

    // 字号
    let scale = PxScale { x: 32.0, y: 32.0 };

    // 获取文字边界
    let v_metrics = font.v_metrics(scale);
    let glyphs: Vec<_> = font.layout(text, scale, rusttype::point(0.0, 0.0 + v_metrics.ascent)).collect();
    let width = glyphs.last().unwrap().pixel_bounding_box().unwrap().max.x as u32;
    let height = (v_metrics.ascent - v_metrics.descent).ceil() as u32;

    // 边距
    let padding_x = 8;
    let padding_y = 6;

    // 画布大小
    let img_width = width + padding_x * 2;
    let img_height = height + padding_y * 2;

    // 创建背景 (透明 RGBA)
    let mut img = ImageBuffer::from_pixel(img_width, img_height, Rgba([0, 0, 0, 0]));

    // 绘制一个圆角矩形的黑色背景
    draw_rounded_rect_mut(&mut img, img_width, img_height, 4, Rgba([0, 0, 0, 255]));

    // 在中间绘制文字 (白色) 
    let start_x = padding_x as i32;
    let start_y = padding_y as i32;
    draw_text_mut(&mut img, Rgba([255, 255, 255, 255]), start_x, start_y, scale, &font, text);

    // 保存为文件
    img.save("output.png").unwrap();
}

/// 绘制圆角矩形 (简单填充)
fn draw_rounded_rect_mut(img: &mut ImageBuffer<Rgba<u8>, Vec<u8>>, w: u32, h: u32, r: u32, color: Rgba<u8>) {
    use imageproc::drawing::draw_filled_circle_mut;
    use imageproc::drawing::draw_filled_rect_mut;

    // 中间矩形
    let rect = Rect::at(r as i32, 0).of_size(w - 2 * r, h);
    draw_filled_rect_mut(img, rect, color);

    let rect = Rect::at(0, r as i32).of_size(w, h - 2 * r);
    draw_filled_rect_mut(img, rect, color);

    // 四个角圆
    draw_filled_circle_mut(img, (r as i32, r as i32), r as i32, color);
    draw_filled_circle_mut(img, ((w - r) as i32, r as i32), r as i32, color);
    draw_filled_circle_mut(img, (r as i32, (h - r) as i32), r as i32, color);
    draw_filled_circle_mut(img, ((w - r) as i32, (h - r) as i32), r as i32, color);
}
use ffmpeg_next::format::{input, output};
use ffmpeg_next::codec::context::Context;
use ffmpeg_next::media::Type;
use ffmpeg_next::Error;
use std::path::Path;
use std::time::Duration;

// 更精确的进度计算版本（使用解码器时间戳）
fn extract_audio_with_better_progress(input_path: &str, output_path: &str) -> Result<(), Error> {
    ffmpeg_next::init()?;
    
    let mut input_ctx = input(&Path::new(input_path))?;
    
    // 获取输入文件的总时长（秒）
    let duration_seconds = input_ctx.duration() as f64 / ffmpeg_next::ffi::AV_TIME_BASE as f64;
    
    let input_stream = input_ctx
        .streams()
        .best(Type::Audio)
        .ok_or(Error::StreamNotFound)?;
    
    let audio_stream_index = input_stream.index();
    let context = Context::from_parameters(input_stream.parameters())?;
    let decoder = context.decoder().audio()?;
    
    let mut output_ctx = output(&Path::new(output_path))?;
    
    let output_stream_index = {
        let mut stream = output_ctx.add_stream(decoder.codec())?;
        stream.set_parameters(input_stream.parameters());
        stream.index()
    };
    
    output_ctx.set_metadata(input_ctx.metadata().to_owned());
    output_ctx.write_header()?;
    
    println!("开始提取音频...");
    println!("总时长: {:.2}秒", duration_seconds);
    
    let mut last_progress = -1;
    
    for (stream, packet) in input_ctx.packets() {
        if stream.index() == audio_stream_index {
            let mut output_packet = packet.clone();
            let output_stream = output_ctx.stream(output_stream_index).unwrap();
            
            output_packet.set_stream(output_stream_index as usize);
            output_packet.rescale_ts(stream.time_base(), output_stream.time_base());
            
            output_packet.write_interleaved(&mut output_ctx)?;
            
            // 更精确的进度计算
            if let (Some(pts), time_base) = (packet.pts(), stream.time_base()) {
                let current_time = pts as f64 * time_base.numerator() as f64 / time_base.denominator() as f64;
                let progress = ((current_time / duration_seconds) * 100.0) as i32;
                
                if progress > last_progress && progress <= 100 {
                    println!("进度: {}% ({:.1}/{:.1}秒)", progress, current_time, duration_seconds);
                    last_progress = progress;
                }
            }
        }
    }
    
    println!("进度: 100% - 完成!");
    output_ctx.write_trailer()?;
    Ok(())
}

fn main() {
    match extract_audio_with_better_progress("input.mp4", "output.aac") {
        Ok(()) => println!("音频提取成功!"),
        Err(e) => eprintln!("错误: {}", e),
    }
}
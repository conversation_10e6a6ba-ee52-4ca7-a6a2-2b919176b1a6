use macros_test::show_streams;
use serde::{
  de::{Error, Visitor},
  Deserialize, Deserializer,
};
use serde_json::{json, Value};

pub struct CommandCallMeta<'a> {
    pub id: String,
    pub method: &'static str,
    pub key: &'static str,
    pub payload: &'a Option<Value>,
}

pub struct ParseError(pub Value);

pub trait CommandArg<'de> : Sized {
    fn from_command(command: CommandCallMeta<'de>) -> Result<Self, serde_json::Error>;
}

impl<'de, D : Deserialize<'de>> CommandArg<'de> for D {
    fn from_command(command: CommandCallMeta<'de>) -> Result<D, serde_json::Error> {
        Self::deserialize(command)
            // .map_err(|_e| ParseError(command.payload.unwrap_or(Value::Null)))
    }
}

impl<'de> Deserializer<'de> for CommandCallMeta<'de> {
    type Error = serde_json::Error;

    fn deserialize_any<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_any");
        todo!()
    }

    fn deserialize_bool<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_bool");
        todo!()
    }

    fn deserialize_i8<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_i8");
        todo!()
    }

    fn deserialize_i16<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_i16");
        todo!()
    }

    fn deserialize_i32<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_i32");
        todo!()
    }

    fn deserialize_i64<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_i64");
        todo!()
    }

    fn deserialize_u8<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_u8");
        todo!()
    }

    fn deserialize_u16<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_u16");
        todo!()
    }

    fn deserialize_u32<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_u32");
        todo!()
    }

    fn deserialize_u64<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_u64");
        todo!()
    }

    fn deserialize_f32<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_f32");
        todo!()
    }

    fn deserialize_f64<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_f64");
        todo!()
    }

    fn deserialize_char<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_char");
        todo!()
    }

    fn deserialize_str<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_str");
        todo!()
    }

    // fn deserialize_json(self) -> serde_json::Result<&'a serde_json::Value> {

    // }

    fn deserialize_string<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_string");

        if self.key.is_empty() {
            return Err(serde_json::Error::custom("key is empty"));
        }

        let result = match &self.payload {
            Some(v) => match v.get(self.key) {
                Some(value) => Ok(value),
                None => Err(serde_json::Error::custom("key not found")),
            },
            None => Err(serde_json::Error::custom("payload is empty")),
        };

        result?.deserialize_str(_visitor)
    }

    fn deserialize_bytes<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_bytes");
        todo!()
    }

    fn deserialize_byte_buf<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_byte_buf");
        todo!()
    }

    fn deserialize_option<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_option");
        todo!()
    }

    fn deserialize_unit<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_unit");
        todo!()
    }

    fn deserialize_unit_struct<V>(
        self,
        _name: &'static str,
        _visitor: V,
    ) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_unit_struct");
        todo!()
    }

    fn deserialize_newtype_struct<V>(
        self,
        _name: &'static str,
        _visitor: V,
    ) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_newtype_struct");
        todo!()
    }

    fn deserialize_seq<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_seq");
        todo!()
    }

    fn deserialize_tuple<V>(self, _len: usize, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_tuple");
        todo!()
    }

    fn deserialize_tuple_struct<V>(
        self,
        _name: &'static str,
        _len: usize,
        _visitor: V,
    ) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_tuple_struct");
        todo!()
    }

    fn deserialize_map<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_map");
        todo!()
    }

    fn deserialize_struct<V>(
        self,
        _name: &'static str,
        _fields: &'static [&'static str],
        _visitor: V,
    ) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_struct");
        todo!()
    }

    fn deserialize_enum<V>(
        self,
        _name: &'static str,
        _variants: &'static [&'static str],
        _visitor: V,
    ) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_enum");
        todo!()
    }

    fn deserialize_identifier<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_identifier");
        todo!()
    }

    fn deserialize_ignored_any<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de> {
        println!("deserialize_ignored_any");
        todo!()
    }
}


fn greet(s: String) {
    println!("Hello, {}!", s);
}

// #[show_streams]
// struct ABC {
//     name: String,
//     age: u32,
// }

// #[show_streams]
// pub async fn xx() {
//     let x = 1;
//     println!("jjj");
// }


fn main() -> Result<(), ()> {


    let command_meta = CommandCallMeta {
        id: "1".to_string(),
        key: "name",
        method: "greet",
        payload: &Some(json!({"name": "JJddd"})),
    };

    greet(
        match CommandArg::from_command(command_meta) {
            Ok(name) => name,
            Err(e) => {
                eprintln!("Failed to parse command: {}", e);
                return Err(());
            }
        }
    );

    Ok(())
}
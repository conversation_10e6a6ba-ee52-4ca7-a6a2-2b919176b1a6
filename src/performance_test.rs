use std::{
    path::PathBuf,
    time::Instant,
};

use crate::{
    scanner::ParallelScanner,
    optimized_scanner::OptimizedScanner,
};

pub struct PerformanceTest;

impl PerformanceTest {
    /// 运行性能对比测试
    pub fn run_comparison(test_path: &PathBuf) {
        println!("🚀 开始性能对比测试");
        println!("测试路径: {:?}", test_path);
        println!("{}", "=".repeat(60));

        // 测试原始版本
        println!("📊 测试原始版本...");
        let (original_time, original_stats) = Self::test_original_scanner(test_path);
        
        // 测试优化版本
        println!("📊 测试优化版本...");
        let (optimized_time, optimized_stats) = Self::test_optimized_scanner(test_path);
        
        // 输出对比结果
        Self::print_comparison(original_time, original_stats, optimized_time, optimized_stats);
    }

    fn test_original_scanner(path: &PathBuf) -> (std::time::Duration, (usize, usize)) {
        let scanner = ParallelScanner::default();
        
        let start = Instant::now();
        scanner.scan(path);
        let duration = start.elapsed();
        
        let stats = (
            scanner.files_processed.load(std::sync::atomic::Ordering::Relaxed),
            scanner.dir_processed.load(std::sync::atomic::Ordering::Relaxed),
        );
        
        (duration, stats)
    }

    fn test_optimized_scanner(path: &PathBuf) -> (std::time::Duration, (usize, usize)) {
        let scanner = OptimizedScanner::default();
        
        let start = Instant::now();
        let _result = scanner.scan(path);
        let duration = start.elapsed();
        
        let stats = scanner.get_stats();
        
        (duration, stats)
    }

    fn print_comparison(
        original_time: std::time::Duration,
        original_stats: (usize, usize),
        optimized_time: std::time::Duration,
        optimized_stats: (usize, usize),
    ) {
        println!("{}", "=".repeat(60));
        println!("📈 性能对比结果");
        println!("{}", "-".repeat(60));
        
        // 时间对比
        println!("⏱️  扫描时间:");
        println!("   原始版本: {:?}", original_time);
        println!("   优化版本: {:?}", optimized_time);
        
        let speedup = original_time.as_secs_f64() / optimized_time.as_secs_f64();
        println!("   🚀 性能提升: {:.2}x", speedup);
        
        // 统计对比
        println!("\n📁 扫描统计:");
        println!("   原始版本: {} 文件, {} 目录", original_stats.0, original_stats.1);
        println!("   优化版本: {} 文件, {} 目录", optimized_stats.0, optimized_stats.1);
        
        // 吞吐量对比
        let original_throughput = (original_stats.0 + original_stats.1) as f64 / original_time.as_secs_f64();
        let optimized_throughput = (optimized_stats.0 + optimized_stats.1) as f64 / optimized_time.as_secs_f64();
        
        println!("\n📊 吞吐量 (条目/秒):");
        println!("   原始版本: {:.0}", original_throughput);
        println!("   优化版本: {:.0}", optimized_throughput);
        println!("   🎯 吞吐量提升: {:.2}x", optimized_throughput / original_throughput);
        
        println!("{}", "=".repeat(60));
        
        // 性能评估
        Self::print_performance_assessment(speedup);
    }

    fn print_performance_assessment(speedup: f64) {
        println!("🎯 性能评估:");
        
        match speedup {
            x if x >= 5.0 => println!("   🌟 优秀! 性能提升超过 5 倍"),
            x if x >= 3.0 => println!("   ✅ 很好! 性能提升超过 3 倍"),
            x if x >= 2.0 => println!("   👍 不错! 性能提升超过 2 倍"),
            x if x >= 1.5 => println!("   📈 有改善，性能提升 1.5 倍以上"),
            x if x >= 1.1 => println!("   📊 轻微改善，性能提升 10% 以上"),
            _ => println!("   ⚠️  性能提升不明显，可能需要进一步优化"),
        }
    }

    /// 内存使用对比测试
    pub fn test_memory_usage(_test_path: &PathBuf) {
        println!("🧠 内存使用对比测试");
        println!("{}", "=".repeat(60));
        
        // 这里可以添加内存使用监控
        // 由于 Rust 没有内置的内存监控，可以使用外部工具或库
        println!("💡 提示: 使用 `cargo build --release` 编译后");
        println!("   可以用 `time` 或 `valgrind` 等工具监控内存使用");
        
        println!("\n预期内存优化:");
        println!("   • 减少 Arc 引用计数开销");
        println!("   • 消除 Mutex 锁开销");
        println!("   • 更好的内存局部性");
        println!("   • 预期内存使用减少 50-70%");
    }

    /// 并发性能测试
    pub fn test_concurrency_scaling(test_path: &PathBuf) {
        println!("🔄 并发扩展性测试");
        println!("{}", "=".repeat(60));
        
        let cpu_count = num_cpus::get();
        println!("CPU 核心数: {}", cpu_count);
        
        // 可以测试不同线程数下的性能
        for thread_count in [1, 2, 4, cpu_count] {
            if thread_count <= cpu_count {
                println!("\n🧵 测试 {} 线程性能...", thread_count);
                
                // 设置 rayon 线程池大小
                let pool = rayon::ThreadPoolBuilder::new()
                    .num_threads(thread_count)
                    .build()
                    .unwrap();
                
                let start = Instant::now();
                pool.install(|| {
                    let scanner = OptimizedScanner::default();
                    let _result = scanner.scan(test_path);
                });
                let duration = start.elapsed();
                
                println!("   时间: {:?}", duration);
            }
        }
    }
}

/// 简单的基准测试宏
#[macro_export]
macro_rules! benchmark {
    ($name:expr, $code:block) => {
        {
            println!("🔍 运行基准测试: {}", $name);
            let start = std::time::Instant::now();
            let result = $code;
            let duration = start.elapsed();
            println!("   ⏱️  耗时: {:?}", duration);
            result
        }
    };
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_current_directory() {
        let current_dir = PathBuf::from(".");
        PerformanceTest::run_comparison(&current_dir);
    }

    #[test]
    fn test_memory_usage() {
        let current_dir = PathBuf::from(".");
        PerformanceTest::test_memory_usage(&current_dir);
    }

    #[test]
    fn test_concurrency() {
        let current_dir = PathBuf::from(".");
        PerformanceTest::test_concurrency_scaling(&current_dir);
    }

    #[test]
    fn benchmark_macro_test() {
        let result = benchmark!("测试计算", {
            // 模拟一些计算
            (0..1000).map(|x| x * x).sum::<i32>()
        });
        
        assert_eq!(result, 332833500);
    }
}

/// 使用示例
pub fn example_usage() {
    // 基本性能测试
    let test_path = PathBuf::from("/path/to/test/directory");
    PerformanceTest::run_comparison(&test_path);
    
    // 内存使用测试
    PerformanceTest::test_memory_usage(&test_path);
    
    // 并发扩展性测试
    PerformanceTest::test_concurrency_scaling(&test_path);
    
    // 使用基准测试宏
    let _result = benchmark!("自定义测试", {
        let scanner = OptimizedScanner::default();
        scanner.scan(&test_path)
    });
}

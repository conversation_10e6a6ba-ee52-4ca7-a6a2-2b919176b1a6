use serde::{Deserialize, Serialize};


#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct SettingsItem {
    pub id: String,
    pub name: String,
    #[serde(rename = "type")]
    pub item_type: String,
    pub parent_id: Option<String>,
    pub description: Option<String>,
    pub value: Option<serde_json::Value>,
    pub default_value: Option<serde_json::Value>,
    pub placeholder: Option<String>,
    pub options: Option<Vec<SettingsOption>>,
    pub client_side: Option<bool>,
    pub disabled: Option<bool>,
    pub order: i32,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SettingsOption {
    pub name: String,
    pub value: String,
}


#[derive(Debug, Default, <PERSON>lone, Serialize, Deserialize)]
pub struct KeyBinding {
    #[serde(rename = "altKey")]
    pub alt_key: bool,
    #[serde(rename = "ctrl<PERSON>ey")]
    pub ctrl_key: bool,
    #[serde(rename = "shift<PERSON><PERSON>")]
    pub shift_key: bool,
    #[serde(rename = "meta<PERSON><PERSON>")]
    pub meta_key: bool,
    pub key: String,
    pub code: String
}

impl KeyBinding {
    pub fn to_string(&self) -> String {
        let mut s = String::new();
        if self.meta_key {
            s += "Super+"
        }
        if self.ctrl_key {
            s += "Ctrl+"
        }
        if self.alt_key {
            s += "Alt+"
        }
        if self.shift_key {
            s += "Shift+"
        }
        s += &self.code;
        return s;
    }
}
